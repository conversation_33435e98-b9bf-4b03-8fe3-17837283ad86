/**
 * @fileoverview District Service for MANEB Results Management System
 * @description Service for fetching districts from the API
 */

import apiClient from '../core/api-client';

/**
 * District interface matching the API response
 */
export interface DistrictDto {
  id: string;
  name: string;
  geographicalDistrictId: string;
  createdOn: string;
  createdBy: string;
  modifiedOn: string | null;
  modifiedBy: string | null;
  demuserId: string;
  emisdistrictCode: string | null;
  candidates: any[];
  geographicalDistrict: any | null;
}

/**
 * API Response interface for paginated districts
 */
export interface DistrictResponse {
  totalCount: number;
  pageNumber: number;
  pageSize: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
  items: DistrictDto[];
}

/**
 * Simplified district option for dropdowns
 */
export interface DistrictOption {
  id: string;
  name: string;
  code: string;
}

/**
 * District Service
 * @description Handles all district related API operations
 */
export class DistrictService {
  private static instance: DistrictService;
  private cachedDistricts: DistrictDto[] | null = null;
  private lastFetchTime: number = 0;
  private readonly CACHE_DURATION = 10 * 60 * 1000; // 10 minutes

  private constructor() {}

  /**
   * Get singleton instance
   */
  static getInstance(): DistrictService {
    if (!DistrictService.instance) {
      DistrictService.instance = new DistrictService();
    }
    return DistrictService.instance;
  }

  /**
   * Fetch districts from API
   */
  async fetchDistricts(forceRefresh: boolean = false): Promise<DistrictDto[]> {
    const now = Date.now();

    // Return cached data if available and not expired
    if (!forceRefresh && this.cachedDistricts && (now - this.lastFetchTime) < this.CACHE_DURATION) {
      return this.cachedDistricts;
    }

    try {
      const response: DistrictResponse = await apiClient.get('/api/District');
      this.cachedDistricts = response.items;
      this.lastFetchTime = now;
      return this.cachedDistricts;
    } catch (error) {
      console.error('Error fetching districts:', error);

      // Return cached data if available, even if expired
      if (this.cachedDistricts) {
        console.warn('Using cached districts due to API error');
        return this.cachedDistricts;
      }

      throw error;
    }
  }

  /**
   * Fetch districts with pagination parameters
   */
  async fetchDistrictsWithPagination(pageSize: number = 40, pageNumber: number = 1, forceRefresh: boolean = false): Promise<DistrictDto[]> {
    const now = Date.now();
    const cacheKey = `districts_${pageSize}_${pageNumber}`;

    // Return cached data if available and not expired
    if (!forceRefresh && this.cachedDistricts && (now - this.lastFetchTime) < this.CACHE_DURATION) {
      return this.cachedDistricts;
    }

    try {
      const params = new URLSearchParams();
      params.append('pageSize', pageSize.toString());
      params.append('pageNumber', pageNumber.toString());

      const response: DistrictResponse = await apiClient.get(`/api/District?${params.toString()}`);
      this.cachedDistricts = response.items;
      this.lastFetchTime = now;

      console.log(`✅ Districts fetched with pagination: ${response.items.length} items (page ${pageNumber}, size ${pageSize})`);
      return this.cachedDistricts;
    } catch (error) {
      console.error('Error fetching districts with pagination:', error);

      // Return cached data if available, even if expired
      if (this.cachedDistricts) {
        console.warn('Using cached districts due to API error');
        return this.cachedDistricts;
      }

      throw error;
    }
  }

  /**
   * Get districts as dropdown options
   */
  async getDistrictOptions(): Promise<DistrictOption[]> {
    const districts = await this.fetchDistricts();
    
    return districts.map(district => ({
      id: district.id,
      name: district.name,
      code: district.geographicalDistrictId || district.id
    }));
  }

  /**
   * Get district by ID
   */
  async getDistrictById(id: string): Promise<DistrictDto | undefined> {
    const districts = await this.fetchDistricts();
    return districts.find(district => district.id === id);
  }

  /**
   * Get district name by ID
   */
  async getDistrictName(id: string): Promise<string> {
    const district = await this.getDistrictById(id);
    return district?.name || id;
  }

  /**
   * Search districts by name
   */
  async searchDistricts(query: string): Promise<DistrictDto[]> {
    const districts = await this.fetchDistricts();
    const lowerQuery = query.toLowerCase();
    
    return districts.filter(district => 
      district.name.toLowerCase().includes(lowerQuery) ||
      district.geographicalDistrictId.toLowerCase().includes(lowerQuery)
    );
  }

  /**
   * Clear cache (useful for testing or forced refresh)
   */
  clearCache(): void {
    this.cachedDistricts = null;
    this.lastFetchTime = 0;
  }

  /**
   * Get districts formatted for filter options
   */
  async getFilterOptions(parentId?: string): Promise<Array<{id: string, name: string, parentId?: string}>> {
    const districts = await this.fetchDistricts();
    
    return districts.map(district => ({
      id: district.id,
      name: district.name,
      parentId
    }));
  }
}

// Export singleton instance
export const districtService = DistrictService.getInstance();

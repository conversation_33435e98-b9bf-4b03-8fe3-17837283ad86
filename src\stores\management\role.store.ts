import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import { roleService, rolePermissionService } from '@/services';
import type {
  RoleDto,
  CreateRoleRequest,
  UpdateRoleRequest,
  RoleFilterDto,
  RolePermissionDto,
  CreateRolePermissionRequest,
  UpdateRolePermissionRequest,
  RecordStatus
} from '@/interfaces';

export const useRoleStore = defineStore('role', () => {
  // State
  const roles = ref<RoleDto[]>([]);
  const currentRole = ref<RoleDto | null>(null);
  const rolePermissions = ref<RolePermissionDto[]>([]);
  const currentRolePermission = ref<RolePermissionDto | null>(null);
  const isLoading = ref(false);
  const error = ref<string | null>(null);
  const searchQuery = ref('');
  const filters = ref<RoleFilterDto>({
    searchQuery: '',
    status: 'All',
    hasPermissions: 'All'
  });
  const pagination = ref({
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 0
  });

  // Getters
  const filteredRoles = computed(() => {
    let filtered = roles.value;

    if (searchQuery.value) {
      const query = searchQuery.value.toLowerCase();
      filtered = filtered.filter(role => 
        role.name?.toLowerCase().includes(query) ||
        role.description?.toLowerCase().includes(query)
      );
    }

    if (filters.value.status !== 'All') {
      filtered = filtered.filter(role => role.status === filters.value.status);
    }

    if (filters.value.hasPermissions !== 'All') {
      filtered = filtered.filter(role => {
        const hasPerms = role.rolePermissions && role.rolePermissions.length > 0;
        return filters.value.hasPermissions ? hasPerms : !hasPerms;
      });
    }

    return filtered;
  });

  const rolesByStatus = computed(() => {
    return {
      approved: roles.value.filter(r => r.status === 'Approved').length,
      secondApproved: roles.value.filter(r => r.status === 'SecondApproved').length,
      unapproved: roles.value.filter(r => r.status === 'Unapproved').length,
      rejected: roles.value.filter(r => r.status === 'Rejected').length,
      withPermissions: roles.value.filter(r => r.rolePermissions && r.rolePermissions.length > 0).length,
      withoutPermissions: roles.value.filter(r => !r.rolePermissions || r.rolePermissions.length === 0).length,
    };
  });

  const totalRoles = computed(() => roles.value.length);
  const approvedRoles = computed(() => roles.value.filter(r => r.status === 'Approved' || r.status === 'SecondApproved').length);

  // Actions
  const fetchRoles = async (): Promise<void> => {
    try {
      isLoading.value = true;
      error.value = null;
      roles.value = await roleService.getAllRoles();
    } catch (err: any) {
      error.value = err.message || 'Failed to fetch roles';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const fetchRolesPaginated = async (page: number = 1, limit: number = 10): Promise<void> => {
    try {
      isLoading.value = true;
      error.value = null;
      
      const response = await roleService.getRolesPaginated(page, limit);
      roles.value = response.roles;
      pagination.value = {
        page: response.page,
        limit,
        total: response.total,
        totalPages: response.totalPages
      };
    } catch (err: any) {
      error.value = err.message || 'Failed to fetch roles';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const fetchRoleById = async (id: string): Promise<RoleDto> => {
    try {
      isLoading.value = true;
      error.value = null;
      const role = await roleService.getRoleById(id);
      currentRole.value = role;
      return role;
    } catch (err: any) {
      error.value = err.message || 'Failed to fetch role';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const createRole = async (roleData: CreateRoleRequest): Promise<RoleDto> => {
    try {
      isLoading.value = true;
      error.value = null;
      const newRole = await roleService.createRole(roleData);
      roles.value.push(newRole);
      return newRole;
    } catch (err: any) {
      error.value = err.message || 'Failed to create role';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const createRoleWithPermissions = async (
    roleData: CreateRoleRequest,
    permissions: Array<{ sectionId: string; action: PermissionAction; canAccess: boolean }>
  ): Promise<RoleDto> => {
    try {
      isLoading.value = true;
      error.value = null;

      // First create the role
      const newRole = await roleService.createRole(roleData);

      // Then create permissions for the role using bulk creation
      if (permissions.length > 0 && newRole.id) {
        try {
          // Prepare permissions for bulk creation
          const permissionRequests = permissions.map(permission => ({
            roleId: newRole.id!,
            sectionID: permission.sectionId,
            action: permission.action,
            canAccess: permission.canAccess,
            status: 'Approved' as const
          }));

          console.log(`Creating ${permissionRequests.length} permissions for role ${newRole.name}:`, permissionRequests);

          // Use bulk assignment method
          await rolePermissionService.bulkAssignPermissionsToRole(newRole.id!, permissionRequests);

          console.log('Successfully created all permissions for role');

          // Fetch the role again to get the permissions
          const roleWithPermissions = await roleService.getRoleById(newRole.id);
          roles.value.push(roleWithPermissions);
          return roleWithPermissions;
        } catch (permError) {
          console.error('Failed to create permissions for role:', permError);
          // Still add the role even if permissions failed
          roles.value.push(newRole);
          throw new Error(`Role created but failed to assign permissions: ${permError.message || 'Unknown error'}`);
        }
      } else {
        roles.value.push(newRole);
        return newRole;
      }
    } catch (err: any) {
      error.value = err.message || 'Failed to create role with permissions';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const updateRole = async (id: string, roleData: UpdateRoleRequest): Promise<RoleDto> => {
    try {
      isLoading.value = true;
      error.value = null;
      const updatedRole = await roleService.updateRole(id, roleData);
      
      const index = roles.value.findIndex(r => r.id === id);
      if (index !== -1) {
        roles.value[index] = updatedRole;
      }
      
      if (currentRole.value?.id === id) {
        currentRole.value = updatedRole;
      }
      
      return updatedRole;
    } catch (err: any) {
      error.value = err.message || 'Failed to update role';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const deleteRole = async (id: string): Promise<void> => {
    try {
      isLoading.value = true;
      error.value = null;
      await roleService.deleteRole(id);
      
      roles.value = roles.value.filter(r => r.id !== id);
      
      if (currentRole.value?.id === id) {
        currentRole.value = null;
      }
    } catch (err: any) {
      error.value = err.message || 'Failed to delete role';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const getRolePermissions = async (roleId: string): Promise<RolePermissionDto[]> => {
    try {
      return await rolePermissionService.getRolePermissionsByRoleId(roleId);
    } catch (err: any) {
      error.value = err.message || 'Failed to fetch role permissions';
      throw err;
    }
  };

  // Role Permission Actions
  const fetchRolePermissions = async (): Promise<void> => {
    try {
      isLoading.value = true;
      error.value = null;
      rolePermissions.value = await rolePermissionService.getRolePermissions();
    } catch (err: any) {
      error.value = err.message || 'Failed to fetch role permissions';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const fetchRolePermissionById = async (id: string): Promise<RolePermissionDto> => {
    try {
      isLoading.value = true;
      error.value = null;
      const rolePermission = await rolePermissionService.getRolePermissionById(id);
      currentRolePermission.value = rolePermission;
      return rolePermission;
    } catch (err: any) {
      error.value = err.message || 'Failed to fetch role permission';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const createRolePermission = async (data: CreateRolePermissionRequest): Promise<RolePermissionDto> => {
    try {
      isLoading.value = true;
      error.value = null;
      const newRolePermission = await rolePermissionService.createRolePermission(data);
      rolePermissions.value.push(newRolePermission);
      return newRolePermission;
    } catch (err: any) {
      error.value = err.message || 'Failed to create role permission';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const updateRolePermission = async (id: string, data: UpdateRolePermissionRequest): Promise<RolePermissionDto> => {
    try {
      isLoading.value = true;
      error.value = null;
      const updatedRolePermission = await rolePermissionService.updateRolePermission(id, data);

      const index = rolePermissions.value.findIndex(rp => rp.id === id);
      if (index !== -1) {
        rolePermissions.value[index] = updatedRolePermission;
      }

      if (currentRolePermission.value?.id === id) {
        currentRolePermission.value = updatedRolePermission;
      }

      return updatedRolePermission;
    } catch (err: any) {
      error.value = err.message || 'Failed to update role permission';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const deleteRolePermission = async (id: string): Promise<void> => {
    try {
      isLoading.value = true;
      error.value = null;
      await rolePermissionService.deleteRolePermission(id);

      const index = rolePermissions.value.findIndex(rp => rp.id === id);
      if (index !== -1) {
        rolePermissions.value.splice(index, 1);
      }

      if (currentRolePermission.value?.id === id) {
        currentRolePermission.value = null;
      }
    } catch (err: any) {
      error.value = err.message || 'Failed to delete role permission';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const searchRoles = async (query: string): Promise<void> => {
    searchQuery.value = query;
    if (query) {
      try {
        isLoading.value = true;
        error.value = null;
        roles.value = await roleService.searchRoles(query);
      } catch (err: any) {
        error.value = err.message || 'Failed to search roles';
        throw err;
      } finally {
        isLoading.value = false;
      }
    } else {
      await fetchRoles();
    }
  };

  const filterRoles = async (filterOptions: RoleFilterDto): Promise<void> => {
    filters.value = { ...filterOptions };
    try {
      isLoading.value = true;
      error.value = null;
      roles.value = await roleService.filterRoles(filterOptions);
    } catch (err: any) {
      error.value = err.message || 'Failed to filter roles';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const updateRoleStatus = async (id: string, status: RecordStatus): Promise<RoleDto> => {
    try {
      isLoading.value = true;
      error.value = null;
      const updatedRole = await updateRole(id, { status });

      const index = roles.value.findIndex(r => r.id === id);
      if (index !== -1) {
        roles.value[index] = updatedRole;
      }

      if (currentRole.value?.id === id) {
        currentRole.value = updatedRole;
      }

      return updatedRole;
    } catch (err: any) {
      error.value = err.message || 'Failed to update role status';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const updateRoleWithPermissions = async (
    roleId: string,
    permissions: Array<{ sectionId: string; action: PermissionAction; canAccess: boolean }>
  ): Promise<RoleDto> => {
    try {
      isLoading.value = true;
      error.value = null;

      // First, get the current role
      const currentRole = await roleService.getRoleById(roleId);

      // Delete existing permissions for this role
      if (currentRole.rolePermissions) {
        for (const permission of currentRole.rolePermissions) {
          if (permission.id) {
            try {
              await rolePermissionService.deleteRolePermission(permission.id);
            } catch (deleteError) {
              console.warn('Failed to delete existing permission:', permission.id, deleteError);
            }
          }
        }
      }

      // Create new permissions
      if (permissions.length > 0) {
        const permissionRequests = permissions.map(permission => ({
          roleId: roleId,
          sectionID: permission.sectionId,
          action: permission.action,
          canAccess: permission.canAccess,
          status: 'Approved' as const
        }));

        console.log(`Updating role ${roleId} with ${permissionRequests.length} permissions`);

        await rolePermissionService.bulkAssignPermissionsToRole(roleId, permissionRequests);
      }

      // Fetch the updated role with new permissions
      const updatedRole = await roleService.getRoleById(roleId);

      // Update the role in the store
      const index = roles.value.findIndex(r => r.id === roleId);
      if (index !== -1) {
        roles.value[index] = updatedRole;
      }

      if (currentRole.value?.id === roleId) {
        currentRole.value = updatedRole;
      }

      return updatedRole;
    } catch (err: any) {
      error.value = err.message || 'Failed to update role permissions';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const clearError = (): void => {
    error.value = null;
  };

  const clearCurrentRole = (): void => {
    currentRole.value = null;
  };

  const resetFilters = (): void => {
    filters.value = {
      searchQuery: '',
      status: 'All',
      hasPermissions: 'All'
    };
    searchQuery.value = '';
  };

  return {
    // State
    roles,
    currentRole,
    rolePermissions,
    currentRolePermission,
    isLoading,
    error,
    searchQuery,
    filters,
    pagination,

    // Getters
    filteredRoles,
    rolesByStatus,
    totalRoles,
    approvedRoles,

    // Role Actions
    fetchRoles,
    fetchRolesPaginated,
    fetchRoleById,
    createRole,
    createRoleWithPermissions,
    updateRole,
    updateRoleWithPermissions,
    deleteRole,
    getRolePermissions,
    searchRoles,
    filterRoles,
    updateRoleStatus,
    clearError,
    clearCurrentRole,
    resetFilters,

    // Role Permission Actions
    fetchRolePermissions,
    fetchRolePermissionById,
    createRolePermission,
    updateRolePermission,
    deleteRolePermission,
  };
});

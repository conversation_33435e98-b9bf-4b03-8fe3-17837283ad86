/**
 * @fileoverview School Service for MANEB Results Management System
 * @description Service for fetching schools from the API
 */

import apiClient from '../core/api-client';

/**
 * School interface matching the API response
 */
export interface SchoolDto {
  id: string;
  fullname: string;
  shortName: string;
  examDistrictPartitionId: string;
  postalAddress: string;
  geoLocationLatitude: number | null;
  geoLocationLongitude: number | null;
  phoneNumber: string | null;
  emailAddress: string | null;
  website: string | null;
  logoFlp: string | null;
  createdOn: string;
  createdBy: string;
  modifiedOn: string | null;
  modifiedBy: string | null;
  headTeacherUserId: string | null;
  contactPerson: string | null;
  headTeacherSignatureFlp: string | null;
  isActive: boolean;
  centreTypeId: string | null;
  centreNumbers: any[];
  centreType: any | null;
}

/**
 * API Response interface for paginated schools
 */
export interface SchoolResponse {
  totalCount: number;
  pageNumber: number;
  pageSize: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
  items: SchoolDto[];
}

/**
 * Simplified school option for dropdowns
 */
export interface SchoolOption {
  id: string;
  name: string;
  shortName: string;
  districtId: string;
  contactPerson: string | null;
  phoneNumber: string | null;
  isActive: boolean;
}

/**
 * School search filters
 */
export interface SchoolFilters {
  districtId?: string;
  search?: string;
  activeOnly?: boolean;
}

/**
 * School Service
 * @description Handles all school related API operations
 */
export class SchoolService {
  private static instance: SchoolService;
  private cachedSchools: SchoolDto[] | null = null;
  private lastFetchTime: number = 0;
  private readonly CACHE_DURATION = 15 * 60 * 1000; // 15 minutes

  private constructor() {}

  /**
   * Get singleton instance
   */
  static getInstance(): SchoolService {
    if (!SchoolService.instance) {
      SchoolService.instance = new SchoolService();
    }
    return SchoolService.instance;
  }

  /**
   * Fetch schools from API with optional filtering
   */
  async fetchSchools(options: {
    forceRefresh?: boolean;
    pageNumber?: number;
    pageSize?: number;
    search?: string;
  } = {}): Promise<SchoolDto[]> {
    const { forceRefresh = false, pageNumber, pageSize, search } = options;
    const now = Date.now();

    // Create cache key based on parameters
    const cacheKey = `schools_${pageNumber || 'all'}_${pageSize || 'default'}_${search || 'none'}`;

    // Return cached data if available and not expired (only for non-parameterized requests)
    if (!forceRefresh && !pageNumber && !pageSize && !search &&
        this.cachedSchools && (now - this.lastFetchTime) < this.CACHE_DURATION) {
      return this.cachedSchools;
    }

    try {
      // Build query parameters
      const params = new URLSearchParams();
      if (pageNumber) params.append('PageNumber', pageNumber.toString());
      if (pageSize) params.append('PageSize', pageSize.toString());
      if (search) params.append('Search', search);

      const url = params.toString() ? `/api/school?${params.toString()}` : '/api/school';
      const response: SchoolResponse = await apiClient.get(url);

      // Cache only if it's a general request (no specific filters)
      if (!pageNumber && !pageSize && !search) {
        this.cachedSchools = response.items;
        this.lastFetchTime = now;
      }

      return response.items;
    } catch (error) {
      console.error('Error fetching schools:', error);

      // Return cached data if available, even if expired (only for general requests)
      if (!pageNumber && !pageSize && !search && this.cachedSchools) {
        console.warn('Using cached schools due to API error');
        return this.cachedSchools;
      }

      throw error;
    }
  }

  /**
   * Get active schools only
   */
  async getActiveSchools(): Promise<SchoolDto[]> {
    const schools = await this.fetchSchools();
    return schools.filter(school => school.isActive);
  }

  /**
   * Get schools as dropdown options
   */
  async getSchoolOptions(activeOnly: boolean = true): Promise<SchoolOption[]> {
    const schools = activeOnly ? await this.getActiveSchools() : await this.fetchSchools();
    
    return schools.map(school => ({
      id: school.id,
      name: school.fullname,
      shortName: school.shortName,
      districtId: school.examDistrictPartitionId,
      contactPerson: school.contactPerson,
      phoneNumber: school.phoneNumber,
      isActive: school.isActive
    }));
  }

  /**
   * Get schools filtered by criteria
   */
  async getFilteredSchools(filters: SchoolFilters): Promise<SchoolDto[]> {
    const schools = filters.activeOnly !== false ? await this.getActiveSchools() : await this.fetchSchools();
    
    return schools.filter(school => {
      if (filters.districtId && school.examDistrictPartitionId !== filters.districtId) {
        return false;
      }
      
      if (filters.search) {
        const searchLower = filters.search.toLowerCase();
        return (
          school.fullname.toLowerCase().includes(searchLower) ||
          school.shortName.toLowerCase().includes(searchLower) ||
          school.id.toLowerCase().includes(searchLower) ||
          (school.contactPerson && school.contactPerson.toLowerCase().includes(searchLower))
        );
      }
      
      return true;
    });
  }

  /**
   * Search schools by query
   */
  async searchSchools(query: string): Promise<SchoolDto[]> {
    return this.getFilteredSchools({ search: query });
  }

  /**
   * Get schools by district
   */
  async getSchoolsByDistrict(districtId: string): Promise<SchoolDto[]> {
    return this.getFilteredSchools({ districtId });
  }

  /**
   * Get school by ID
   */
  async getSchoolById(id: string): Promise<SchoolDto | undefined> {
    const schools = await this.fetchSchools();
    return schools.find(school => school.id === id);
  }

  /**
   * Get school name by ID
   */
  async getSchoolName(id: string): Promise<string> {
    const school = await this.getSchoolById(id);
    return school?.fullname || id;
  }

  /**
   * Get unique districts from schools
   */
  async getDistricts(): Promise<string[]> {
    const schools = await this.getActiveSchools();
    const districts = [...new Set(schools.map(school => school.examDistrictPartitionId))];
    return districts.sort();
  }

  /**
   * Clear cache (useful for testing or forced refresh)
   */
  clearCache(): void {
    this.cachedSchools = null;
    this.lastFetchTime = 0;
  }
}

// Export singleton instance
export const schoolService = SchoolService.getInstance();

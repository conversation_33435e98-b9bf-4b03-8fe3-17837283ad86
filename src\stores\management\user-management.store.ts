/**
 * @fileoverview User Management Store for MANEB Results Management System
 * @description Pinia store for managing user state with full CRUD operations
 */

import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import { userManagementService, userRolesService } from '@/services';
import type { 
  UserDto, 
  CreateUserRequest, 
  UpdateUserRequest,
  UserFilter,
  RecordStatus,
  AssignRoleDto
} from '@/interfaces';

export const useUserManagementStore = defineStore('userManagement', () => {
  // State
  const users = ref<UserDto[]>([]);
  const currentUser = ref<UserDto | null>(null);
  const isLoading = ref(false);
  const error = ref<string | null>(null);
  const searchQuery = ref('');
  const filters = ref<UserFilter>({
    searchQuery: '',
    roleId: 'All',
    status: 'All',
    gender: 'All',
    isActive: 'All'
  });
  const pagination = ref({
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 0
  });

  // Getters
  const filteredUsers = computed(() => {
    let result = users.value;

    if (searchQuery.value) {
      const query = searchQuery.value.toLowerCase();
      result = result.filter(user =>
        user.firstName?.toLowerCase().includes(query) ||
        user.lastName?.toLowerCase().includes(query) ||
        user.email?.toLowerCase().includes(query) ||
        user.userName?.toLowerCase().includes(query)
      );
    }

    if (filters.value.roleId && filters.value.roleId !== 'All') {
      result = result.filter(user =>
        user.roles?.some(role => role.id === filters.value.roleId)
      );
    }

    if (filters.value.status && filters.value.status !== 'All') {
      result = result.filter(user => user.status === filters.value.status);
    }

    if (filters.value.gender && filters.value.gender !== 'All') {
      result = result.filter(user => user.gender === filters.value.gender);
    }

    return result;
  });

  const usersByStatus = computed(() => {
    return (status: RecordStatus) => users.value.filter(user => user.status === status);
  });

  const totalUsers = computed(() => users.value.length);

  const activeUsers = computed(() => 
    users.value.filter(user => user.status === 'Approved' || user.status === 'SecondApproved')
  );

  // Actions
  const fetchUsers = async (): Promise<void> => {
    try {
      isLoading.value = true;
      error.value = null;
      users.value = await userManagementService.getAllUsers();
    } catch (err: any) {
      error.value = err.message || 'Failed to fetch users';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const fetchUsersPaginated = async (page: number = 1, limit: number = 10): Promise<void> => {
    try {
      isLoading.value = true;
      error.value = null;
      
      const response = await userManagementService.getUsersPaginated(page, limit, {
        Search: searchQuery.value || undefined,
        RoleId: filters.value.roleId !== 'All' ? filters.value.roleId : undefined,
        Gender: filters.value.gender !== 'All' ? filters.value.gender as any : undefined,
        status: filters.value.status !== 'All' ? filters.value.status as RecordStatus : undefined
      });
      
      users.value = response;
      pagination.value = {
        page,
        limit,
        total: response.length,
        totalPages: Math.ceil(response.length / limit)
      };
    } catch (err: any) {
      error.value = err.message || 'Failed to fetch users';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const fetchUserById = async (id: string): Promise<UserDto> => {
    try {
      isLoading.value = true;
      error.value = null;
      const user = await userManagementService.getUserById(id);
      currentUser.value = user;
      return user;
    } catch (err: any) {
      error.value = err.message || 'Failed to fetch user';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const createUser = async (userData: CreateUserRequest): Promise<UserDto> => {
    try {
      isLoading.value = true;
      error.value = null;
      const newUser = await userManagementService.createUser(userData);
      users.value.push(newUser);
      
      // Assign roles if provided
      if (userData.roleIds && userData.roleIds.length > 0) {
        await assignUserRoles(newUser.id!, userData.roleIds);
      }
      
      return newUser;
    } catch (err: any) {
      error.value = err.message || 'Failed to create user';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const updateUser = async (id: string, userData: UpdateUserRequest): Promise<void> => {
    try {
      isLoading.value = true;
      error.value = null;
      await userManagementService.updateUser(id, userData);
      
      // Update local state
      const index = users.value.findIndex(user => user.id === id);
      if (index !== -1) {
        users.value[index] = { ...users.value[index], ...userData };
      }
      
      if (currentUser.value?.id === id) {
        currentUser.value = { ...currentUser.value, ...userData };
      }
    } catch (err: any) {
      error.value = err.message || 'Failed to update user';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const deleteUser = async (id: string): Promise<void> => {
    try {
      isLoading.value = true;
      error.value = null;
      await userManagementService.deleteUser(id);
      
      // Remove from local state
      users.value = users.value.filter(user => user.id !== id);
      if (currentUser.value?.id === id) {
        currentUser.value = null;
      }
    } catch (err: any) {
      error.value = err.message || 'Failed to delete user';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const approveUser = async (id: string): Promise<void> => {
    try {
      isLoading.value = true;
      error.value = null;
      await userManagementService.approveUser(id);
      
      // Update local state
      const index = users.value.findIndex(user => user.id === id);
      if (index !== -1) {
        users.value[index].status = 'Approved';
      }
    } catch (err: any) {
      error.value = err.message || 'Failed to approve user';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const assignUserRoles = async (userId: string, roleIds: string[]): Promise<void> => {
    try {
      isLoading.value = true;
      error.value = null;
      await userRolesService.bulkAssignRoles(userId, roleIds);
      
      // Refresh user data to get updated roles
      if (currentUser.value?.id === userId) {
        await fetchUserById(userId);
      }
    } catch (err: any) {
      error.value = err.message || 'Failed to assign roles';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const removeUserRoles = async (userId: string, roleIds: string[]): Promise<void> => {
    try {
      isLoading.value = true;
      error.value = null;
      await userRolesService.bulkRemoveRoles(userId, roleIds);
      
      // Refresh user data to get updated roles
      if (currentUser.value?.id === userId) {
        await fetchUserById(userId);
      }
    } catch (err: any) {
      error.value = err.message || 'Failed to remove roles';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const searchUsers = async (query: string): Promise<void> => {
    searchQuery.value = query;
    await fetchUsersPaginated(1, pagination.value.limit);
  };

  const filterUsers = async (newFilters: Partial<UserFilter>): Promise<void> => {
    filters.value = { ...filters.value, ...newFilters };
    await fetchUsersPaginated(1, pagination.value.limit);
  };

  const clearError = (): void => {
    error.value = null;
  };

  const clearCurrentUser = (): void => {
    currentUser.value = null;
  };

  const resetFilters = (): void => {
    filters.value = {
      searchQuery: '',
      roleId: 'All',
      status: 'All',
      gender: 'All',
      isActive: 'All'
    };
    searchQuery.value = '';
  };

  return {
    // State
    users,
    currentUser,
    isLoading,
    error,
    searchQuery,
    filters,
    pagination,
    
    // Getters
    filteredUsers,
    usersByStatus,
    totalUsers,
    activeUsers,
    
    // Actions
    fetchUsers,
    fetchUsersPaginated,
    fetchUserById,
    createUser,
    updateUser,
    deleteUser,
    approveUser,
    assignUserRoles,
    removeUserRoles,
    searchUsers,
    filterUsers,
    clearError,
    clearCurrentUser,
    resetFilters,
  };
});

<script setup lang="ts">
import BreadcrumbsActions from '../sections/breadcrumbs_actions.vue'
import CardOne from '@/components/shared/UI/cards/card-one.vue';
import TransactionCard from '@/components/shared/UI/cards/transaction-card.vue';
import InvoicesCard from '@/components/shared/UI/cards/invoices-card.vue';
import ProjectsCard from '@/components/shared/UI/cards/projects-card.vue';
import Footer from '@/components/shared/UI/footers/footer-one.vue';

</script>

<template>
      <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <!-- <BreadcrumbsActions /> -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <CardOne
            title="Members"
            value="53"
            percentage="+55%"
            percentageColorClass="text-green-500"
            iconBgColorClass="bg-red-50"
            iconSvgPath="M12 6a3 3 0 11-6 0 3 3 0 016 0zM12 14a3 3 0 00-3-3H5a3 3 0 003 3h6z"
          />
          <CardOne
            title="Programs"
            value="53"
            percentage="+55%"
            percentageColorClass="text-green-500"
            iconBgColorClass="bg-red-50"
            iconSvgPath="M12 6a3 3 0 11-6 0 3 3 0 016 0zM12 14a3 3 0 00-3-3H5a3 3 0 003 3h6z"
          />
          <CardOne
            title="Memberships"
            value="53"
            percentage="+55%"
            percentageColorClass="text-green-500"
            iconBgColorClass="bg-red-50"
            iconSvgPath="M15 12a3 3 0 11-6 0 3 3 0 016 0zM12 9a3 3 0 00-3 3h6a3 3 0 00-3-3zM12 15a3 3 0 01-3-3h6a3 3 0 01-3 3z"
          />
        </div>

        <!-- <TransactionCard
          :transactions="[
            {
              group: 'NEWEST',
              items: [
                { type: 'expense', name: 'Netflix', date: '27 March 2020', time: '12:30 PM', amount: '-$2500' },
                { type: 'income', name: 'Apple', date: '27 March 2020', time: '12:30 PM', amount: '+$2500' }
              ]
            },
            {
              group: 'YESTERDAY',
              items: [
                { type: 'income', name: 'Stripe', date: '26 March 2020', time: '13:45 PM', amount: '+$800' },
                { type: 'income', name: 'HubSpot', date: '26 March 2020', time: '12:30 PM', amount: '+$1700' },
                { type: 'pending', name: 'Webflow', date: '26 March 2020', time: '05:00 AM', amount: 'Pending' },
                { type: 'expense', name: 'Microsoft', date: '25 March 2025', time: '16:30 PM', amount: '-$987' }
              ]
            }
          ]"
        /> -->

        <!-- <InvoicesCard
          :invoices="[
            { date: 'March, 01, 2020', invoiceId: '#MS-415646', amount: '$180' },
            { date: 'February, 10, 2021', invoiceId: '#RV-126749', amount: '$250' },
            { date: 'April, 05, 2020', invoiceId: '#FB-212562', amount: '$560' },
            { date: 'June, 25, 2019', invoiceId: '#QW-103578', amount: '$120' },
            { date: 'March, 01, 2019', invoiceId: '#AR-803481', amount: '$300' }
          ]"
        /> -->

        <ProjectsCard
          :projects="[
            {
              image: 'https://picsum.photos/300/200',
              title: 'Modern',
              description: 'An AI tool works through a huge amount of internal management forms.',
            },
            {
              image: 'https://picsum.photos/300/201',
              title: 'Scandinavian',
              description: 'Music is something that every person has his or her own specific camera about.',
            },
            {
              image: 'https://picsum.photos/300/202',
              title: 'Minimalist',
              description: 'Different people have different taste, and various types of music.',
            },
          ]"
        />

        <Footer />
      </div>
</template>

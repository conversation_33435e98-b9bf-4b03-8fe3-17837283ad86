<template>
  <!--
    This example requires updating your template:

    ```
    <html class="h-full bg-white">
    <body class="h-full">
    ```
  -->
  <div>
    <TransitionRoot as="template" :show="sidebarOpen">
      <Dialog class="relative z-50 lg:hidden" @close="sidebarOpen = false">
        <TransitionChild as="template" enter="transition-opacity ease-linear duration-300" enter-from="opacity-0" enter-to="opacity-100" leave="transition-opacity ease-linear duration-300" leave-from="opacity-100" leave-to="opacity-0">
          <div class="fixed inset-0 bg-gray-900/80" />
        </TransitionChild>

        <div class="fixed inset-0 flex">
          <TransitionChild as="template" enter="transition ease-in-out duration-300 transform" enter-from="-translate-x-full" enter-to="translate-x-0" leave="transition ease-in-out duration-300 transform" leave-from="translate-x-0" leave-to="-translate-x-full">
            <DialogPanel class="relative mr-16 flex w-full max-w-xs flex-1">
              <TransitionChild as="template" enter="ease-in-out duration-300" enter-from="opacity-0" enter-to="opacity-100" leave="ease-in-out duration-300" leave-from="opacity-100" leave-to="opacity-0">
                <div class="absolute left-full top-0 flex w-16 justify-center pt-5">
                  <button type="button" class="-m-2.5 p-2.5" @click="sidebarOpen = false">
                    <span class="sr-only">Close sidebar</span>
                    <XMarkIcon class="size-6 text-white" aria-hidden="true" />
                  </button>
                </div>
              </TransitionChild>

              <!-- Sidebar component, swap this element with another sidebar if you like -->
              <div class="flex grow flex-col gap-y-6 overflow-y-auto px-4 pb-6" style="background-color: #f3f4f6;">
                <div class="flex h-20 shrink-0 items-center px-2 space-x-4 pt-8">
                  <div class="flex items-center justify-center w-16 h-16 bg-white rounded-xl shadow-sm border border-gray-200">
                    <img class="h-12 w-auto" src="@/assets/logo.png" alt="MANEB" />
                  </div>
                  <div class="text-gray-900">
                    <span class="block font-semibold text-lg text-maneb-primary">MANEB</span>
                    <div class="text-xs text-gray-600">Results Management System</div>
                  </div>
                </div>
                <nav class="flex flex-1 flex-col">
                  <ul role="list" class="flex flex-1 flex-col gap-y-8">
                    <li>
                      <ul role="list" class="space-y-2">
                        <li v-for="item in navigation" :key="item.name">
                          <div v-if="item.children">
                            <button @click="toggleExpand(item.name)" :class="[item.current ? 'bg-red-200 text-maneb-primary' : 'text-gray-700 hover:bg-red-100 hover:text-maneb-primary', 'group flex w-full items-center justify-between rounded-lg px-3 py-2 text-sm font-medium transition-colors duration-200']">
                              <div class="flex items-center gap-x-3">
                                <component :is="item.icon" :class="[item.current ? 'text-maneb-primary' : 'text-gray-500 group-hover:text-maneb-primary', 'size-5 shrink-0']" aria-hidden="true" />
                                <span :class="[item.current ? 'text-maneb-primary' : 'text-gray-700 group-hover:text-maneb-primary', '']">{{ item.name }}</span>
                              </div>
                            <ChevronRightIcon :class="[(expandedItems.has(item.name) || manuallyExpandedItems.has(item.name)) ? 'rotate-90' : '', 'size-5 text-gray-400 transition-transform duration-200 ease-in-out group-hover:text-maneb-primary']" aria-hidden="true" />
                          </button>
                          <div v-if="expandedItems.has(item.name) || manuallyExpandedItems.has(item.name)" class="mt-1 space-y-1">
                            <router-link v-for="child in item.children" :key="child.name" :to="child.href" :class="[child.href === route.path ? 'bg-red-200 text-maneb-primary' : 'text-gray-600 hover:bg-red-100 hover:text-maneb-primary', 'group flex items-center rounded-lg pl-11 pr-3 py-2 text-sm font-medium transition-colors duration-200']">
                              {{ child.name }}
                            </router-link>
                          </div>
                        </div>
                          <div v-else>
                            <router-link :to="item.href" :class="[item.current ? 'bg-red-200 text-maneb-primary' : 'text-gray-700 hover:bg-red-100 hover:text-maneb-primary', 'group flex w-full items-center justify-between rounded-lg px-3 py-2 text-sm font-medium transition-colors duration-200']">
                              <div class="flex items-center gap-x-3">
                                <component :is="item.icon" :class="[item.current ? 'text-maneb-primary' : 'text-gray-500 group-hover:text-maneb-primary', 'size-5 shrink-0']" aria-hidden="true" />
                                <span :class="[item.current ? 'text-maneb-primary' : 'text-gray-700 group-hover:text-maneb-primary', '']">{{ item.name }}</span>
                              </div>
                            </router-link>
                          </div>
                        </li>
                      </ul>
                    </li>
                    <li>
                      <div class="px-3 text-xs font-semibold uppercase tracking-wider text-gray-900">Your teams</div>
                      <ul role="list" class="mt-3 space-y-2">
                        <li v-for="team in teams" :key="team.name">
                          <button :class="[team.current ? 'bg-gray-800 text-white' : 'text-gray-900 hover:bg-gray-800 hover:text-white', 'group flex w-full items-center justify-between rounded-lg px-3 py-2 text-sm font-medium']">
                            <div class="flex items-center gap-x-3">
                              <span class="flex size-6 shrink-0 items-center justify-center rounded-lg bg-gray-800 text-[0.625rem] font-medium text-white ring-1 ring-gray-600">{{ team.initial }}</span>
                              <span :class="[team.current ? 'text-white' : 'text-gray-900', 'truncate']">{{ team.name }}</span>
                            </div>
                            <ChevronRightIcon class="size-5 text-gray-700 transition-transform duration-200 ease-in-out group-hover:text-white" aria-hidden="true" />
                          </button>
                        </li>
                      </ul>
                    </li>
                  </ul>

                  <!-- Powered by NITEL Footer (Mobile) -->
                  <div class="px-3 py-4 border-t border-gray-700 mt-4">
                    <a
                      href="https://www.nitel.mw"
                      target="_blank"
                      rel="noopener noreferrer"
                      class="text-xs text-gray-400 hover:text-white transition-colors duration-200"
                    >
                      <span class="mr-1">Powered by</span>
                      <span class="font-semibold" style="color:#5ddbdd">NITEL</span>
                    </a>
                  </div>
                </nav>
              </div>
            </DialogPanel>
          </TransitionChild>
        </div>
      </Dialog>
    </TransitionRoot>

    <!-- Static sidebar for desktop -->
    <div class="hidden lg:fixed lg:inset-y-0 lg:z-50 lg:flex lg:w-72 lg:flex-col">
      <!-- Sidebar component, swap this element with another sidebar if you like -->
      <div class="flex grow flex-col gap-y-6 overflow-y-auto px-4 pb-6 border-r border-gray-200" style="background-color: #f3f4f6;">
        <div class="flex h-20 shrink-0 items-center px-2 space-x-4 pt-4">
          <div class="flex items-center justify-center bg-white rounded-xl shadow-sm border border-gray-200">
            <img class="h-14 w-auto" src="@/assets/logo.png" alt="MANEB" />
          </div>
          <div class="text-gray-900">
            <span class="block font-semibold text-lg text-maneb-primary">MANEB</span>
            <div class="text-xs text-gray-600">Results Management System</div>
          </div>
        </div>
        <nav class="flex flex-1 flex-col">
          <ul role="list" class="flex flex-1 flex-col gap-y-8">
            <li>
              <ul role="list" class="space-y-2">
                <li v-for="item in navigation" :key="item.name">
                  <div v-if="item.children">
                    <button @click="toggleExpand(item.name)" :class="[item.current ? 'bg-red-200 text-maneb-primary' : 'text-gray-700 hover:bg-red-100 hover:text-maneb-primary', 'group flex w-full items-center justify-between rounded-lg px-3 py-2 text-sm font-medium transition-colors duration-200']">
                      <div class="flex items-center gap-x-3">
                        <component :is="item.icon" :class="[item.current ? 'text-maneb-primary' : 'text-gray-500 group-hover:text-maneb-primary', 'size-5 shrink-0']" aria-hidden="true" />
                        <span>{{ item.name }}</span>
                      </div>
                      <ChevronRightIcon :class="[(expandedItems.has(item.name) || manuallyExpandedItems.has(item.name)) ? 'rotate-90' : '', 'size-5 text-gray-400 transition-transform duration-200 ease-in-out group-hover:text-maneb-primary']" aria-hidden="true" />
                    </button>
                    <div v-if="expandedItems.has(item.name) || manuallyExpandedItems.has(item.name)" class="mt-1 space-y-1">
                      <router-link v-for="child in item.children" :key="child.name" :to="child.href" :class="[child.href === route.path ? 'bg-red-200 text-maneb-primary' : 'text-gray-600 hover:bg-red-100 hover:text-maneb-primary', 'group flex items-center rounded-lg pl-11 pr-3 py-2 text-sm font-medium transition-colors duration-200']">
                        {{ child.name }}
                      </router-link>
                    </div>
                  </div>
                  <div v-else>
                    <router-link :to="item.href" :class="[item.current ? 'bg-red-200 text-maneb-primary' : 'text-gray-700 hover:bg-red-100 hover:text-maneb-primary', 'group flex w-full items-center justify-between rounded-lg px-3 py-2 text-sm font-medium transition-colors duration-200']">
                      <div class="flex items-center gap-x-3">
                        <component :is="item.icon" :class="[item.current ? 'text-maneb-primary' : 'text-gray-500 group-hover:text-maneb-primary', 'size-5 shrink-0']" aria-hidden="true" />
                        <span>{{ item.name }}</span>
                      </div>
                    </router-link>
                  </div>
                </li>
              </ul>
            </li>
            <!-- <li>
              <div class="px-3 text-xs font-semibold uppercase tracking-wider text-gray-900">Your teams</div>
              <ul role="list" class="mt-3 space-y-2">
                <li v-for="team in teams" :key="team.name">
                  <div>
                    <button @click="toggleExpand(team.name)" class="group flex w-full items-center justify-between rounded-lg px-3 py-2 text-sm font-medium text-gray-900 hover:bg-gray-800 hover:text-white">
                      <div class="flex items-center gap-x-3">
                        <span class="flex size-6 shrink-0 items-center justify-center rounded-lg bg-gray-800 text-[0.625rem] font-medium text-white ring-1 ring-gray-600">{{ team.initial }}</span>
                        <span class="truncate">{{ team.name }}</span>
                      </div>
                      <ChevronRightIcon :class="[(expandedItems.has(team.name) || manuallyExpandedItems.has(team.name)) ? 'rotate-90' : '', 'size-5 text-gray-600 transition-transform duration-200 ease-in-out group-hover:text-white']" aria-hidden="true" />
                    </button>
                    <div v-if="expandedItems.has(team.name) || manuallyExpandedItems.has(team.name)" class="mt-1 space-y-1">
                      <a v-for="child in team.children" :key="child.name" :href="child.href" :class="[child.href === route.path ? 'bg-gray-800 text-white' : 'text-gray-800 hover:bg-gray-800 hover:text-white', 'group flex items-center rounded-lg pl-11 pr-3 py-2 text-sm font-medium']">
                        {{ child.name }}
                      </a>
                    </div>
                  </div>
                </li>
              </ul>
            </li> -->
          </ul>

          <!-- Powered by NITEL Footer -->
          <div class="px-3 py-4 border-t border-gray-700 mt-4">
            <a
              href="https://www.nitel.mw"
              target="_blank"
              rel="noopener noreferrer"
              class=" text-xs text-gray-400 transition-colors duration-200"
            >
              <span class="mr-1">Powered by</span>
              <span class="font-semibold" style="color:#5ddbdd">NITEL</span>
            </a>
          </div>
        </nav>
      </div>
    </div>

    <NavBar />
    <div class="lg:pl-72 pt-12 bg-gray-50 min-h-screen">
      <main class="py-4">
          <router-view />
      </main>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
// @ts-ignore
import NavBar from './nav-bar.vue'
import {
  Dialog,
  DialogPanel,
  TransitionChild,
  TransitionRoot,
} from '@headlessui/vue'
import {
  Cog6ToothIcon,
  HomeIcon,
  UsersIcon,
  XMarkIcon,
  ChevronRightIcon,
  ClipboardDocumentListIcon,
  AcademicCapIcon,
  DocumentTextIcon
} from '@heroicons/vue/24/outline'
import { useRoute } from 'vue-router'
import { useAuthStore } from '@/stores/auth/auth.store'
import { PermissionModule, PermissionAction } from '@/utils/auth/permissions'
import { useNavigationPermissions } from '@/composables/auth/useNavigationPermissions'

const route = useRoute()
const authStore = useAuthStore()
const { filterNavigationItems, filterSubmenuItems, permissionSummary, hasModulePermission, getModuleActions } = useNavigationPermissions()

// Watch for changes in user permissions to update navigation
import { watch } from 'vue'
watch(() => authStore.userPermissions, (newPermissions) => {
  console.log('User permissions changed, navigation will update:', newPermissions)
  console.log('Permission summary:', permissionSummary.value)
}, { deep: true })

const manuallyExpandedItems = ref(new Set<string>());

const expandedItems = computed(() => {
  const expanded = new Set<string>();
  navigation.value.forEach(item => {
    if (item.children && item.children.some(child => child.href === route.path)) {
      expanded.add(item.name);
    }
  });
  return expanded;
});

const toggleExpand = (itemName: string) => {
  if (manuallyExpandedItems.value.has(itemName)) {
    manuallyExpandedItems.value.delete(itemName);
  } else {
    manuallyExpandedItems.value.add(itemName);
  }
};

// Define all possible navigation items with RBAC permissions
const allNavigationItems = [
  {
    name: 'Dashboard',
    href: '/admin/dashboard',
    icon: HomeIcon,
    current: false,
    key: 'dashboard',
    permission: {
      module: PermissionModule.DASHBOARD,
      action: PermissionAction.VIEW
    }
  },
  {
    name: 'Score Entry',
    href: '/admin/score-entry',
    icon: ClipboardDocumentListIcon,
    current: false,
    key: 'score-entry',
    permission: {
      module: PermissionModule.SCORE_ENTRY,
      action: PermissionAction.VIEW
    },
    children: [
      { name: 'JCE | 2024', href: '/admin/score-entry/JCE/2024' },
      { name: 'MSCE | 2024', href: '/admin/score-entry/MSCE/2024' },
      { name: 'PSLCE | 2024', href: '/admin/score-entry/PSLCE/2024' },
      { name: 'TTC | 2024', href: '/admin/score-entry/TTC/2024' },
      { name: 'JCE | 2023', href: '/admin/score-entry/JCE/2023' },
      { name: 'MSCE | 2023', href: '/admin/score-entry/MSCE/2023' },
      { name: 'PSLCE | 2023', href: '/admin/score-entry/PSLCE/2023' },
      { name: 'TTC | 2023', href: '/admin/score-entry/TTC/2023' }
    ]
  },
  {
    name: 'Grading System Setup',
    href: '/admin/grading-system',
    icon: AcademicCapIcon,
    current: false,
    key: 'grading-system',
    permission: {
      module: PermissionModule.GRADING,
      action: PermissionAction.VIEW
    },
    children: [
      { name: 'Grading System', href: '/admin/grading-system' }
    ]
  },
  {
    name: 'Results Management',
    href: '/admin/results',
    icon: DocumentTextIcon,
    current: false,
    key: 'results',
    permission: {
      module: PermissionModule.RESULTS_MANAGEMENT,
      action: PermissionAction.VIEW
    }
  },
  {
    name: 'User Management',
    href: '/admin/user-management',
    icon: UsersIcon,
    current: false,
    key: 'user-management',
    permission: {
      module: PermissionModule.USER_MANAGEMENT,
      action: PermissionAction.VIEW
    },
    children: [
      { name: 'Users', href: '/admin/user-management/users' },
      { name: 'Roles', href: '/admin/user-management/roles' },
      { name: 'Permissions', href: '/admin/user-management/permissions' },
      { name: 'Sections', href: '/admin/user-management/sections' }
    ]
  },
  {
    name: 'Reports',
    href: '/admin/reports',
    icon: DocumentTextIcon,
    current: false,
    key: 'reports',
    permission: {
      module: PermissionModule.REPORTS,
      action: PermissionAction.VIEW
    }
  },
  {
    name: 'Audit Logs',
    href: '/admin/audit-logs',
    icon: ClipboardDocumentListIcon,
    current: false,
    key: 'audit-logs',
    permission: {
      module: PermissionModule.AUDIT_LOGS,
      action: PermissionAction.VIEW
    }
  },
  {
    name: 'System Settings',
    href: '/admin/system-settings',
    icon: Cog6ToothIcon,
    current: false,
    key: 'system-settings',
    permission: {
      module: PermissionModule.SYSTEM_SETTINGS,
      action: PermissionAction.VIEW
    }
  },
];

// Use the navigation permissions composable for all permission logic

// Filter navigation based on actual user permissions
const navigation = computed(() => {
  console.log('=== Dynamic Navigation Filtering ===');
  console.log('User role:', authStore.userRole);
  console.log('User permissions:', authStore.userPermissions);
  console.log('Is System Administrator:', authStore.isSystemAdministrator);

  // Filter navigation items based on user permissions
  const filteredItems = allNavigationItems.filter(item => {
    if (!item.permission?.module) {
      // If no permission module specified, show the item
      return true;
    }

    const hasAccess = hasModulePermission(item.permission.module);
    console.log(`Module ${item.permission.module} access:`, hasAccess);
    return hasAccess;
  });

  // Map items with current state and filtered children
  return filteredItems.map(item => {
    let isCurrent = false;
    if (item.name === 'Dashboard') {
      isCurrent = route.path === '/' || route.path === '/admin' || route.path.startsWith('/admin/dashboard');
    } else if (item.name === 'Score Entry') {
      isCurrent = route.path.startsWith('/admin/score-entry');
    } else {
      isCurrent = item.href === route.path || (item.children ? item.children.some(child => child.href === route.path) : false);
    }

    // Filter children based on permissions using the composable
    let filteredChildren = item.children;
    if (item.children && item.permission?.module) {
      const userActions = getModuleActions(item.permission.module);
      console.log(`User actions for ${item.permission.module}:`, userActions);

      // Use the composable's filterSubmenuItems method for enhanced filtering
      filteredChildren = filterSubmenuItems(item.permission.module, item.children);
    }

    return {
      ...item,
      current: isCurrent,
      children: filteredChildren
    };
  });
});



const teams = [
  {
    id: 1,
    name: 'Heroicons',
    href: '#',
    initial: 'H',
    current: false,
    children: [
      { name: 'Team Settings', href: '#' },
      { name: 'Members', href: '#' },
    ]
  },
  {
    id: 2,
    name: 'Tailwind Labs',
    href: '#',
    initial: 'T',
    current: false,
    children: [
      { name: 'Team Settings', href: '#' },
      { name: 'Members', href: '#' },
    ]
  },
  {
    id: 3,
    name: 'Workcation',
    href: '#',
    initial: 'W',
    current: false,
    children: [
      { name: 'Team Settings', href: '#' },
      { name: 'Members', href: '#' },
    ]
  },
]

const sidebarOpen = ref(false)
// const expandedItems = ref(new Set())

// const toggleExpand = (itemName: string) => {
//   if (expandedItems.value.has(itemName)) {
//     expandedItems.value.delete(itemName)
//   } else {
//     expandedItems.value.add(itemName)
//   }
// }
</script>

<style scoped>
/* MANEB Theme Colors */
.bg-maneb-primary {
  background-color: #a12c2c;
}

.text-maneb-primary {
  color: #a12c2c;
}

.bg-maneb-red-dark {
  background-color: #8b2424;
}

/* Light colored background containers for MANEB branding */
.bg-red-50 {
  background-color: #fef2f2;
}

.bg-red-100 {
  background-color: #fee2e2;
}

.bg-red-200 {
  background-color: #fecaca;
}

/* MANEB Primary with opacity variants for navigation states */
.bg-maneb-primary\/10 {
  background-color: rgba(161, 44, 44, 0.1);
}

.bg-maneb-primary\/20 {
  background-color: rgba(161, 44, 44, 0.2);
}

.bg-maneb-primary\/30 {
  background-color: rgba(161, 44, 44, 0.3);
}

/* Hover states */
.hover\:bg-maneb-primary\/10:hover {
  background-color: rgba(161, 44, 44, 0.1);
}

.hover\:bg-maneb-primary\/20:hover {
  background-color: rgba(161, 44, 44, 0.2);
}

.hover\:bg-maneb-primary\/30:hover {
  background-color: rgba(161, 44, 44, 0.3);
}
</style>

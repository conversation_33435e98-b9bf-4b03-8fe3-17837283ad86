<template>
  <div class="mx-auto max-w-4xl px-4 sm:px-6 lg:px-8 py-6">
    <!-- Header -->
    <div class="mb-8">
      <nav class="flex mb-5" aria-label="Breadcrumb">
        <ol class="inline-flex items-center space-x-1 text-sm font-medium md:space-x-2">
          <li class="inline-flex items-center">
            <router-link to="/admin/dashboard" class="inline-flex items-center text-gray-700 hover:text-maneb-primary transition-colors duration-200">
              <svg class="w-5 h-5 mr-2.5" fill="currentColor" viewBox="0 0 20 20">
                <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"/>
              </svg>
              Admin Dashboard
            </router-link>
          </li>
          <li>
            <div class="flex items-center">
              <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
              </svg>
              <router-link to="/admin/grading-system" class="ml-1 text-gray-700 hover:text-maneb-primary md:ml-2 transition-colors duration-200">
                Grading System
              </router-link>
            </div>
          </li>
          <li>
            <div class="flex items-center">
              <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
              </svg>
              <span class="ml-1 text-gray-400 md:ml-2" aria-current="page">Edit Paper</span>
            </div>
          </li>
        </ol>
      </nav>
      <h1 class="text-3xl font-bold text-gray-900">Edit Paper</h1>
      <p class="mt-2 text-sm text-gray-600">Update paper information</p>
    </div>

    <!-- Loading State -->
    <div v-if="isLoading" class="flex justify-center items-center py-16">
      <div class="animate-spin rounded-full h-16 w-16 border-4 border-gray-200 border-t-maneb-primary"></div>
    </div>

    <!-- Error State -->
    <div v-else-if="loadError" class="bg-red-50 border border-red-200 rounded-lg p-6 mb-6">
      <div class="flex">
        <div class="flex-shrink-0">
          <svg class="h-6 w-6 text-red-400" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
          </svg>
        </div>
        <div class="ml-3">
          <h3 class="text-sm font-medium text-red-800">Error loading paper</h3>
          <div class="mt-2 text-sm text-red-700">{{ loadError }}</div>
        </div>
      </div>
    </div>

    <!-- Edit Form -->
    <div v-else-if="paper" class="bg-white shadow-sm rounded-lg border border-gray-200">
      <div class="px-6 py-5 border-b border-gray-200">
        <h3 class="text-lg font-semibold text-gray-900">Update Paper Information</h3>
        <p class="mt-1 text-sm text-gray-600">Modify the paper details below and save changes.</p>
      </div>

      <form @submit.prevent="handleSubmit" class="px-6 py-6 space-y-6">
        <!-- Subject -->
        <div>
          <label for="subjectId" class="block text-sm font-medium text-gray-700">Subject <span class="text-red-500">*</span></label>
          <select id="subjectId" v-model="formData.subjectId" required class="mt-1 block w-full rounded-lg border border-gray-300 bg-white px-3 py-2.5 text-sm text-gray-900 focus:border-maneb-primary focus:ring-2 focus:ring-maneb-primary">
            <option value="">Select subject</option>
            <option v-for="subject in subjects" :key="subject.id" :value="subject.id">{{ subject.name }} ({{ subject.code }})</option>
          </select>
          <p v-if="errors.subjectId" class="mt-1 text-sm text-red-600">{{ errors.subjectId }}</p>
        </div>

        <!-- Paper Name -->
        <div>
          <label for="name" class="block text-sm font-medium text-gray-700">Paper Name <span class="text-red-500">*</span></label>
          <input id="name" v-model="formData.name" type="text" required class="mt-1 block w-full rounded-lg border border-gray-300 bg-white px-3 py-2.5 text-sm text-gray-900 focus:border-maneb-primary focus:ring-2 focus:ring-maneb-primary" placeholder="Enter paper name" />
          <p v-if="errors.name" class="mt-1 text-sm text-red-600">{{ errors.name }}</p>
        </div>

        <!-- Paper Code -->
        <div>
          <label for="code" class="block text-sm font-medium text-gray-700">Paper Code <span class="text-red-500">*</span></label>
          <input id="code" v-model="formData.code" type="text" required class="mt-1 block w-full rounded-lg border border-gray-300 bg-white px-3 py-2.5 text-sm text-gray-900 focus:border-maneb-primary focus:ring-2 focus:ring-maneb-primary" placeholder="Enter paper code" />
          <p v-if="errors.code" class="mt-1 text-sm text-red-600">{{ errors.code }}</p>
        </div>

        <!-- Paper Number -->
        <div>
          <label for="paperNumber" class="block text-sm font-medium text-gray-700">Paper Number <span class="text-red-500">*</span></label>
          <input id="paperNumber" v-model.number="formData.paperNumber" type="number" required min="1" max="10" class="mt-1 block w-full rounded-lg border border-gray-300 bg-white px-3 py-2.5 text-sm text-gray-900 focus:border-maneb-primary focus:ring-2 focus:ring-maneb-primary" placeholder="Enter paper number" />
          <p v-if="errors.paperNumber" class="mt-1 text-sm text-red-600">{{ errors.paperNumber }}</p>
        </div>

        <!-- Duration -->
        <div>
          <label for="duration" class="block text-sm font-medium text-gray-700">Duration (minutes) <span class="text-red-500">*</span></label>
          <input id="duration" v-model.number="formData.duration" type="number" required min="30" max="480" class="mt-1 block w-full rounded-lg border border-gray-300 bg-white px-3 py-2.5 text-sm text-gray-900 focus:border-maneb-primary focus:ring-2 focus:ring-maneb-primary" placeholder="Enter duration in minutes" />
          <p v-if="errors.duration" class="mt-1 text-sm text-red-600">{{ errors.duration }}</p>
        </div>

        <!-- Maximum Marks -->
        <div>
          <label for="maxMarks" class="block text-sm font-medium text-gray-700">Maximum Marks <span class="text-red-500">*</span></label>
          <input id="maxMarks" v-model.number="formData.maxMarks" type="number" required min="1" max="1000" class="mt-1 block w-full rounded-lg border border-gray-300 bg-white px-3 py-2.5 text-sm text-gray-900 focus:border-maneb-primary focus:ring-2 focus:ring-maneb-primary" placeholder="Enter maximum marks" />
          <p v-if="errors.maxMarks" class="mt-1 text-sm text-red-600">{{ errors.maxMarks }}</p>
        </div>

        <!-- Description -->
        <div>
          <label for="description" class="block text-sm font-medium text-gray-700">Description</label>
          <textarea id="description" v-model="formData.description" rows="3" class="mt-1 block w-full rounded-lg border border-gray-300 bg-white px-3 py-2.5 text-sm text-gray-900 focus:border-maneb-primary focus:ring-2 focus:ring-maneb-primary" placeholder="Enter description (optional)" />
        </div>

        <!-- Active Status -->
        <div class="flex items-center">
          <input id="isActive" v-model="formData.isActive" type="checkbox" class="h-4 w-4 text-maneb-primary focus:ring-maneb-primary border-gray-300 rounded" />
          <label for="isActive" class="ml-2 block text-sm text-gray-900">Active (paper is currently in use)</label>
        </div>

        <!-- Error Display -->
        <div v-if="submitError" class="bg-red-50 border border-red-200 rounded-lg p-4">
          <div class="flex">
            <div class="flex-shrink-0">
              <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
              </svg>
            </div>
            <div class="ml-3">
              <h3 class="text-sm font-medium text-red-800">Error updating paper</h3>
              <div class="mt-2 text-sm text-red-700">{{ submitError }}</div>
            </div>
          </div>
        </div>

        <!-- Form Actions -->
        <div class="flex justify-end space-x-3 pt-6 border-t border-gray-200">
          <router-link :to="{ name: 'admin.grading-system' }" class="inline-flex items-center px-6 py-3 border border-gray-300 text-sm font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-maneb-primary transition-colors duration-200">
            <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
            </svg>
            Cancel
          </router-link>
          <button type="submit" :disabled="isSubmitting" class="inline-flex items-center px-6 py-3 border border-transparent text-sm font-medium rounded-lg text-white bg-maneb-primary hover:bg-maneb-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-maneb-primary disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200 shadow-sm">
            <svg v-if="isSubmitting" class="animate-spin -ml-1 mr-3 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            <svg v-else class="h-4 w-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z"/>
            </svg>
            {{ isSubmitting ? 'Updating Paper...' : 'Update Paper' }}
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useGradingStore } from '@/stores'
import type { UpdatePaperRequest, PaperDto } from '@/interfaces'
import sweetAlert from '@/utils/ui/sweetAlert'

// Router
const router = useRouter()
const route = useRoute()

// Stores
const gradingStore = useGradingStore()

// Reactive state
const paper = ref<PaperDto | null>(null)
const isLoading = ref(false)
const loadError = ref<string | null>(null)
const isSubmitting = ref(false)
const submitError = ref<string | null>(null)
const subjects = ref<any[]>([])

const formData = reactive<{
  subjectId: string
  name: string
  code: string
  description: string
  paperNumber: number | null
  duration: number | null
  maxMarks: number | null
  isActive: boolean
}>({
  subjectId: '',
  name: '',
  code: '',
  description: '',
  paperNumber: null,
  duration: null,
  maxMarks: null,
  isActive: true
})

const errors = reactive<Record<string, string>>({})

// Computed
const paperId = computed(() => route.params.id as string)

// Methods
const populateForm = (paperData: PaperDto) => {
  formData.subjectId = paperData.subjectId || ''
  formData.name = paperData.name || ''
  formData.code = paperData.code || ''
  formData.description = paperData.description || ''
  formData.paperNumber = paperData.paperNumber || null
  formData.duration = paperData.duration || null
  formData.maxMarks = paperData.maxMarks || null
  formData.isActive = paperData.isActive ?? true
}

const validateForm = (): boolean => {
  Object.keys(errors).forEach(key => delete errors[key])
  let isValid = true

  if (!formData.subjectId.trim()) {
    errors.subjectId = 'Subject is required'
    isValid = false
  }
  if (!formData.name.trim()) {
    errors.name = 'Paper name is required'
    isValid = false
  }
  if (!formData.code.trim()) {
    errors.code = 'Paper code is required'
    isValid = false
  }
  if (formData.paperNumber === null || formData.paperNumber < 1 || formData.paperNumber > 10) {
    errors.paperNumber = 'Paper number must be between 1 and 10'
    isValid = false
  }
  if (formData.duration === null || formData.duration < 30 || formData.duration > 480) {
    errors.duration = 'Duration must be between 30 and 480 minutes'
    isValid = false
  }
  if (formData.maxMarks === null || formData.maxMarks < 1 || formData.maxMarks > 1000) {
    errors.maxMarks = 'Maximum marks must be between 1 and 1000'
    isValid = false
  }

  return isValid
}

const handleSubmit = async () => {
  if (!validateForm() || !paper.value?.id) return

  try {
    isSubmitting.value = true
    submitError.value = null

    const submitData: UpdatePaperRequest = {
      subjectId: formData.subjectId,
      name: formData.name.trim(),
      code: formData.code.trim(),
      description: formData.description.trim() || undefined,
      paperNumber: formData.paperNumber!,
      duration: formData.duration!,
      maxMarks: formData.maxMarks!,
      isActive: formData.isActive
    }

    await gradingStore.updatePaper(paper.value.id, submitData)
    await sweetAlert.success('Paper updated successfully!')
    router.push({ name: 'admin.grading-system' })
  } catch (error: any) {
    submitError.value = error.message || 'Failed to update paper'
  } finally {
    isSubmitting.value = false
  }
}

const fetchPaperData = async () => {
  try {
    isLoading.value = true
    loadError.value = null
    const paperData = await gradingStore.getPaper(paperId.value)
    paper.value = paperData
    populateForm(paperData)
  } catch (error: any) {
    loadError.value = error.message || 'Failed to load paper'
  } finally {
    isLoading.value = false
  }
}

const fetchSubjects = async () => {
  try {
    await gradingStore.fetchSubjects()
    subjects.value = gradingStore.subjects
  } catch (error) {
    console.error('Error fetching subjects:', error)
  }
}

// Lifecycle
onMounted(async () => {
  await fetchSubjects()
  await fetchPaperData()
})
</script>

<style scoped>
.bg-maneb-primary { background-color: #a12c2c; }
.text-maneb-primary { color: #a12c2c; }
.bg-maneb-primary-dark { background-color: #8b2424; }
.border-maneb-primary { border-color: #a12c2c; }
.focus\:ring-maneb-primary:focus { --tw-ring-color: #a12c2c; }
.focus\:border-maneb-primary:focus { border-color: #a12c2c; }
.hover\:bg-maneb-primary-dark:hover { background-color: #8b2424; }
</style>

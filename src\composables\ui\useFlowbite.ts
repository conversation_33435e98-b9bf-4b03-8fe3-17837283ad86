import { onMounted, onUnmounted, nextTick } from 'vue'
import { initFlowbite } from 'flowbite'

/**
 * Composable for proper Flowbite initialization in Vue components
 * Use this in components that contain Flowbite components to ensure proper initialization
 */
export function useFlowbite() {
  let initialized = false

  const initializeFlowbite = async () => {
    if (initialized) return
    
    await nextTick()
    initFlowbite()
    initialized = true
  }

  const reinitializeFlowbite = async () => {
    await nextTick()
    initFlowbite()
  }

  onMounted(() => {
    initializeFlowbite()
  })

  onUnmounted(() => {
    initialized = false
  })

  return {
    initializeFlowbite,
    reinitializeFlowbite
  }
}

/**
 * Manual Flowbite initialization for dynamic content
 * Call this when you dynamically add Flowbite components to the DOM
 */
export function reinitFlowbite() {
  nextTick(() => {
    initFlowbite()
  })
}

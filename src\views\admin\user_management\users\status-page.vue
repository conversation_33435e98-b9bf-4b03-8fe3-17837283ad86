<template>
  <div class="mx-auto max-w-4xl px-4 sm:px-6 lg:px-8 py-6">
    <!-- Enhanced <PERSON><PERSON> with MANEB Branding -->
    <div class="mb-8">
      <nav class="flex mb-5" aria-label="Breadcrumb">
        <ol class="inline-flex items-center space-x-1 text-sm font-medium md:space-x-2">
          <li class="inline-flex items-center">
            <router-link
              to="/admin/dashboard"
              class="inline-flex items-center text-gray-700 hover:text-maneb-primary transition-colors duration-200"
            >
              <svg class="w-5 h-5 mr-2.5" fill="currentColor" viewBox="0 0 20 20">
                <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"/>
              </svg>
              Admin Dashboard
            </router-link>
          </li>
          <li>
            <div class="flex items-center">
              <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
              </svg>
              <router-link
                to="/admin/user-management/users"
                class="ml-1 text-gray-700 hover:text-maneb-primary md:ml-2 transition-colors duration-200"
              >
                User Management
              </router-link>
            </div>
          </li>
          <li>
            <div class="flex items-center">
              <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
              </svg>
              <span class="ml-1 text-gray-400 md:ml-2" aria-current="page">Manage Status</span>
            </div>
          </li>
        </ol>
      </nav>
    </div>

    <!-- Enhanced Loading State -->
    <div v-if="isLoading" class="flex flex-col justify-center items-center py-16">
      <div class="animate-spin rounded-full h-16 w-16 border-4 border-gray-200 border-t-maneb-primary"></div>
      <p class="mt-4 text-sm text-gray-600">Loading user information...</p>
    </div>

    <!-- Enhanced Error State -->
    <div v-else-if="loadError" class="bg-red-50 border border-red-200 rounded-lg p-6 mb-6">
      <div class="flex">
        <div class="flex-shrink-0">
          <svg class="h-6 w-6 text-red-400" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
          </svg>
        </div>
        <div class="ml-3 flex-1">
          <h3 class="text-sm font-medium text-red-800">Error loading user</h3>
          <div class="mt-2 text-sm text-red-700">{{ loadError }}</div>
          <div class="mt-4">
            <button
              @click="fetchUserData"
              class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-red-700 bg-red-100 hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors duration-200"
            >
              Try Again
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Enhanced Status Management Content -->
    <div v-else-if="user" class="space-y-8">
      <!-- Enhanced Header -->
      <div class="bg-white shadow-sm rounded-lg border border-gray-200">
        <div class="px-6 py-6">
          <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
            <!-- User Info -->
            <div class="flex items-center space-x-4">
              <div class="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center">
                <span class="text-xl font-bold text-maneb-primary">
                  {{ getUserInitials(user) }}
                </span>
              </div>
              <div>
                <h1 class="text-3xl font-bold text-gray-900">Manage User Status</h1>
                <p class="mt-1 text-sm text-gray-600">
                  Update approval status for {{ user.fullName || `${user.firstName} ${user.lastName}` }}
                </p>
                <div class="mt-2 flex items-center space-x-4">
                  <span :class="getStatusBadgeClass(user.status)" class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium">
                    {{ getStatusDisplayText(user.status) }}
                  </span>
                  <span v-if="user.role" class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                    {{ user.role.name }}
                  </span>
                </div>
              </div>
            </div>

            <!-- Action Buttons -->
            <div class="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-3">
              <router-link
                :to="{ name: 'admin.user-management.users.detail', params: { id: user.id } }"
                class="inline-flex items-center justify-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-maneb-primary transition-colors duration-200"
              >
                <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                </svg>
                View Details
              </router-link>
              <router-link
                :to="{ name: 'admin.user-management.users' }"
                class="inline-flex items-center justify-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-maneb-primary transition-colors duration-200"
              >
                <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                </svg>
                Back to Users
              </router-link>
            </div>
          </div>
        </div>
      </div>

      <!-- Enhanced Current Status Display -->
      <div class="bg-white shadow-sm rounded-lg border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
          <div class="flex items-center space-x-3">
            <div class="w-8 h-8 bg-blue-50 rounded-lg flex items-center justify-center">
              <svg class="w-5 h-5 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
              </svg>
            </div>
            <h3 class="text-lg font-semibold text-gray-900">Current Status</h3>
          </div>
        </div>
        <div class="px-6 py-6">
          <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
              <div class="w-12 h-12 bg-red-50 rounded-full flex items-center justify-center">
                <span class="text-lg font-bold text-maneb-primary">
                  {{ getUserInitials(user) }}
                </span>
              </div>
              <div>
                <p class="text-base font-medium text-gray-900">{{ user.fullName || `${user.firstName} ${user.lastName}` }}</p>
                <p class="text-sm text-gray-600">{{ user.email }}</p>
                <p v-if="user.userName" class="text-sm text-gray-500">@{{ user.userName }}</p>
              </div>
            </div>
            <div class="text-right">
              <span :class="getStatusBadgeClass(user.status)" class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium">
                {{ getStatusDisplayText(user.status) }}
              </span>
              <p class="text-xs text-gray-500 mt-1">Current Status</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Status Change Form -->
      <div class="bg-white shadow-sm rounded-lg border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
          <h3 class="text-lg font-medium text-gray-900">Update Status</h3>
          <p class="mt-1 text-sm text-gray-600">Select a new status for this user account.</p>
        </div>
        
        <form @submit.prevent="handleStatusUpdate" class="px-6 py-4 space-y-6">
          <!-- Status Selection -->
          <div>
            <label class="text-base font-medium text-gray-900">Select New Status</label>
            <p class="text-sm leading-5 text-gray-500">Choose the appropriate status for this user.</p>
            <fieldset class="mt-4">
              <legend class="sr-only">Status options</legend>
              <div class="space-y-4">
                <div v-for="status in statusOptions" :key="status.value" class="flex items-center">
                  <input
                    :id="status.value"
                    v-model="selectedStatus"
                    :value="status.value"
                    name="status"
                    type="radio"
                    class="h-4 w-4 border-gray-300 text-maneb-primary focus:ring-maneb-primary"
                  />
                  <label :for="status.value" class="ml-3 block text-sm font-medium text-gray-700">
                    <span class="block">{{ status.label }}</span>
                    <span class="block text-gray-500 text-xs">{{ status.description }}</span>
                  </label>
                </div>
              </div>
            </fieldset>
          </div>

          <!-- Reason for Change -->
          <div>
            <label for="reason" class="block text-sm font-medium text-gray-700">
              Reason for Status Change <span class="text-red-500">*</span>
            </label>
            <textarea
              id="reason"
              v-model="reason"
              rows="3"
              required
              class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-maneb-primary focus:ring-maneb-primary sm:text-sm"
              placeholder="Please provide a reason for this status change..."
            ></textarea>
            <p v-if="errors.reason" class="mt-1 text-sm text-red-600">{{ errors.reason }}</p>
          </div>

          <!-- Error Display -->
          <div v-if="submitError" class="bg-red-50 border border-red-200 rounded-lg p-4">
            <div class="flex">
              <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                </svg>
              </div>
              <div class="ml-3">
                <h3 class="text-sm font-medium text-red-800">Error updating status</h3>
                <div class="mt-2 text-sm text-red-700">{{ submitError }}</div>
              </div>
            </div>
          </div>

          <!-- Form Actions -->
          <div class="flex justify-end space-x-3 pt-6 border-t border-gray-200">
            <router-link 
              :to="{ name: 'admin.user-management.users.detail', params: { id: user.id } }"
              class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-maneb-primary"
            >
              Cancel
            </router-link>
            <button
              type="submit"
              :disabled="isSubmitting || !selectedStatus || selectedStatus === user.status"
              class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-maneb-primary hover:bg-maneb-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-maneb-primary disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <svg v-if="isSubmitting" class="animate-spin -ml-1 mr-3 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              {{ isSubmitting ? 'Updating Status...' : 'Update Status' }}
            </button>
          </div>
        </form>
      </div>

      <!-- Status History (if available) -->
      <div class="bg-white shadow-sm rounded-lg border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
          <h3 class="text-lg font-medium text-gray-900">Status History</h3>
          <p class="mt-1 text-sm text-gray-600">Previous status changes for this user.</p>
        </div>
        <div class="px-6 py-4">
          <div class="text-center py-6">
            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
            </svg>
            <h3 class="mt-2 text-sm font-medium text-gray-900">No status history available</h3>
            <p class="mt-1 text-sm text-gray-500">Status change history will appear here once available.</p>
          </div>
        </div>
      </div>
    </div>

    <!-- User Not Found -->
    <div v-else class="text-center py-12">
      <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
      </svg>
      <h3 class="mt-2 text-sm font-medium text-gray-900">User not found</h3>
      <p class="mt-1 text-sm text-gray-500">The requested user could not be found.</p>
      <div class="mt-6">
        <router-link 
          :to="{ name: 'admin.user-management.users' }"
          class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-maneb-primary hover:bg-maneb-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-maneb-primary"
        >
          Back to Users
        </router-link>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useUserStore } from '@/stores'
import { showSuccessAlert, showConfirmDialog } from '@/utils/ui/sweetAlert'
import type { UserDto, RecordStatus } from '@/interfaces'

// Router
const route = useRoute()
const router = useRouter()

// Store
const userStore = useUserStore()

// Reactive state
const user = ref<UserDto | null>(null)
const isLoading = ref(false)
const loadError = ref<string | null>(null)
const isSubmitting = ref(false)
const submitError = ref<string | null>(null)
const selectedStatus = ref<RecordStatus | ''>('')
const reason = ref('')

const errors = reactive<Record<string, string>>({})

// Utility functions
const getUserInitials = (user: UserDto | null): string => {
  if (!user) return ''
  const firstName = user.firstName || ''
  const lastName = user.lastName || ''
  return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase()
}

const getStatusDisplayText = (status: RecordStatus | undefined): string => {
  if (!status) return 'Unknown'
  const statusNum = Number(status)
  switch (statusNum) {
    case 1: return 'Active'
    case 2: return 'Inactive'
    case 3: return 'Pending'
    case 4: return 'Suspended'
    default: return 'Unknown'
  }
}

const fetchUserData = async () => {
  if (!route.params.id || typeof route.params.id !== 'string') {
    loadError.value = 'Invalid user ID'
    return
  }

  isLoading.value = true
  loadError.value = null

  try {
    user.value = await userStore.fetchUserById(route.params.id)
    if (user.value) {
      selectedStatus.value = user.value.status || ''
    }
  } catch (error: any) {
    loadError.value = error.message || 'Failed to load user data'
  } finally {
    isLoading.value = false
  }
}

// Computed
const userId = computed(() => route.params.id as string)

const userStatusBreadcrumbs = computed(() => [
  { name: 'Admin', href: '/admin', current: false },
  { name: 'User Management', href: '/admin/user-management', current: false },
  { name: 'Users', href: '/admin/user-management/users', current: false },
  { name: user.value?.fullName || `${user.value?.firstName} ${user.value?.lastName}` || 'Manage Status', href: '', current: true },
])

const statusOptions = [
  {
    value: 'Unapproved' as RecordStatus,
    label: 'Unapproved',
    description: 'User account is pending approval and cannot access the system.'
  },
  {
    value: 'Approved' as RecordStatus,
    label: 'Approved',
    description: 'User account is approved and can access basic system features.'
  },
  {
    value: 'SecondApproved' as RecordStatus,
    label: 'Second Approved',
    description: 'User account has full approval with administrative privileges.'
  },
  {
    value: 'Rejected' as RecordStatus,
    label: 'Rejected',
    description: 'User account has been rejected and cannot access the system.'
  }
]

// Methods
const getStatusBadgeClass = (status?: RecordStatus) => {
  switch (status) {
    case 'Approved':
      return 'bg-green-100 text-green-800'
    case 'SecondApproved':
      return 'bg-blue-100 text-blue-800'
    case 'Unapproved':
      return 'bg-yellow-100 text-yellow-800'
    case 'Rejected':
      return 'bg-red-100 text-red-800'
    default:
      return 'bg-gray-100 text-gray-800'
  }
}

const validateForm = (): boolean => {
  // Clear previous errors
  Object.keys(errors).forEach(key => delete errors[key])

  let isValid = true

  if (!reason.value.trim()) {
    errors.reason = 'Reason for status change is required'
    isValid = false
  } else if (reason.value.trim().length < 10) {
    errors.reason = 'Please provide a more detailed reason (at least 10 characters)'
    isValid = false
  }

  return isValid
}

const handleStatusUpdate = async () => {
  if (!validateForm() || !user.value?.id || !selectedStatus.value) {
    return
  }

  // Show confirmation dialog
  const confirmed = await showConfirmDialog(
    'Confirm Status Change',
    `Are you sure you want to change the user status from "${user.value.status}" to "${selectedStatus.value}"?`,
    'Yes, Update Status'
  )

  if (!confirmed) {
    return
  }

  try {
    isSubmitting.value = true
    submitError.value = null

    // Update user status
    await userStore.updateUserStatus(user.value.id, selectedStatus.value)

    // Update local user data
    user.value.status = selectedStatus.value

    // Show success message
    await showSuccessAlert('User status updated successfully!')

    // Navigate to user detail page
    router.push({
      name: 'admin.user-management.users.detail',
      params: { id: user.value.id }
    })

  } catch (error: any) {
    submitError.value = error.message || 'Failed to update user status'
    console.error('Error updating user status:', error)
  } finally {
    isSubmitting.value = false
  }
}

const fetchUserDetails = async () => {
  if (!userId.value) {
    loadError.value = 'No user ID provided'
    return
  }

  try {
    isLoading.value = true
    loadError.value = null
    user.value = await userStore.fetchUserById(userId.value)
    selectedStatus.value = user.value.status || ''
  } catch (err: any) {
    loadError.value = err.message || 'Failed to load user details'
    console.error('Error fetching user details:', err)
  } finally {
    isLoading.value = false
  }
}

// Lifecycle
onMounted(() => {
  fetchUserDetails()
})
</script>

<style scoped>
/* MANEB Theme Colors */
.bg-maneb-primary {
  background-color: #a12c2c;
}

.text-maneb-primary {
  color: #a12c2c;
}

.bg-maneb-primary-dark {
  background-color: #8b2424;
}

.border-maneb-primary {
  border-color: #a12c2c;
}

.focus\:ring-maneb-primary:focus {
  --tw-ring-color: #a12c2c;
}

.focus\:border-maneb-primary:focus {
  border-color: #a12c2c;
}

.hover\:bg-maneb-primary-dark:hover {
  background-color: #8b2424;
}
</style>

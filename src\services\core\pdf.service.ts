import jsPDF from 'jspdf'
import html2canvas from 'html2canvas'
import type { StudentCertificateDto } from '@/interfaces'

export class PDFService {
  /**
   * Generate PDF from certificate data
   */
  async generateCertificatePDF(certificate: StudentCertificateDto): Promise<void> {
    try {
      // Create a temporary container for the certificate HTML
      const tempContainer = document.createElement('div')
      tempContainer.style.position = 'absolute'
      tempContainer.style.left = '-9999px'
      tempContainer.style.top = '-9999px'
      tempContainer.style.width = '800px'
      tempContainer.style.backgroundColor = 'white'
      tempContainer.style.padding = '40px'
      tempContainer.style.fontFamily = 'Arial, sans-serif'
      
      // Generate certificate HTML
      tempContainer.innerHTML = this.generateCertificateHTML(certificate)
      
      // Add to DOM temporarily
      document.body.appendChild(tempContainer)
      
      // Convert to canvas
      const canvas = await html2canvas(tempContainer, {
        scale: 2,
        useCORS: true,
        allowTaint: true,
        backgroundColor: '#ffffff'
      })
      
      // Remove temporary container
      document.body.removeChild(tempContainer)
      
      // Create PDF
      const imgData = canvas.toDataURL('image/png')
      const pdf = new jsPDF('p', 'mm', 'a4')
      
      const imgWidth = 210 // A4 width in mm
      const pageHeight = 295 // A4 height in mm
      const imgHeight = (canvas.height * imgWidth) / canvas.width
      let heightLeft = imgHeight
      
      let position = 0
      
      // Add first page
      pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight)
      heightLeft -= pageHeight
      
      // Add additional pages if needed
      while (heightLeft >= 0) {
        position = heightLeft - imgHeight
        pdf.addPage()
        pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight)
        heightLeft -= pageHeight
      }
      
      // Download the PDF
      const fileName = `MANEB_Certificate_${certificate.studentId}_${certificate.year}.pdf`
      pdf.save(fileName)
      
    } catch (error) {
      console.error('Error generating PDF:', error)
      throw new Error('Failed to generate PDF certificate')
    }
  }
  
  /**
   * Generate HTML content for certificate
   */
  private generateCertificateHTML(certificate: StudentCertificateDto): string {
    return `
      <div style="max-width: 800px; margin: 0 auto; background: white;">
        <!-- Header -->
        <div style="text-align: center; border-bottom: 4px solid #a12c2c; padding: 30px; background: linear-gradient(to right, #fef2f2, #fee2e2);">
          <div style="display: flex; align-items: center; justify-content: center; margin-bottom: 20px;">
            <div style="width: 60px; height: 60px; background: white; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-right: 20px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
              <svg width="40" height="40" fill="#a12c2c" viewBox="0 0 24 24">
                <path d="M12 2L2 7v10c0 5.55 3.84 9.74 9 11 5.16-1.26 9-5.45 9-11V7l-10-5z"/>
              </svg>
            </div>
            <div style="text-align: left;">
              <h1 style="margin: 0; font-size: 24px; font-weight: bold; color: #111827;">MALAWI NATIONAL EXAMINATIONS BOARD</h1>
              <p style="margin: 5px 0 0 0; font-size: 18px; color: #a12c2c; font-weight: 600;">MANEB</p>
            </div>
          </div>
          <h2 style="margin: 0 0 10px 0; font-size: 28px; font-weight: bold; color: #a12c2c;">CERTIFICATE OF EXAMINATION</h2>
          <p style="margin: 0 0 15px 0; font-size: 18px; color: #374151;">${certificate.examSession}</p>
          <p style="margin: 0; font-size: 14px; color: #6b7280;">Certificate No: <span style="font-family: monospace; font-weight: 600;">${certificate.certificateNumber}</span></p>
        </div>
        
        <!-- Student Information -->
        <div style="padding: 30px; border-bottom: 1px solid #e5e7eb;">
          <div style="text-align: center; margin-bottom: 20px;">
            <p style="margin: 0 0 10px 0; font-size: 18px; color: #374151;">This is to certify that</p>
            <h3 style="margin: 0 0 15px 0; font-size: 24px; font-weight: bold; color: #111827;">${certificate.studentName || certificate.studentId}</h3>
            <div style="display: flex; justify-content: center; gap: 30px; font-size: 14px; color: #6b7280;">
              <div>Student ID: <span style="font-weight: 600;">${certificate.studentId}</span></div>
              ${certificate.examNumber ? `<div>Exam Number: <span style="font-weight: 600;">${certificate.examNumber}</span></div>` : ''}
            </div>
          </div>
          <p style="text-align: center; font-size: 18px; color: #374151; margin: 0;">
            has successfully completed the examination in the following subjects:
          </p>
        </div>
        
        <!-- Subjects and Results -->
        <div style="padding: 30px;">
          ${certificate.subjects.map(subject => `
            <div style="background: #f9fafb; border-radius: 8px; padding: 20px; margin-bottom: 20px; border: 1px solid #e5e7eb;">
              <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                <div>
                  <h4 style="margin: 0 0 5px 0; font-size: 20px; font-weight: 600; color: #111827;">${subject.subjectName}</h4>
                  <p style="margin: 0; font-size: 14px; color: #6b7280;">Subject Code: ${subject.subjectCode}</p>
                </div>
                <div style="text-align: right;">
                  <div style="display: flex; align-items: center; justify-content: flex-end;">
                    <span style="font-size: 18px; font-weight: bold; margin-right: 10px;">Overall Grade:</span>
                    <span style="display: inline-block; padding: 6px 12px; border-radius: 20px; font-size: 14px; font-weight: 600; ${this.getGradeStyles(subject.overallGrade)}">${subject.overallGrade}</span>
                  </div>
                  <div style="font-size: 14px; margin-top: 5px; color: ${subject.overallPassStatus ? '#059669' : '#dc2626'};">
                    ${subject.overallPassStatus ? 'PASS' : 'FAIL'}
                  </div>
                </div>
              </div>
              
              <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 15px;">
                ${subject.papers.map(paper => `
                  <div style="background: white; border-radius: 6px; padding: 15px; border: 1px solid #f3f4f6;">
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                      <div>
                        <p style="margin: 0 0 5px 0; font-weight: 500; color: #111827;">${paper.paperName}</p>
                        <p style="margin: 0; font-size: 12px; color: #9ca3af;">${paper.paperId}</p>
                      </div>
                      <div style="text-align: right;">
                        <div style="font-size: 18px; font-weight: bold; color: #111827;">${paper.score}%</div>
                        <div style="display: flex; align-items: center; gap: 8px; margin-top: 5px;">
                          <span style="display: inline-block; padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: 600; ${this.getGradeStyles(paper.grade)}">${paper.grade}</span>
                          <span style="font-size: 12px; font-weight: 600; color: ${paper.passStatus ? '#059669' : '#dc2626'};">
                            ${paper.passStatus ? 'PASS' : 'FAIL'}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                `).join('')}
              </div>
            </div>
          `).join('')}
        </div>
        
        <!-- Overall Performance Summary -->
        <div style="background: #f9fafb; padding: 30px; border-top: 1px solid #e5e7eb;">
          <h4 style="margin: 0 0 20px 0; font-size: 20px; font-weight: 600; color: #111827; text-align: center;">Overall Performance Summary</h4>
          <div style="display: grid; grid-template-columns: repeat(4, 1fr); gap: 15px; text-align: center; margin-bottom: 25px;">
            <div style="background: white; border-radius: 8px; padding: 15px; border: 1px solid #e5e7eb;">
              <div style="font-size: 24px; font-weight: bold; color: #a12c2c;">${certificate.overallPerformance.totalSubjects}</div>
              <div style="font-size: 14px; color: #6b7280;">Total Subjects</div>
            </div>
            <div style="background: white; border-radius: 8px; padding: 15px; border: 1px solid #e5e7eb;">
              <div style="font-size: 24px; font-weight: bold; color: #059669;">${certificate.overallPerformance.passedSubjects}</div>
              <div style="font-size: 14px; color: #6b7280;">Passed</div>
            </div>
            <div style="background: white; border-radius: 8px; padding: 15px; border: 1px solid #e5e7eb;">
              <div style="font-size: 24px; font-weight: bold; color: #dc2626;">${certificate.overallPerformance.failedSubjects}</div>
              <div style="font-size: 14px; color: #6b7280;">Failed</div>
            </div>
            <div style="background: white; border-radius: 8px; padding: 15px; border: 1px solid #e5e7eb;">
              <div style="font-size: 24px; font-weight: bold; color: #2563eb;">${certificate.overallPerformance.passRate.toFixed(1)}%</div>
              <div style="font-size: 14px; color: #6b7280;">Pass Rate</div>
            </div>
          </div>
          
          <div style="text-align: center;">
            <div style="display: inline-flex; align-items: center; gap: 12px;">
              <span style="font-size: 18px; font-weight: 600; color: #111827;">Overall Grade:</span>
              <span style="display: inline-block; padding: 8px 16px; border-radius: 20px; font-size: 18px; font-weight: bold; ${this.getGradeStyles(certificate.overallPerformance.overallGrade)}">${certificate.overallPerformance.overallGrade}</span>
            </div>
          </div>
        </div>
        
        <!-- Footer -->
        <div style="background: #a12c2c; color: white; padding: 25px; text-align: center;">
          <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; font-size: 14px; margin-bottom: 15px;">
            <div>
              <p style="margin: 0 0 5px 0; font-weight: 600;">Issue Date:</p>
              <p style="margin: 0;">${new Date(certificate.issueDate).toLocaleDateString('en-GB', { day: 'numeric', month: 'long', year: 'numeric' })}</p>
            </div>
            <div>
              <p style="margin: 0 0 5px 0; font-weight: 600;">Issued By:</p>
              <p style="margin: 0;">${certificate.issuedBy}</p>
            </div>
          </div>
          <div style="border-top: 1px solid rgba(255,255,255,0.3); padding-top: 15px;">
            <p style="margin: 0; font-size: 12px; opacity: 0.9;">
              This certificate is issued by the Malawi National Examinations Board and is valid for official purposes.
            </p>
          </div>
        </div>
      </div>
    `
  }
  
  /**
   * Get CSS styles for grade badges
   */
  private getGradeStyles(grade: string): string {
    const styles = {
      'A': 'background: #dcfce7; color: #166534;',
      'B': 'background: #dbeafe; color: #1e40af;',
      'C': 'background: #fef3c7; color: #92400e;',
      'D': 'background: #fed7aa; color: #c2410c;',
      'F': 'background: #fecaca; color: #dc2626;'
    }
    return styles[grade as keyof typeof styles] || 'background: #f3f4f6; color: #374151;'
  }
}

// Create singleton instance
export const pdfService = new PDFService()

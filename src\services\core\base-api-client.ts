import axios from 'axios';
import type { AxiosInstance, AxiosRequestConfig, AxiosResponse, AxiosError } from 'axios';
import { authErrorHandler } from '../auth/auth-error-handler.service';

/**
 * Configuration for API client instances
 */
export interface BaseApiClientConfig {
  /** Base URL for the API */
  baseURL: string;
  /** Request timeout in milliseconds */
  timeout?: number;
  /** Additional headers to include with requests */
  headers?: Record<string, string>;
  /** Whether to enable debug logging */
  enableDebugLogging?: boolean;
  /** Custom error formatter */
  errorFormatter?: (error: AxiosError) => any;
}

/**
 * Base API client with common functionality
 * Provides shared axios setup, token management, and error handling
 */
export abstract class BaseApiClient {
  protected client: AxiosInstance;
  protected token: string | null = null;
  protected baseURL: string;
  protected enableDebugLogging: boolean;
  protected errorFormatter?: (error: AxiosError) => any;

  constructor(config: BaseApiClientConfig) {
    this.baseURL = config.baseURL;
    this.enableDebugLogging = config.enableDebugLogging ?? import.meta.env.DEV;
    this.errorFormatter = config.errorFormatter;

    // Log configuration in development (console logging removed)
    if (this.enableDebugLogging) {
      // Configuration logging removed
    }

    this.client = axios.create({
      baseURL: config.baseURL,
      timeout: config.timeout || 30000,
      headers: {
        'Content-Type': 'application/json',
        ...config.headers,
      },
    });

    this.setupInterceptors();
  }

  /**
   * Setup request and response interceptors
   */
  private setupInterceptors(): void {
    // Request interceptor to add auth token
    this.client.interceptors.request.use(
      (config) => {
        if (this.token) {
          config.headers.Authorization = `Bearer ${this.token}`;
        }
        return config;
      },
      (error) => Promise.reject(error)
    );

    // Response interceptor for error handling
    this.client.interceptors.response.use(
      (response) => response,
      (error: AxiosError) => {
        this.handleError(error);
        return Promise.reject(this.formatError(error));
      }
    );
  }

  /**
   * Handle API errors with logging and auth failure detection
   */
  protected handleError(error: AxiosError): void {
    // Enhanced error logging for debugging (console logging removed)
    if (this.enableDebugLogging && error.response) {
      // Error details logged (console logging removed)
    }

    // Handle authentication errors using centralized handler
    if (error.response?.status === 401) {
      authErrorHandler.handle401Error(this.constructor.name, error);
    } else if (error.response?.status === 403) {
      authErrorHandler.handlePermissionDenied(this.constructor.name, error);
    }
  }

  /**
   * Format error for consistent error handling
   */
  protected formatError(error: AxiosError): any {
    if (this.errorFormatter) {
      return this.errorFormatter(error);
    }

    // Default error formatting
    return {
      message: error.response?.data?.message || error.message || 'Request failed',
      status: error.response?.status,
      statusText: error.response?.statusText,
      data: error.response?.data,
    };
  }

  /**
   * Set authentication token
   */
  setToken(token: string): void {
    this.token = token;
    if (typeof localStorage !== 'undefined') {
      localStorage.setItem('auth_token', token);
    }
  }

  /**
   * Clear authentication token
   */
  clearToken(): void {
    this.token = null;
    if (typeof localStorage !== 'undefined') {
      localStorage.removeItem('auth_token');
      localStorage.removeItem('refresh_token');
    }
  }

  /**
   * Load token from localStorage
   */
  loadToken(): void {
    if (typeof localStorage !== 'undefined') {
      const token = localStorage.getItem('auth_token');
      if (token) {
        this.token = token;
      }
    }
  }

  /**
   * Check if client has a valid token
   */
  hasToken(): boolean {
    return !!this.token;
  }

  /**
   * Get current base URL
   */
  getBaseURL(): string {
    return this.baseURL;
  }

  /**
   * Generic HTTP methods
   */
  protected async get<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
    const response: AxiosResponse<T> = await this.client.get(url, config);
    return response.data;
  }

  protected async post<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    const response: AxiosResponse<T> = await this.client.post(url, data, config);
    return response.data;
  }

  protected async put<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    const response: AxiosResponse<T> = await this.client.put(url, data, config);
    return response.data;
  }

  protected async delete<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
    const response: AxiosResponse<T> = await this.client.delete(url, config);
    return response.data;
  }

  protected async patch<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    const response: AxiosResponse<T> = await this.client.patch(url, data, config);
    return response.data;
  }

  /**
   * Health check endpoint
   */
  async healthCheck(): Promise<boolean> {
    try {
      await this.get('/api/health');
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Get API status information
   */
  getApiStatus(): { connected: boolean; baseURL: string; hasToken: boolean; clientName: string } {
    return {
      connected: true, // Will be false if health check fails
      baseURL: this.baseURL,
      hasToken: this.hasToken(),
      clientName: this.constructor.name
    };
  }
}

import apiClient from '@/services/core/api-client';
import type { UserDto, RoleDto } from '@/interfaces';

export class ApiDebugger {
  
  /**
   * Test user update endpoint with minimal data
   */
  static async testUserUpdate(userId: string, roleId?: string) {
    try {
      // API Debug: Testing User Update (console logging removed)

      // First, get the current user
      // Fetching current user (console logging removed)
      const currentUser = await apiClient.get<UserDto>(`/api/User/${userId}`);
      // Current user (console logging removed)
      
      // Test with minimal update data
      const updateData = {
        id: userId,
        firstName: currentUser.firstName,
        lastName: currentUser.lastName,
        ...(roleId && { roleId })
      };
      
      // Testing update with data (console logging removed)

      const result = await apiClient.put<UserDto>(`/api/User/${userId}`, updateData);
      // Update successful (console logging removed)

      return result;
    } catch (error: any) {
      // API Debug: Update Failed (console logging removed)
      throw error;
    }
  }
  
  /**
   * Test role endpoints
   */
  static async testRoleEndpoints() {
    try {
      // API Debug: Testing Role Endpoints (console logging removed)

      // Get all roles
      const roles = await apiClient.get<RoleDto[]>('/api/Role');
      // Available roles (console logging removed)

      return roles;
    } catch (error: any) {
      // API Debug: Role Fetch Failed (console logging removed)
      throw error;
    }
  }
  
  static async testAuth() {
    try {
      const authResult = await apiClient.get('/api/debug-user');
      return authResult;
    } catch (error: any) {
      throw error;
    }
  }
  
  static async runFullTest(userId: string, roleId?: string) {
    try {
      await this.testAuth();
      await this.testRoleEndpoints();

      if (roleId) {
        await this.testUserUpdate(userId, roleId);
      }
    } catch (error) {
      throw error;
    }
  }

  /**
   * Test UserRoles endpoints
   */
  static async testUserRolesEndpoints(userId: string, roleId?: string) {
    try {
      // API Debug: Testing UserRoles Endpoints (console logging removed)

      if (!userId) {
        throw new Error('User ID is required for UserRoles testing');
      }

      // Test 1: Get user roles
      // Testing GET /api/UserRoles/user/{userId} (console logging removed)
      try {
        const userRoles = await apiClient.get<RoleDto[]>(`/api/UserRoles/user/${userId}`);
        // User roles retrieved (console logging removed)
      } catch (error: any) {
        // Failed to get user roles (console logging removed)
      }

      if (roleId) {
        // Test 2: Check if user has specific role
        // Testing GET /api/UserRoles/check/{userId}/{roleId} (console logging removed)
        try {
          const hasRole = await apiClient.get<boolean>(`/api/UserRoles/check/${userId}/${roleId}`);
          // User has role check result (console logging removed)
        } catch (error: any) {
          // Failed to check user role (console logging removed)
        }

        // Test 3: Get users with specific role
        // Testing GET /api/UserRoles/role/{roleId} (console logging removed)
        try {
          const usersWithRole = await apiClient.get<UserDto[]>(`/api/UserRoles/role/${roleId}`);
          // Users with role retrieved (console logging removed)
        } catch (error: any) {
          // Failed to get users with role (console logging removed)
        }

        // Test 4: Assign role
        // Testing POST /api/UserRoles/assign (console logging removed)
        try {
          const assignData = { userId, roleId };
          await apiClient.post('/api/UserRoles/assign', assignData);
          // Role assignment successful (console logging removed)
        } catch (error: any) {
          // Failed to assign role (console logging removed)
        }

        // Test 5: Remove role
        // Testing DELETE /api/UserRoles/remove/{userId}/{roleId} (console logging removed)
        try {
          await apiClient.delete(`/api/UserRoles/remove/${userId}/${roleId}`);
          // Role removal successful (console logging removed)
        } catch (error: any) {
          // Failed to remove role (console logging removed)
        }
      } else {
        // No roleId provided, skipping role-specific tests (console logging removed)
      }

      // UserRoles Endpoints Test Complete (console logging removed)
    } catch (error: any) {
      // API Debug: UserRoles Test Failed (console logging removed)
      throw error;
    }
  }

  /**
   * Test the new role assignment workflow
   */
  static async testRoleAssignmentWorkflow(userId: string, roleId: string) {
    try {
      // API Debug: Testing Role Assignment Workflow (console logging removed)

      // Step 1: Check initial state
      // Checking initial user role state (console logging removed)
      const initialRoles = await apiClient.get<RoleDto[]>(`/api/UserRoles/user/${userId}`);
      // Initial roles (console logging removed)

      const initialHasRole = await apiClient.get<boolean>(`/api/UserRoles/check/${userId}/${roleId}`);
      // Initially has target role (console logging removed)

      // Step 2: Assign role if not already assigned
      if (!initialHasRole) {
        // Assigning role (console logging removed)
        const assignData = { userId, roleId };
        await apiClient.post('/api/UserRoles/assign', assignData);
        // Role assigned successfully (console logging removed)

        // Verify assignment
        const hasRoleAfterAssign = await apiClient.get<boolean>(`/api/UserRoles/check/${userId}/${roleId}`);
        // Has role after assignment (console logging removed)
      } else {
        // User already has the role, skipping assignment (console logging removed)
      }

      // Step 3: Remove role
      // Removing role (console logging removed)
      await apiClient.delete(`/api/UserRoles/remove/${userId}/${roleId}`);
      // Role removed successfully (console logging removed)

      // Verify removal
      const hasRoleAfterRemoval = await apiClient.get<boolean>(`/api/UserRoles/check/${userId}/${roleId}`);
      // Has role after removal (console logging removed)

      // Step 4: Check final state
      // Checking final user role state (console logging removed)
      const finalRoles = await apiClient.get<RoleDto[]>(`/api/UserRoles/user/${userId}`);
      // Final roles (console logging removed)

      // Role Assignment Workflow Test Complete (console logging removed)
      return {
        initialRoles,
        initialHasRole,
        finalRoles,
        hasRoleAfterRemoval
      };
    } catch (error: any) {
      // API Debug: Role Assignment Workflow Failed (console logging removed)
      throw error;
    }
  }

  /**
   * Simple test function for role assignment - can be called from browser console
   */
  static async testSimpleRoleAssignment(userId: string, roleId: string) {
    try {
      // Simple Role Assignment Test (console logging removed)

      const assignmentData = {
        userId: userId,
        roleId: roleId
      };

      // Assignment data (console logging removed)

      const response = await apiClient.post('/api/UserRoles/assign', assignmentData);
      // Assignment successful (console logging removed)

      return response;
    } catch (error: any) {
      // Assignment failed (console logging removed)
      throw error;
    }
  }
}

// Expose to global window for easy testing in browser console
if (typeof window !== 'undefined') {
  (window as any).ApiDebugger = ApiDebugger;
}

// Export for use in components
export default ApiDebugger;

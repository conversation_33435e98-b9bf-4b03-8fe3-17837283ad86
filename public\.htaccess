# Apache .htaccess file for SPA routing
# This file handles SPA routing for Apache web servers

<IfModule mod_rewrite.c>
  RewriteEngine On
  
  # Handle Angular and Vue.js Router
  # If the requested resource doesn't exist as a file or directory
  # Serve the index.html file instead
  RewriteCond %{REQUEST_FILENAME} !-f
  RewriteCond %{REQUEST_FILENAME} !-d
  RewriteRule . /index.html [L]
  
  # Optional: Set proper MIME types for modern file extensions
  <IfModule mod_mime.c>
    AddType application/javascript .js
    AddType text/css .css
  </IfModule>
  
  # Optional: Enable compression
  <IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
  </IfModule>
</IfModule>

/**
 * @fileoverview Role Management Store for MANEB Results Management System
 * @description Pinia store for managing role state with full CRUD operations
 */

import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import { roleManagementService, rolePermissionManagementService } from '@/services';
import type { 
  RoleDto, 
  CreateRoleRequest, 
  UpdateRoleRequest,
  RoleFilterDto,
  RecordStatus,
  CreateRolePermissionRequest
} from '@/interfaces';

export const useRoleManagementStore = defineStore('roleManagement', () => {
  // State
  const roles = ref<RoleDto[]>([]);
  const currentRole = ref<RoleDto | null>(null);
  const isLoading = ref(false);
  const error = ref<string | null>(null);
  const searchQuery = ref('');
  const filters = ref<RoleFilterDto>({
    searchQuery: '',
    status: 'All',
    hasPermissions: 'All',
    isActive: 'All'
  });
  const pagination = ref({
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 0
  });

  // Getters
  const filteredRoles = computed(() => {
    let result = roles.value;

    if (searchQuery.value) {
      const query = searchQuery.value.toLowerCase();
      result = result.filter(role =>
        role.name?.toLowerCase().includes(query)
      );
    }

    if (filters.value.status && filters.value.status !== 'All') {
      result = result.filter(role => role.status === filters.value.status);
    }

    if (filters.value.hasPermissions !== undefined && filters.value.hasPermissions !== 'All') {
      result = result.filter(role => {
        const hasPerms = role.rolePermissions && role.rolePermissions.length > 0;
        return filters.value.hasPermissions === true ? hasPerms : !hasPerms;
      });
    }

    return result;
  });

  const rolesByStatus = computed(() => {
    return (status: RecordStatus) => roles.value.filter(role => role.status === status);
  });

  const totalRoles = computed(() => roles.value.length);

  const activeRoles = computed(() => 
    roles.value.filter(role => role.status === 'Approved' || role.status === 'SecondApproved')
  );

  const systemRoles = computed(() => 
    roles.value.filter(role => roleManagementService.isSystemAdminRole(role))
  );

  const nonSystemRoles = computed(() => 
    roles.value.filter(role => !roleManagementService.isSystemAdminRole(role))
  );

  // Actions
  const fetchRoles = async (): Promise<void> => {
    try {
      isLoading.value = true;
      error.value = null;
      roles.value = await roleManagementService.getAllRoles();
    } catch (err: any) {
      error.value = err.message || 'Failed to fetch roles';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const fetchRolesPaginated = async (page: number = 1, limit: number = 10): Promise<void> => {
    try {
      isLoading.value = true;
      error.value = null;
      
      const response = await roleManagementService.getRolesPaginated(page, limit, {
        Search: searchQuery.value || undefined,
        status: filters.value.status !== 'All' ? filters.value.status as RecordStatus : undefined
      });
      
      roles.value = response;
      pagination.value = {
        page,
        limit,
        total: response.length,
        totalPages: Math.ceil(response.length / limit)
      };
    } catch (err: any) {
      error.value = err.message || 'Failed to fetch roles';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const fetchRoleById = async (id: string): Promise<RoleDto> => {
    try {
      isLoading.value = true;
      error.value = null;
      const role = await roleManagementService.getRoleById(id);
      currentRole.value = role;
      return role;
    } catch (err: any) {
      error.value = err.message || 'Failed to fetch role';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const createRole = async (roleData: CreateRoleRequest): Promise<RoleDto> => {
    try {
      isLoading.value = true;
      error.value = null;
      const newRole = await roleManagementService.createRole(roleData);
      roles.value.push(newRole);
      return newRole;
    } catch (err: any) {
      error.value = err.message || 'Failed to create role';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const createRoleWithPermissions = async (
    roleData: CreateRoleRequest,
    permissions: CreateRolePermissionRequest[]
  ): Promise<RoleDto> => {
    try {
      isLoading.value = true;
      error.value = null;
      
      // Create the role first
      const newRole = await roleManagementService.createRole(roleData);
      
      // Then create permissions for the role
      if (permissions.length > 0 && newRole.id) {
        const permissionRequests = permissions.map(permission => ({
          ...permission,
          roleId: newRole.id!
        }));
        
        await rolePermissionManagementService.bulkCreateRolePermissions(permissionRequests);
        
        // Refresh role data to get permissions
        const updatedRole = await roleManagementService.getRoleById(newRole.id);
        
        // Update local state
        const index = roles.value.findIndex(role => role.id === newRole.id);
        if (index !== -1) {
          roles.value[index] = updatedRole;
        } else {
          roles.value.push(updatedRole);
        }
        
        return updatedRole;
      }
      
      roles.value.push(newRole);
      return newRole;
    } catch (err: any) {
      error.value = err.message || 'Failed to create role with permissions';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const updateRole = async (id: string, roleData: UpdateRoleRequest): Promise<void> => {
    try {
      isLoading.value = true;
      error.value = null;
      await roleManagementService.updateRole(id, roleData);
      
      // Update local state
      const index = roles.value.findIndex(role => role.id === id);
      if (index !== -1) {
        roles.value[index] = { ...roles.value[index], ...roleData };
      }
      
      if (currentRole.value?.id === id) {
        currentRole.value = { ...currentRole.value, ...roleData };
      }
    } catch (err: any) {
      error.value = err.message || 'Failed to update role';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const updateRoleWithPermissions = async (
    roleId: string,
    roleData: UpdateRoleRequest,
    permissions: CreateRolePermissionRequest[]
  ): Promise<void> => {
    try {
      isLoading.value = true;
      error.value = null;
      
      // Update role data
      await roleManagementService.updateRole(roleId, roleData);
      
      // Replace permissions
      const permissionRequests = permissions.map(permission => ({
        ...permission,
        roleId
      }));
      
      await rolePermissionManagementService.replaceRolePermissions(roleId, permissionRequests);
      
      // Refresh role data
      const updatedRole = await roleManagementService.getRoleById(roleId);
      
      // Update local state
      const index = roles.value.findIndex(role => role.id === roleId);
      if (index !== -1) {
        roles.value[index] = updatedRole;
      }
      
      if (currentRole.value?.id === roleId) {
        currentRole.value = updatedRole;
      }
    } catch (err: any) {
      error.value = err.message || 'Failed to update role with permissions';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const deleteRole = async (id: string): Promise<void> => {
    try {
      isLoading.value = true;
      error.value = null;
      await roleManagementService.deleteRole(id);
      
      // Remove from local state
      roles.value = roles.value.filter(role => role.id !== id);
      if (currentRole.value?.id === id) {
        currentRole.value = null;
      }
    } catch (err: any) {
      error.value = err.message || 'Failed to delete role';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const approveRole = async (id: string): Promise<void> => {
    try {
      isLoading.value = true;
      error.value = null;
      await roleManagementService.approveRole(id);
      
      // Update local state
      const index = roles.value.findIndex(role => role.id === id);
      if (index !== -1) {
        roles.value[index].status = 'Approved';
      }
    } catch (err: any) {
      error.value = err.message || 'Failed to approve role';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const searchRoles = async (query: string): Promise<void> => {
    searchQuery.value = query;
    await fetchRolesPaginated(1, pagination.value.limit);
  };

  const filterRoles = async (newFilters: Partial<RoleFilterDto>): Promise<void> => {
    filters.value = { ...filters.value, ...newFilters };
    await fetchRolesPaginated(1, pagination.value.limit);
  };

  const clearError = (): void => {
    error.value = null;
  };

  const clearCurrentRole = (): void => {
    currentRole.value = null;
  };

  const resetFilters = (): void => {
    filters.value = {
      searchQuery: '',
      status: 'All',
      hasPermissions: 'All',
      isActive: 'All'
    };
    searchQuery.value = '';
  };

  return {
    // State
    roles,
    currentRole,
    isLoading,
    error,
    searchQuery,
    filters,
    pagination,
    
    // Getters
    filteredRoles,
    rolesByStatus,
    totalRoles,
    activeRoles,
    systemRoles,
    nonSystemRoles,
    
    // Actions
    fetchRoles,
    fetchRolesPaginated,
    fetchRoleById,
    createRole,
    createRoleWithPermissions,
    updateRole,
    updateRoleWithPermissions,
    deleteRole,
    approveRole,
    searchRoles,
    filterRoles,
    clearError,
    clearCurrentRole,
    resetFilters,
  };
});

/**
 * @fileoverview External Results API interfaces for MANEB Results Management System
 * @description Interfaces for external examination results system API integration
 * @apiUrl http://************:5000/swagger/v1/swagger.json
 */

export interface AuditLog {
  logId: number
  action?: string
  performedBy?: string // UUID
  performedAt?: Date
  entity?: string
  entityId?: number
  details?: string
}

export interface GradeBoundaryDto {
  id: string
  createdBy: string
  dateCreated: string
  status: "Unapproved" | "Approved" | "Rejected"
  isDeleted: boolean
  examLevel: string
  subjectCode: string
  grade: string
  lowerBound: number
  upperBound: number
  isActive: boolean
  approvalStatus: string
  approvedByDirector?: string
  approvedByEd?: string
  approvalDate?: string
  examYear: string
  cohort: string
  award: string
  gradeValue: number
}

export interface PaperDto {
  id: number
  paperId: string
  subjectName: string
  subjectCode: string
  examLevel: string
  paperMark: number
}

export interface CreateGradeBoundaryRequest {
  examLevel: string
  subjectCode: string
  grade: string | null
  lowerBound: number
  upperBound: number
  isActive: boolean
  approvalStatus: string
  examYear: string
  cohort: string
  award: string
  gradeValue: number | null
}

export interface UpdateGradeBoundaryRequest {
  examLevel?: string
  subjectCode?: string
  grade?: string | null
  lowerBound?: number
  upperBound?: number
  isActive?: boolean
  approvalStatus?: string
  examYear?: string
  cohort?: string
  award?: string
  gradeValue?: number | null
}

export interface GradeBoundaryFilters {
  examLevel?: string
  subjectCode?: string
}

export interface Candidate {
  candidateId: number
  examType?: string
  district?: string
  center?: string
  centerNumber?: string
  candidateNumber?: string
  subject?: string
  firstName?: string
  otherNames?: string
  lastName?: string
  cohortId?: string
  scripts?: Script[]
}

export interface Script {
  scriptId: number
  candidateId?: number
  subject?: string
  scriptNumber?: string
  examSession?: string
  createdAt?: Date
  candidate?: Candidate
  scoreEntries?: ScoreEntry[]
}

export interface ScoreEntry {
  scoreEntryId: number
  paperId?: number
  enteredByUserId?: string // UUID
  scoreType?: string
  score?: number
  correctionNote?: string
  examinationNumber?: string
  timestamp?: Date
  verifiedByUserId?: string // UUID
  verifiedAt?: Date
  approvedByUserId?: string // UUID
  approvedAt?: Date
  finalApprovedByUserId?: string // UUID
  finalApprovedAt?: Date
  status?: string
  script?: Script
}

export interface ScoreEntryDto {
  scriptId: number
  userId: string // UUID - current authenticated user ID
  score: number
  scoreType: string // Required score type
  correctionNote?: string // Optional correction notes
  timestamp: Date // Auto-generated submission time
  status: string // Initial status (e.g., "pending")
  verifiedByUserId?: string // UUID - initially null/undefined
  enteredByUserId: string // UUID - current user ID (same as userId)
  verifiedAt?: Date // Initially null/undefined
  approvedByUserId?: string // UUID - initially null/undefined
  approvedAt?: Date // Initially null/undefined
  finalApprovedByUserId?: string // UUID - initially null/undefined
  finalApprovedAt?: Date // Initially null/undefined
}

export interface CreateScoreEntryRequest {
  paperId: string // Required - Paper/Script identifier
  examinationNumber: string // Required - Student examination number
  userId: string // UUID
  score: number | null // Allow null for absent/missing students
  scoreType: string // Required score type
  correctionNote?: string
  studentStatus?: 'Present' | 'Absent' | 'Missing' // Optional - Student attendance status
  // Additional required parameters for querying back
  centerNo: string // Required - Center number
  candidateId: string // Required - Candidate ID
  schoolId: string // Required - School ID
  examYear: number // Required - Examination year
}

/**
 * Score Entry DTO for the new /scoreentry endpoint
 */
export interface ScoreEntryApiDto {
  id: string
  createdBy: string
  dateCreated: string
  status: 'Unapproved' | 'Approved' | 'Verified' | 'Rejected'
  isDeleted: boolean
  scoreType: 'Initial' | 'Verify' | 'Remark' | 'Final' | 'Regression' | 'Absent'
  score: number
  correctionNote: string
  verifiedByUserId: string | null
  verifiedAt: string | null
  approvedByUserId: string | null
  approvedAt: string | null
  finalApprovedByUserId: string | null
  finalApprovedAt: string | null
  paperId: string
  examinationNumber: string
  candidateId: string
  award: string
  studentStatus: 'Present' | 'Absent' | 'Missing'
  // Additional required parameters for querying back
  centerNo: string
  schoolId: string
  examYear: number
}

export interface UpdateScoreEntryRequest {
  scriptId?: number
  userId?: string // UUID
  score?: number
  scoreType?: string
  correctionNote?: string
  status?: string
}

export interface ScoreApprovalRequest {
  scriptId: number
  userId: string // UUID
  level: string
}

export interface ScoreVerificationRequest {
  scriptId: number
  userId: string // UUID
  score: number
  scoreType?: string
  correctionNote?: string
}

// Score Types enum based on MANEB requirements
export enum ScoreType {
  FIRST = 'first',
  VERIFY = 'verify', 
  REMARK = 'remark',
  FINAL = 'final',
  REGRESSION = 'regression',
  ABSENT = 'absent'
}

// Score Status enum
export enum ScoreStatus {
  PENDING = 'pending',
  VERIFIED = 'verified',
  APPROVED = 'approved',
  FINAL_APPROVED = 'final_approved',
  REJECTED = 'rejected'
}

// Approval Status enum for Grade Boundaries
export enum ApprovalStatus {
  PENDING = 'pending',
  APPROVED = 'approved',
  REJECTED = 'rejected'
}

// Exam Levels enum - use centralized exam types
export enum ExamLevel {
  PLCE = 'PLCE',
  JCE = 'JCE',
  MSCE = 'MSCE',
  TTC = 'TTC',
  PSLCE = 'PSLCE'
}

// Grade Letters enum
export enum GradeLetter {
  A = 'A',
  B = 'B',
  C = 'C',
  D = 'D',
  F = 'F'
}

// API Response wrapper
export interface ApiResponse<T> {
  data: T
  success: boolean
  message?: string
  errors?: string[]
}

// Pagination interface
export interface PaginatedResponse<T> {
  data: T[]
  totalCount: number
  pageNumber: number
  pageSize: number
  totalPages: number
}

// Error response interface
export interface ApiError {
  message: string
  statusCode: number
  details?: string
  timestamp: Date
}

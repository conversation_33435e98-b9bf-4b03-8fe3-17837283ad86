/**
 * @fileoverview Score entry form interfaces for MANEB Results Management System
 * @description Standardized interfaces for score entry form data and validation
 */

import type { ScoreType, ScoreStatus } from '../common/exam-types.interface';
import type { ValidationResult } from '../common/base.interface';

/**
 * Score entry form data (Internal EMS)
 * @description Form data structure for entering/editing scores
 */
export interface ScoreEntryFormData {
  /** Student ID */
  studentId: string;
  /** Subject ID */
  subjectId: string;
  /** Academic year */
  year: number | '';
  /** Score value */
  score: number | null;
  /** Type of score entry */
  scoreType: ScoreType | '';
  /** Optional correction notes */
  correctionNote?: string;
  /** Examination number */
  examinationNumber?: string;
  /** Current status */
  status?: ScoreStatus;
}

/**
 * External score entry form data (External Grading API)
 * @description Form data structure for external grading system
 */
export interface ExternalScoreEntryFormData {
  /** Paper code identifier */
  paperId: string;
  /** Examination number */
  examinationNumber: string;
  /** User ID */
  userId: string;
  /** Score value */
  score: number;
  /** Score type */
  scoreType: string;
  /** Optional correction notes */
  correctionNote?: string;
}

/**
 * Bulk score entry form data
 * @description Form data for entering multiple scores at once
 */
export interface BulkScoreEntryFormData {
  /** Subject ID */
  subjectId: string;
  /** Academic year */
  year: number | '';
  /** Score type */
  scoreType: ScoreType | '';
  /** Array of individual score entries */
  scores: Array<{
    /** Student ID or examination number */
    studentIdentifier: string;
    /** Score value */
    score: number | null;
    /** Optional notes */
    notes?: string;
  }>;
  /** General notes for all entries */
  generalNotes?: string;
}

/**
 * Score verification form data
 * @description Form data for score verification process
 */
export interface ScoreVerificationFormData {
  /** Score entry ID */
  scoreEntryId: string;
  /** Verified score value */
  verifiedScore: number;
  /** Verification notes */
  verificationNotes?: string;
  /** Whether score is approved */
  isApproved: boolean;
  /** Verification action */
  action: 'approve' | 'reject' | 'request_remark';
}

/**
 * Score approval form data
 * @description Form data for score approval process
 */
export interface ScoreApprovalFormData {
  /** Score entry ID */
  scoreEntryId: string;
  /** Approval level */
  approvalLevel: 'first' | 'final';
  /** Approval notes */
  approvalNotes?: string;
  /** Whether to approve or reject */
  action: 'approve' | 'reject';
  /** Reason for rejection (if applicable) */
  rejectionReason?: string;
}

/**
 * Score entry form validation rules
 * @description Validation configuration for score entry forms
 */
export interface ScoreEntryFormValidation {
  /** Required fields */
  required: (keyof ScoreEntryFormData)[];
  /** Score range validation */
  scoreRange: {
    min: number;
    max: number;
  };
  /** Year range validation */
  yearRange: {
    min: number;
    max: number;
  };
  /** Maximum length for notes */
  maxNoteLength: number;
  /** Custom validation rules */
  customRules?: {
    /** Rule name */
    name: string;
    /** Validation function */
    validator: (data: ScoreEntryFormData) => boolean;
    /** Error message */
    message: string;
  }[];
}

/**
 * Score entry form errors
 * @description Error state for score entry forms
 */
export interface ScoreEntryFormErrors {
  /** Student error */
  studentId?: string;
  /** Subject error */
  subjectId?: string;
  /** Year error */
  year?: string;
  /** Score error */
  score?: string;
  /** Score type error */
  scoreType?: string;
  /** Correction note error */
  correctionNote?: string;
  /** Examination number error */
  examinationNumber?: string;
  /** General form error */
  general?: string;
}

/**
 * Score entry form state
 * @description Complete form state including data, errors, and status
 */
export interface ScoreEntryFormState {
  /** Form data */
  data: ScoreEntryFormData;
  /** Form errors */
  errors: ScoreEntryFormErrors;
  /** Whether form is being submitted */
  isSubmitting: boolean;
  /** Whether form has been touched */
  isTouched: boolean;
  /** Whether form is valid */
  isValid: boolean;
  /** Validation warnings */
  warnings: string[];
  /** Current step in multi-step form */
  currentStep?: number;
  /** Total steps in multi-step form */
  totalSteps?: number;
}

/**
 * Score entry form props
 * @description Props interface for score entry form components
 */
export interface ScoreEntryFormProps {
  /** Whether modal/form is open */
  isOpen: boolean;
  /** Existing score data for editing */
  scoreEntry?: any | null;
  /** Pre-selected student */
  preSelectedStudent?: string | null;
  /** Pre-selected subject */
  preSelectedSubject?: string | null;
  /** Pre-selected year */
  preSelectedYear?: number | null;
  /** Whether form is in edit mode */
  isEditing?: boolean;
  /** Form validation rules */
  validationRules?: ScoreEntryFormValidation;
  /** Whether to show advanced options */
  showAdvancedOptions?: boolean;
}

/**
 * Score entry form events
 * @description Event interface for score entry form components
 */
export interface ScoreEntryFormEvents {
  /** Form close event */
  close: [];
  /** Form success event */
  success: [studentId?: string];
  /** Form error event */
  error: [error: string];
  /** Form validation event */
  validate: [result: ValidationResult];
  /** Form data change event */
  change: [data: ScoreEntryFormData];
  /** Score auto-submit event */
  autoSubmit: [data: ScoreEntryFormData];
}

/**
 * Score entry filter form data
 * @description Form data for filtering score entries
 */
export interface ScoreEntryFilterFormData {
  /** Search query */
  searchQuery?: string;
  /** Subject filter */
  subjectId?: string | 'All';
  /** Year filter */
  year?: number | 'All';
  /** Score type filter */
  scoreType?: ScoreType | 'All';
  /** Status filter */
  status?: ScoreStatus | 'All';
  /** Score range filter */
  scoreRange?: {
    min?: number;
    max?: number;
  };
  /** Date range filter */
  dateRange?: {
    from?: Date;
    to?: Date;
  };
  /** Sorting options */
  sortBy?: 'score' | 'date' | 'student' | 'subject';
  /** Sort direction */
  sortDirection?: 'asc' | 'desc';
}

/**
 * Score entry import form data
 * @description Form data for importing scores from file
 */
export interface ScoreEntryImportFormData {
  /** File to import */
  file: File | null;
  /** File format */
  format: 'csv' | 'excel' | 'json';
  /** Subject ID for all scores */
  subjectId: string;
  /** Academic year for all scores */
  year: number;
  /** Score type for all scores */
  scoreType: ScoreType;
  /** Whether to overwrite existing scores */
  overwriteExisting: boolean;
  /** Whether to validate before import */
  validateFirst: boolean;
  /** Column mapping for CSV/Excel */
  columnMapping?: Record<string, string>;
  /** Import options */
  options?: {
    /** Skip header row */
    skipHeader?: boolean;
    /** Delimiter for CSV */
    delimiter?: string;
    /** Sheet name for Excel */
    sheetName?: string;
    /** Default score for missing values */
    defaultScore?: number;
  };
}

/**
 * Score entry export form data
 * @description Form data for exporting scores
 */
export interface ScoreEntryExportFormData {
  /** Export format */
  format: 'csv' | 'excel' | 'json' | 'pdf';
  /** Filters for export */
  filters: ScoreEntryFilterFormData;
  /** Export options */
  options?: {
    /** Include headers */
    includeHeaders?: boolean;
    /** Include student names */
    includeStudentNames?: boolean;
    /** Include subject names */
    includeSubjectNames?: boolean;
    /** Include calculated grades */
    includeCalculatedGrades?: boolean;
    /** File name */
    fileName?: string;
  };
}

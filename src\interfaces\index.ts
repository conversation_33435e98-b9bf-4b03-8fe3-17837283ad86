/**
 * @fileoverview Main interfaces index for MANEB Results Management System
 * @description Centralized export of all standardized interfaces
 * @version 2.0.0 - Standardized naming conventions and comprehensive type system
 */

// ============================================================================
// COMMON INTERFACES AND TYPES
// ============================================================================

// Base interfaces and common types
export * from './common';

// ============================================================================
// TABLE/DATA MODEL INTERFACES
// ============================================================================

// All table/entity interfaces
export * from './tables';



// ============================================================================
// FORM INTERFACES
// ============================================================================

// All form-related interfaces
export * from './forms';

// ============================================================================
// EXTERNAL API INTERFACES
// ============================================================================

// External grading API interfaces - specific exports to avoid conflicts
export type {
  AuditLog,
  GradeBoundaryDto as ExternalGradeBoundaryDto,
  PaperDto as ExternalPaperDto,
  CreateGradeBoundaryRequest as ExternalCreateGradeBoundaryRequest,
  UpdateGradeBoundaryRequest as ExternalUpdateGradeBoundaryRequest,
  GradeBoundaryFilters as ExternalGradeBoundaryFilters,
  Candidate,
  Script,
  ScoreEntry as ExternalScoreEntry,
  ScoreEntryDto as ExternalScoreEntryDto,
  CreateScoreEntryRequest as ExternalCreateScoreEntryRequest,
  UpdateScoreEntryRequest as ExternalUpdateScoreEntryRequest,
  ScoreApprovalRequest,
  ScoreVerificationRequest
} from './external/results-api.interface';

// Re-export standardized UserDto and related types for backward compatibility
export type { UserDto, UserEntity } from './tables/user.interface';
export type { CreateRequest, UpdateRequest } from './common/base.interface';

// ============================================================================
// LEGACY INTERFACES (DEPRECATED - Use new standardized interfaces above)
// ============================================================================

/**
 * @deprecated Use ApiResponse from './common/base.interface' instead
 * Legacy API response interface - maintained for backward compatibility
 */
export interface LegacyApiResponse<T = any> {
  data?: T;
  message?: string;
  success: boolean;
  errors?: string[];
}

/**
 * @deprecated Use standardized authentication interfaces instead
 * Legacy authentication interfaces - maintained for backward compatibility
 */
export interface LoginModel {
  email: string;
  password: string;
  rememberMe?: boolean;
}

export interface AuthResponse {
  token?: string;
  accessToken?: string;
  access_token?: string;
  jwt?: string;
  refreshToken?: string;
  refresh_token?: string;
  user?: import('./tables/user.interface').UserDto;
  expiresAt?: string;
}

export interface RefreshTokenRequest {
  refreshToken: string;
}

/**
 * @deprecated Use Gender from './common/base.interface' instead
 * Legacy gender type - maintained for backward compatibility only
 */
export type LegacyGender = 'Male' | 'Female' | 'Other';

/**
 * @deprecated Use UserDto from './tables/user.interface' instead
 * Legacy user DTO interface - maintained for backward compatibility only
 * This interface is being phased out in favor of the standardized UserDto
 */
export interface LegacyUserDto {
  id?: string;
  createdBy?: string;
  dateCreated?: Date | string;
  status?: RecordStatus;
  isDeleted?: boolean;
  firstName: string;
  lastName: string;
  gender?: LegacyGender;
  idNumber?: string;
  idType?: string;
  dateOfBirth?: Date | string;
  email?: string;
  userName?: string;
  fullName?: string;
  roleId?: string;
  role?: RoleDto;
  roles?: RoleDto[];
}

/**
 * @deprecated Use CreateRequest<UserEntity> from './common/base.interface' instead
 * Legacy create user request interface - maintained for backward compatibility only
 */
export interface LegacyCreateUserRequest extends Omit<LegacyUserDto, 'id' | 'createdBy' | 'dateCreated' | 'fullName'> {
  /** Password for new user account */
  password?: string;
}

// Re-export standardized request types for backward compatibility
export type CreateUserRequest = import('./common/base.interface').CreateRequest<import('./tables/user.interface').UserEntity> & {
  /** Password for new user account */
  password?: string;
};
export type UpdateUserRequest = import('./common/base.interface').UpdateRequest<import('./tables/user.interface').UserEntity>;

/**
 * @deprecated Use ApiClientConfig from './common/base.interface' instead
 * Legacy API client types - maintained for backward compatibility
 */
export interface LegacyApiClientConfig {
  baseURL: string;
  timeout?: number;
}

export interface LegacyRequestConfig {
  headers?: Record<string, string>;
  params?: Record<string, any>;
}

// Table UI interfaces are now exported through ./tables/index.ts

/**
 * Legacy grade DTO interface - maintained for backward compatibility
 */
export interface GradeDto {
  id?: string;
  createdBy?: string;
  dateCreated?: Date;
  isDeleted?: boolean;
  studentId: string;
  subjectId: string;
  year: number;
  startScore: number;
  endScore: number;
  assignedGrade: string; // Changed from GradeLevel to string for legacy compatibility
  failResult: boolean;
}

/**
 * Legacy DTOs - maintained for backward compatibility
 */
export interface SubjectDto {
  id?: string;
  createdBy?: string;
  dateCreated?: Date;
  isDeleted?: boolean;
  name: string;
  code: string;
  description?: string;
  isActive: boolean;
}

export interface PaperDto {
  id?: string;
  createdBy?: string;
  dateCreated?: Date;
  isDeleted?: boolean;
  subjectId: string;
  name: string;
  code: string;
  description?: string;
  paperNumber: number;
  duration?: number;
  maxMarks?: number;
  isActive: boolean;
}

export interface GradeBoundaryDto {
  id?: string;
  createdBy?: string;
  dateCreated?: Date;
  isDeleted?: boolean;
  examType: string; // Changed from ExamType to string for legacy compatibility
  subjectId: string;
  year: number;
  gradeLevel: string; // Changed from GradeLevel to string for legacy compatibility
  minScore: number;
  maxScore: number;
  description?: string;
  isActive: boolean;
}

export interface GradeConfigurationDto {
  id?: string;
  createdBy?: string;
  dateCreated?: Date;
  isDeleted?: boolean;

  // Configuration details
  year: number;
  isActive: boolean;
  description?: string;
  gradeBoundaries: GradeBoundaryDto[];
}

// Request types for grading system
export interface CreateGradeRequest extends Omit<GradeDto, 'id' | 'createdBy' | 'dateCreated'> {}
export interface UpdateGradeRequest extends Partial<CreateGradeRequest> {}

export interface CreateSubjectRequest extends Omit<SubjectDto, 'id' | 'createdBy' | 'dateCreated'> {}
export interface UpdateSubjectRequest extends Partial<CreateSubjectRequest> {}

export interface CreatePaperRequest extends Omit<PaperDto, 'id' | 'createdBy' | 'dateCreated'> {}
export interface UpdatePaperRequest extends Partial<CreatePaperRequest> {}

export interface CreateGradeBoundaryRequest extends Omit<GradeBoundaryDto, 'id' | 'createdBy' | 'dateCreated'> {}
export interface UpdateGradeBoundaryRequest extends Partial<CreateGradeBoundaryRequest> {}

export interface CreateGradeConfigurationRequest extends Omit<GradeConfigurationDto, 'id' | 'createdBy' | 'dateCreated'> {}
export interface UpdateGradeConfigurationRequest extends Partial<CreateGradeConfigurationRequest> {}

/**
 * Legacy comprehensive grade boundary interfaces - maintained for backward compatibility
 */
export interface GradeBoundarySet {
  gradeLevel: string; // Changed from GradeLevel to string for legacy compatibility
  minScore: number;
  maxScore: number;
  description?: string;
}

export interface ComprehensiveGradeBoundaryRequest {
  examType: string; // Changed from ExamType to string for legacy compatibility
  subjectId: string;
  year: number;
  gradeBoundaries: GradeBoundarySet[];
  isActive?: boolean;
}

export interface BulkGradeBoundaryResponse {
  success: boolean;
  createdBoundaries: GradeBoundaryDto[];
  errors?: string[];
}

/**
 * Legacy results interfaces - maintained for backward compatibility
 */
export interface StudentResultDto {
  id?: string;
  studentId: string;
  subjectId: string;
  year: number;
  score: number;
  calculatedGrade: string; // Changed from GradeLevel to string for legacy compatibility
  passStatus: boolean;
  dateCalculated?: Date;
}

/**
 * Legacy certificate interfaces - maintained for backward compatibility
 */
export interface CertificateSubjectResult {
  subjectId: string;
  subjectName: string;
  subjectCode: string;
  papers: CertificatePaperResult[];
  overallGrade: string; // Changed from GradeLevel to string for legacy compatibility
  overallPassStatus: boolean;
}

export interface CertificatePaperResult {
  paperId: string;
  paperName: string;
  score: number;
  grade: string; // Changed from GradeLevel to string for legacy compatibility
  passStatus: boolean;
}

export interface StudentCertificateDto {
  studentId: string;
  studentName?: string;
  examNumber?: string;
  year: number;
  examSession: string;
  subjects: CertificateSubjectResult[];
  overallPerformance: {
    totalSubjects: number;
    passedSubjects: number;
    failedSubjects: number;
    overallGrade: string; // Changed from GradeLevel to string for legacy compatibility
    passRate: number;
  };
  certificateNumber: string;
  issueDate: Date;
  issuedBy: string;
}

export interface ResultsFilterDto {
  searchQuery?: string;
  year?: number | 'All';
  subjectId?: string | 'All';
  paperId?: string | 'All';
  gradeLevel?: string | 'All'; // Changed from GradeLevel to string for legacy compatibility
  passStatus?: boolean | 'All';
}

/**
 * @deprecated Use appropriate interfaces from './tables' instead
 * Legacy examination results interfaces - maintained for backward compatibility
 */
export interface ExaminationResultDto {
  id?: string;
  studentId: string;
  examNumber?: string;
  level: string; // Changed from ExamType to string for legacy compatibility
  year: number;
  averageScore: number;
  averageGrade: string; // Changed from GradeLevel to string for legacy compatibility
  totalSubjects: number;
  passedSubjects: number;
  failedSubjects: number;
  passRate: number;
  status: 'Complete' | 'Incomplete' | 'Pending';
  dateCalculated?: Date;
  subjects: StudentSubjectResult[];
}

export interface StudentSubjectResult {
  subjectId: string;
  subjectName: string;
  subjectCode: string;
  papers: StudentPaperResult[];
  averageScore: number;
  overallGrade: string; // Changed from GradeLevel to string for legacy compatibility
  passStatus: boolean;
}

export interface StudentPaperResult {
  paperId: string;
  paperName: string;
  score: number;
  grade: string; // Changed from GradeLevel to string for legacy compatibility
  passStatus: boolean;
  scoreType: string; // Changed from ScoreType to string for legacy compatibility
}

export interface ExaminationResultsFilterDto {
  searchQuery?: string;
  year?: number | 'All';
  level?: string | 'All'; // Changed from ExamType to string for legacy compatibility
  gradeLevel?: string | 'All'; // Changed from GradeLevel to string for legacy compatibility
  passStatus?: boolean | 'All';
  status?: 'Complete' | 'Incomplete' | 'Pending' | 'All';
}

// User Management System Types (Based on actual API)
export type RecordStatus = 'Unapproved' | 'Approved' | 'SecondApproved' | 'Rejected';
export type UserStatus = RecordStatus; // Alias for backward compatibility
export type PermissionAction = 'Login' | 'Logout' | 'Update' | 'Delete' | 'Authorise' | 'Approve' | 'Reject' | 'SecondApprove' | 'All' | 'View' | 'ViewAll' | 'Create' | 'Check' | 'Verify' | 'Active';

// User-Role Assignment interfaces - New for API alignment
export interface UserRolesDto {
  userId: string;
  roleId: string;
  userName?: string;
  userEmail?: string;
  roleName?: string;
}

export interface AssignRoleDto {
  userId: string;
  roleId: string;
}

export interface RoleDto {
  id?: string;
  createdBy?: string;
  dateCreated?: Date | string; // Can be Date object or ISO string
  status?: RecordStatus;
  isDeleted?: boolean;

  // Role details
  name: string;
  description?: string; // Added for backward compatibility
  isActive?: boolean; // Added for backward compatibility
  rolePermissions?: RolePermissionDto[];
}

export interface CreateRoleRequest extends Omit<RoleDto, 'id' | 'createdBy' | 'dateCreated' | 'rolePermissions'> {}

export interface UpdateRoleRequest extends Partial<CreateRoleRequest> {}

// Role Permission Management (Junction table)
export interface RolePermissionDto {
  id?: string;
  createdBy?: string;
  dateCreated?: Date | string; // Can be Date object or ISO string
  status?: RecordStatus;
  isDeleted?: boolean;

  // Permission details
  sectionID: string; // Required - references Section
  roleId: string; // Required - references Role
  action: PermissionAction;
  canAccess: boolean;
  section?: SectionDto | null; // Optional section object (can be null in API response)
}

export interface CreateRolePermissionRequest extends Omit<RolePermissionDto, 'id' | 'createdBy' | 'dateCreated'> {}

export interface UpdateRolePermissionRequest extends Partial<CreateRolePermissionRequest> {}

// Section Management (Organizational Structure)
export interface SectionDto {
  id?: string;
  createdBy?: string;
  dateCreated?: Date;
  status?: RecordStatus;
  isDeleted?: boolean;

  // Section details
  name: string;
  controller?: string; // API controller name
  area?: string; // API area name
  description?: string; // Section description
  code?: string; // Section code
  parentSectionId?: string; // Parent section ID for hierarchy
  level?: number; // Hierarchy level
  isActive?: boolean; // Whether section is active
  userCount?: number; // Number of users in this section
  childSections?: SectionDto[]; // Child sections for hierarchy
}

export interface CreateSectionRequest extends Omit<SectionDto, 'id' | 'createdBy' | 'dateCreated'> {}

export interface UpdateSectionRequest extends Partial<CreateSectionRequest> {}

// Filter and Search Types
export interface RoleFilterDto {
  searchQuery?: string;
  status?: RecordStatus | 'All';
  hasPermissions?: boolean | 'All';
  isActive?: boolean | 'All'; // Added for backward compatibility
}

export interface RolePermissionFilterDto {
  searchQuery?: string;
  roleId?: string | 'All';
  sectionId?: string | 'All';
  action?: PermissionAction | 'All';
  canAccess?: boolean | 'All';
  status?: RecordStatus | 'All';
}

export interface SectionFilterDto {
  searchQuery?: string;
  controller?: string | 'All';
  area?: string | 'All';
  status?: RecordStatus | 'All';
  parentSectionId?: string | 'All';
  level?: number | 'All';
  isActive?: boolean | 'All';
  hasUsers?: boolean | 'All';
}

// Workspace Management
export interface WorkspaceDto {
  id?: string;
  createdBy?: string;
  dateCreated?: Date;
  status?: RecordStatus;
  isDeleted?: boolean;

  // Workspace details
  name: string;
  description?: string;
  ownerId: string; // User ID of workspace owner
  owner?: import('./tables/user.interface').UserDto; // Populated owner information
  isActive: boolean;
  memberCount?: number; // Number of users in workspace
  settings?: WorkspaceSettings;
}

export interface WorkspaceSettings {
  allowPublicAccess?: boolean;
  requireApprovalForJoin?: boolean;
  maxMembers?: number;
  defaultRole?: string;
}

export interface CreateWorkspaceRequest extends Omit<WorkspaceDto, 'id' | 'createdBy' | 'dateCreated' | 'owner' | 'memberCount'> {}

export interface UpdateWorkspaceRequest extends Partial<CreateWorkspaceRequest> {}

export interface WorkspaceFilterDto {
  searchQuery?: string;
  ownerId?: string | 'All';
  isActive?: boolean | 'All';
  status?: RecordStatus | 'All';
  memberCount?: 'None' | 'Some' | 'Many' | 'All';
}

// Audit Management
export type AuditAction = 'Login' | 'Logout' | 'Create' | 'Update' | 'Delete' | 'View' | 'Export' | 'Import' | 'Approve' | 'Reject' | 'StatusChange' | 'RoleAssign' | 'PermissionChange';
export type AuditStatus = 'Success' | 'Failed' | 'Warning' | 'Info';

export interface AuditDto {
  id?: string;
  createdBy?: string;
  dateCreated?: Date;
  isDeleted?: boolean;

  // Audit details
  userId: string; // User who performed the action
  user?: import('./tables/user.interface').UserDto; // Populated user information
  action: AuditAction;
  entityType: string; // Type of entity affected (User, Role, Section, etc.)
  entityId?: string; // ID of the affected entity
  entityName?: string; // Name/description of affected entity
  description: string; // Detailed description of the action
  status: AuditStatus;
  ipAddress?: string;
  userAgent?: string;
  sessionId?: string;
  additionalData?: Record<string, any>; // Additional context data
}

export interface CreateAuditRequest extends Omit<AuditDto, 'id' | 'createdBy' | 'dateCreated' | 'user'> {}

export interface UpdateAuditRequest extends Partial<CreateAuditRequest> {}

export interface AuditFilterDto {
  searchQuery?: string;
  userId?: string | 'All';
  action?: AuditAction | 'All';
  entityType?: string | 'All';
  status?: AuditStatus | 'All';
  dateFrom?: Date;
  dateTo?: Date;
  ipAddress?: string;
}

// Bulk operations
export interface BulkRolePermissionRequest {
  permissions: CreateRolePermissionRequest[];
}

export interface BulkRolePermissionAssignRequest {
  roleId: string;
  permissions: CreateRolePermissionRequest[];
}

export interface BulkRolePermissionRemoveRequest {
  roleId: string;
  permissionIds?: string[];
  sectionIds?: string[];
}

export interface BulkRolePermissionUpdateRequest {
  updates: Array<{
    id: string;
    data: UpdateRolePermissionRequest;
  }>;
}

export interface RolePermissionValidationResult {
  valid: CreateRolePermissionRequest[];
  invalid: Array<{
    permission: CreateRolePermissionRequest;
    errors: string[];
  }>;
}

export interface AssignRoleToUserRequest {
  userId: string;
  roleId: string;
}
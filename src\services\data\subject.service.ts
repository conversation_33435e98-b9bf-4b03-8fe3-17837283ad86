/**
 * @fileoverview Subject Service for MANEB Results Management System
 * @description Service for fetching subjects from the API
 */

import apiClient from '../core/api-client';

/**
 * Subject interface matching the API response
 */
export interface SubjectDto {
  id: string;
  examTypeId: string;
  name: string;
  shortName: string;
  isActive: boolean;
  createdOn: string;
  createdBy: string;
  modifiedOn: string | null;
  modifiedBy: string | null;
  manebNumericId: number;
  examType: {
    id: string;
    name: string;
    isActive: boolean;
    numericId: number;
    configurationJson: string | null;
    createdOn: string;
    createdBy: string;
    modifiedOn: string | null;
    modifiedBy: string | null;
  };
}

/**
 * API Response interface for paginated subjects
 */
export interface SubjectResponse {
  totalCount: number;
  pageNumber: number;
  pageSize: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
  items: SubjectDto[];
}

/**
 * Simplified subject option for dropdowns
 */
export interface SubjectOption {
  id: string;
  name: string;
  shortName: string;
  examTypeId: string;
  examTypeName: string;
  isActive: boolean;
}

/**
 * Subject search filters
 */
export interface SubjectFilters {
  examTypeId?: string;
  search?: string;
  activeOnly?: boolean;
}

/**
 * Subject Service
 * @description Handles all subject related API operations
 */
export class SubjectService {
  private static instance: SubjectService;
  private cachedSubjects: SubjectDto[] | null = null;
  private lastFetchTime: number = 0;
  private readonly CACHE_DURATION = 10 * 60 * 1000; // 10 minutes

  private constructor() {}

  /**
   * Get singleton instance
   */
  static getInstance(): SubjectService {
    if (!SubjectService.instance) {
      SubjectService.instance = new SubjectService();
    }
    return SubjectService.instance;
  }

  /**
   * Fetch subjects from API with optional filters
   */
  async fetchSubjects(filters: {
    examTypeId?: string;
    pageSize?: number;
    search?: string;
    forceRefresh?: boolean;
  } = {}): Promise<SubjectResponse> {
    const now = Date.now();
    const cacheKey = JSON.stringify(filters);

    // Return cached data if available and not expired (unless force refresh)
    if (!filters.forceRefresh && this.cachedSubjects && (now - this.lastFetchTime) < this.CACHE_DURATION) {
      // For filtered requests, we still need to filter the cached data
      if (filters.examTypeId || filters.search) {
        const filteredSubjects = this.filterSubjects(this.cachedSubjects, filters);
        return {
          totalCount: filteredSubjects.length,
          pageNumber: 1,
          pageSize: filters.pageSize || filteredSubjects.length,
          totalPages: 1,
          hasNextPage: false,
          hasPreviousPage: false,
          items: filteredSubjects
        };
      }

      return {
        totalCount: this.cachedSubjects.length,
        pageNumber: 1,
        pageSize: filters.pageSize || this.cachedSubjects.length,
        totalPages: 1,
        hasNextPage: false,
        hasPreviousPage: false,
        items: this.cachedSubjects
      };
    }

    try {
      console.log('🔍 Fetching subjects from API with filters:', filters);

      // Build query parameters
      const params = new URLSearchParams();
      if (filters.examTypeId) {
        params.append('ExamTypeId', filters.examTypeId);
      }
      if (filters.search) {
        params.append('Search', filters.search);
      }
      if (filters.pageSize) {
        params.append('PageSize', filters.pageSize.toString());
      } else {
        params.append('PageSize', '200'); // Default to large page size
      }

      const url = `/api/subject${params.toString() ? `?${params.toString()}` : ''}`;
      const response: SubjectResponse = await apiClient.get(url);

      console.log('📥 Subject API response:', response);

      // Handle paginated response format
      if (!response) {
        console.error('No response from subject API');
        return {
          totalCount: 0,
          pageNumber: 1,
          pageSize: filters.pageSize || 200,
          totalPages: 0,
          hasNextPage: false,
          hasPreviousPage: false,
          items: []
        };
      }

      if (!response.items || !Array.isArray(response.items)) {
        console.error('Invalid subject API response - missing or invalid items array:', response);
        return {
          totalCount: 0,
          pageNumber: 1,
          pageSize: filters.pageSize || 200,
          totalPages: 0,
          hasNextPage: false,
          hasPreviousPage: false,
          items: []
        };
      }

      // Cache the results if no specific filters (cache the full dataset)
      if (!filters.examTypeId && !filters.search) {
        this.cachedSubjects = response.items;
        this.lastFetchTime = now;
        console.log('✅ Subjects cached:', this.cachedSubjects.length, 'items');
      }

      return response;
    } catch (error) {
      console.error('Error fetching subjects:', error);

      // Return cached data if available, even if expired
      if (this.cachedSubjects) {
        console.warn('Using cached subjects due to API error');
        const filteredSubjects = this.filterSubjects(this.cachedSubjects, filters);
        return {
          totalCount: filteredSubjects.length,
          pageNumber: 1,
          pageSize: filters.pageSize || filteredSubjects.length,
          totalPages: 1,
          hasNextPage: false,
          hasPreviousPage: false,
          items: filteredSubjects
        };
      }

      throw error;
    }
  }

  /**
   * Filter subjects locally (for cached data)
   */
  private filterSubjects(subjects: SubjectDto[], filters: {
    examTypeId?: string;
    search?: string;
  }): SubjectDto[] {
    return subjects.filter(subject => {
      if (filters.examTypeId && subject.examTypeId !== filters.examTypeId) {
        return false;
      }

      if (filters.search) {
        const searchLower = filters.search.toLowerCase();
        return (
          subject.name.toLowerCase().includes(searchLower) ||
          subject.shortName.toLowerCase().includes(searchLower) ||
          subject.id.toLowerCase().includes(searchLower)
        );
      }

      return true;
    });
  }

  /**
   * Get active subjects only
   */
  async getActiveSubjects(): Promise<SubjectDto[]> {
    const response = await this.fetchSubjects();
    return response.items.filter(subject => subject.isActive);
  }

  /**
   * Get subjects as dropdown options
   */
  async getSubjectOptions(activeOnly: boolean = true): Promise<SubjectOption[]> {
    const subjects = activeOnly ? await this.getActiveSubjects() : (await this.fetchSubjects()).items;

    return subjects.map(subject => ({
      id: subject.id,
      name: subject.name,
      shortName: subject.shortName,
      examTypeId: subject.examTypeId,
      examTypeName: subject.examType?.name || subject.examTypeId,
      isActive: subject.isActive
    }));
  }

  /**
   * Get subjects filtered by criteria
   */
  async getFilteredSubjects(filters: SubjectFilters): Promise<SubjectDto[]> {
    const response = await this.fetchSubjects({
      examTypeId: filters.examTypeId,
      search: filters.search,
      pageSize: 200
    });

    let subjects = response.items;

    if (filters.activeOnly !== false) {
      subjects = subjects.filter(subject => subject.isActive);
    }

    return subjects;
  }

  /**
   * Search subjects by query
   */
  async searchSubjects(query: string): Promise<SubjectDto[]> {
    return this.getFilteredSubjects({ search: query });
  }

  /**
   * Get subjects by exam type
   */
  async getSubjectsByExamType(examTypeId: string): Promise<SubjectDto[]> {
    return this.getFilteredSubjects({ examTypeId });
  }

  /**
   * Get subject by ID
   */
  async getSubjectById(id: string): Promise<SubjectDto | undefined> {
    const response = await this.fetchSubjects();
    return response.items.find(subject => subject.id === id);
  }

  /**
   * Get subject name by ID
   */
  async getSubjectName(id: string): Promise<string> {
    const subject = await this.getSubjectById(id);
    return subject?.name || id;
  }

  /**
   * Get unique exam types from subjects
   */
  async getExamTypes(): Promise<Array<{id: string, name: string}>> {
    const subjects = await this.getActiveSubjects();
    const examTypesMap = new Map<string, string>();

    subjects.forEach(subject => {
      if (subject.examType) {
        examTypesMap.set(subject.examType.id, subject.examType.name);
      }
    });

    return Array.from(examTypesMap.entries()).map(([id, name]) => ({ id, name }));
  }

  /**
   * Clear cache (useful for testing or forced refresh)
   */
  clearCache(): void {
    this.cachedSubjects = null;
    this.lastFetchTime = 0;
  }
}

// Export singleton instance
export const subjectService = SubjectService.getInstance();

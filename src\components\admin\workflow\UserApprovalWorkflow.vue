<template>
  <div class="p-6 bg-white border border-gray-200 rounded-lg shadow-sm dark:bg-gray-800 dark:border-gray-700">
    <!-- Header -->
    <div class="flex items-center justify-between mb-6">
      <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
        User Approval Workflow
      </h3>
      <div class="flex items-center space-x-2">
        <span class="text-sm text-gray-500 dark:text-gray-400">Status:</span>
        <span :class="getStatusBadgeClass(user.status)" class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium">
          {{ user.status }}
        </span>
      </div>
    </div>

    <!-- User Info -->
    <div class="mb-6 p-4 bg-gray-50 rounded-lg dark:bg-gray-700">
      <div class="flex items-center space-x-4">
        <div class="flex items-center justify-center w-12 h-12 bg-gray-300 rounded-full dark:bg-gray-600">
          <span class="text-lg font-medium text-gray-600 dark:text-gray-300">
            {{ getUserInitials(user) }}
          </span>
        </div>
        <div>
          <h4 class="text-lg font-semibold text-gray-900 dark:text-white">
            {{ user.fullName || `${user.firstName} ${user.lastName}` }}
          </h4>
          <p class="text-sm text-gray-500 dark:text-gray-400">{{ user.email }}</p>
          <p class="text-xs text-gray-400 dark:text-gray-500">
            Created: {{ formatDate(user.dateCreated) }}
          </p>
        </div>
      </div>
    </div>

    <!-- Workflow Steps -->
    <div class="mb-6">
      <h4 class="text-md font-medium text-gray-900 dark:text-white mb-4">Approval Process</h4>
      
      <!-- Progress Steps -->
      <ol class="flex items-center w-full text-sm font-medium text-center text-gray-500 dark:text-gray-400 sm:text-base">
        <!-- Step 1: Unapproved -->
        <li :class="getStepClass('Unapproved')" class="flex md:w-full items-center sm:after:content-[''] after:w-full after:h-1 after:border-b after:border-gray-200 after:border-1 after:hidden sm:after:inline-block after:mx-6 xl:after:mx-10 dark:after:border-gray-700">
          <span class="flex items-center after:content-['/'] sm:after:hidden after:mx-2 after:text-gray-200 dark:after:text-gray-500">
            <svg v-if="isStepCompleted('Unapproved')" class="w-3.5 h-3.5 sm:w-4 sm:h-4 mr-2.5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
              <path d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5Zm3.707 8.207-4 4a1 1 0 0 1-1.414 0l-2-2a1 1 0 0 1 1.414-1.414L9 10.586l3.293-3.293a1 1 0 0 1 1.414 1.414Z"/>
            </svg>
            <span v-else class="mr-2">1</span>
            Submitted
          </span>
        </li>
        
        <!-- Step 2: Approved -->
        <li :class="getStepClass('Approved')" class="flex md:w-full items-center after:content-[''] after:w-full after:h-1 after:border-b after:border-gray-200 after:border-1 after:hidden sm:after:inline-block after:mx-6 xl:after:mx-10 dark:after:border-gray-700">
          <span class="flex items-center after:content-['/'] sm:after:hidden after:mx-2 after:text-gray-200 dark:after:text-gray-500">
            <svg v-if="isStepCompleted('Approved')" class="w-3.5 h-3.5 sm:w-4 sm:h-4 mr-2.5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
              <path d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5Zm3.707 8.207-4 4a1 1 0 0 1-1.414 0l-2-2a1 1 0 0 1 1.414-1.414L9 10.586l3.293-3.293a1 1 0 0 1 1.414 1.414Z"/>
            </svg>
            <span v-else class="mr-2">2</span>
            First Approval
          </span>
        </li>
        
        <!-- Step 3: Second Approved -->
        <li :class="getStepClass('SecondApproved')" class="flex items-center">
          <svg v-if="isStepCompleted('SecondApproved')" class="w-3.5 h-3.5 sm:w-4 sm:h-4 mr-2.5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
            <path d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5Zm3.707 8.207-4 4a1 1 0 0 1-1.414 0l-2-2a1 1 0 0 1 1.414-1.414L9 10.586l3.293-3.293a1 1 0 0 1 1.414 1.414Z"/>
          </svg>
          <span v-else class="mr-2">3</span>
          Final Approval
        </li>
      </ol>
    </div>

    <!-- Action Buttons -->
    <div class="space-y-4">
      <div v-if="user.status === 'Unapproved'" class="flex flex-col sm:flex-row gap-3">
        <button 
          @click="updateStatus('Approved')"
          :disabled="isLoading"
          class="flex-1 text-white bg-green-600 hover:bg-green-700 focus:ring-4 focus:outline-none focus:ring-green-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-green-600 dark:hover:bg-green-700 dark:focus:ring-green-800 disabled:opacity-50"
        >
          <svg class="w-4 h-4 mr-2 inline" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
          </svg>
          Give First Approval
        </button>
        <button 
          @click="updateStatus('Rejected')"
          :disabled="isLoading"
          class="flex-1 text-white bg-red-600 hover:bg-red-700 focus:ring-4 focus:outline-none focus:ring-red-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-red-600 dark:hover:bg-red-700 dark:focus:ring-red-800 disabled:opacity-50"
        >
          <svg class="w-4 h-4 mr-2 inline" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
            <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
          </svg>
          Reject Application
        </button>
      </div>

      <div v-else-if="user.status === 'Approved'" class="flex flex-col sm:flex-row gap-3">
        <button 
          @click="updateStatus('SecondApproved')"
          :disabled="isLoading"
          class="flex-1 text-white bg-blue-600 hover:bg-blue-700 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800 disabled:opacity-50"
        >
          <svg class="w-4 h-4 mr-2 inline" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
          </svg>
          Give Final Approval
        </button>
        <button 
          @click="updateStatus('Unapproved')"
          :disabled="isLoading"
          class="flex-1 text-gray-900 bg-white border border-gray-300 focus:outline-none hover:bg-gray-100 focus:ring-4 focus:ring-gray-200 font-medium rounded-lg text-sm px-5 py-2.5 dark:bg-gray-800 dark:text-white dark:border-gray-600 dark:hover:bg-gray-700 dark:hover:border-gray-600 dark:focus:ring-gray-700 disabled:opacity-50"
        >
          <svg class="w-4 h-4 mr-2 inline" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
            <path fill-rule="evenodd" d="M7.707 14.707a1 1 0 01-1.414 0L2.586 11H16a1 1 0 110 2H2.586l3.707 3.707a1 1 0 01-1.414 1.414l-5.414-5.414a1 1 0 010-1.414l5.414-5.414a1 1 0 011.414 1.414L2.586 9H16a1 1 0 110 2H2.586l3.707 3.707z" clip-rule="evenodd"></path>
          </svg>
          Revert to Pending
        </button>
        <button 
          @click="updateStatus('Rejected')"
          :disabled="isLoading"
          class="flex-1 text-white bg-red-600 hover:bg-red-700 focus:ring-4 focus:outline-none focus:ring-red-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-red-600 dark:hover:bg-red-700 dark:focus:ring-red-800 disabled:opacity-50"
        >
          <svg class="w-4 h-4 mr-2 inline" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
            <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
          </svg>
          Reject Application
        </button>
      </div>

      <div v-else-if="user.status === 'SecondApproved'" class="text-center">
        <div class="inline-flex items-center px-4 py-2 text-sm font-medium text-green-800 bg-green-100 rounded-lg dark:bg-green-900 dark:text-green-300">
          <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
          </svg>
          User has been fully approved
        </div>
        <button 
          @click="updateStatus('Approved')"
          :disabled="isLoading"
          class="mt-3 text-gray-900 bg-white border border-gray-300 focus:outline-none hover:bg-gray-100 focus:ring-4 focus:ring-gray-200 font-medium rounded-lg text-sm px-5 py-2.5 dark:bg-gray-800 dark:text-white dark:border-gray-600 dark:hover:bg-gray-700 dark:hover:border-gray-600 dark:focus:ring-gray-700 disabled:opacity-50"
        >
          Revert to First Approval
        </button>
      </div>

      <div v-else-if="user.status === 'Rejected'" class="text-center">
        <div class="inline-flex items-center px-4 py-2 text-sm font-medium text-red-800 bg-red-100 rounded-lg dark:bg-red-900 dark:text-red-300 mb-3">
          <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
            <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
          </svg>
          Application has been rejected
        </div>
        <button 
          @click="updateStatus('Unapproved')"
          :disabled="isLoading"
          class="text-white bg-blue-600 hover:bg-blue-700 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800 disabled:opacity-50"
        >
          Reopen Application
        </button>
      </div>
    </div>

    <!-- Loading Overlay -->
    <div v-if="isLoading" class="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center rounded-lg dark:bg-gray-800 dark:bg-opacity-75">
      <div class="flex items-center">
        <svg class="animate-spin h-5 w-5 text-blue-600 mr-3" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
          <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
        <span class="text-gray-600 dark:text-gray-300">Updating status...</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useUserStore } from '@/stores'
import type { UserDto, UserStatus } from '@/interfaces'

// Props
interface Props {
  user: UserDto
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  statusUpdated: [user: UserDto, newStatus: UserStatus]
}>()

// Store
const userStore = useUserStore()

// Reactive state
const isLoading = ref(false)

// Methods
const getUserInitials = (user: UserDto): string => {
  const first = user.firstName?.charAt(0) || ''
  const last = user.lastName?.charAt(0) || ''
  return (first + last).toUpperCase()
}

const getStatusBadgeClass = (status?: UserStatus): string => {
  switch (status) {
    case 'Approved':
      return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
    case 'SecondApproved':
      return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300'
    case 'Unapproved':
      return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300'
    case 'Rejected':
      return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'
    default:
      return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300'
  }
}

const getStepClass = (stepStatus: UserStatus): string => {
  const currentStatus = props.user.status

  if (isStepCompleted(stepStatus)) {
    return 'text-blue-600 dark:text-blue-500'
  } else if (currentStatus === stepStatus) {
    return 'text-blue-600 dark:text-blue-500'
  } else {
    return 'text-gray-500 dark:text-gray-400'
  }
}

const isStepCompleted = (stepStatus: UserStatus): boolean => {
  const currentStatus = props.user.status
  const statusOrder = ['Unapproved', 'Approved', 'SecondApproved']

  if (currentStatus === 'Rejected') {
    return false
  }

  const currentIndex = statusOrder.indexOf(currentStatus || 'Unapproved')
  const stepIndex = statusOrder.indexOf(stepStatus)

  return currentIndex > stepIndex
}

const formatDate = (date?: Date | string): string => {
  if (!date) return 'N/A'

  const dateObj = typeof date === 'string' ? new Date(date) : date
  return dateObj.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const updateStatus = async (newStatus: UserStatus) => {
  if (!props.user.id) return

  try {
    isLoading.value = true
    await userStore.updateUserStatus(props.user.id, newStatus)

    // Update the local user object
    const updatedUser = { ...props.user, status: newStatus }
    emit('statusUpdated', updatedUser, newStatus)

  } catch (error: any) {
    console.error('Failed to update user status:', error)
    // You could add a toast notification here
  } finally {
    isLoading.value = false
  }
}
</script>

<style scoped>
.relative {
  position: relative;
}
</style>

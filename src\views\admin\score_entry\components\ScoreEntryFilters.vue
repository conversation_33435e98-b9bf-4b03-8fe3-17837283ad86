<template>
  <div class="bg-white rounded-lg border border-gray-200 p-3">
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-2 items-end">

      <div v-if="!hasRouteParams">
        <label for="examType" class="block text-xs font-medium text-gray-700 mb-1">
          Exam Type <span class="text-red-500">*</span>
        </label>
        <select
          id="examType"
          v-model="filters.examType"
          @change="onExamTypeChange"
          :disabled="isLoading"
          class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-maneb-primary focus:border-maneb-primary block w-full p-2 disabled:bg-gray-100 disabled:cursor-not-allowed"
        >
          <option value="">
            {{ isLoading ? 'Loading...' : 'Select Exam Type' }}
          </option>
          <option v-for="examType in examTypes" :key="examType.id" :value="examType.id">
            {{ examType.name }}
          </option>
        </select>
      </div>

      <div v-if="isPrimarySchoolExamType">
        <label for="district" class="block text-xs font-medium text-gray-700 mb-1">
          District <span class="text-red-500">*</span>
        </label>

        <div class="relative">
          <input
            type="text"
            v-model="districtSearchQuery"
            @focus="showDistrictDropdown = true"
            @blur="handleDistrictBlur"
            @input="filterDistricts"
            @keydown="handleDistrictKeydown"
            :placeholder="isLoadingDistricts ? 'Loading districts...' : 'Search districts...'"
            :disabled="isLoadingDistricts"
            class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-maneb-primary focus:border-maneb-primary block w-full p-2 disabled:bg-gray-100 disabled:cursor-not-allowed"
          />

          <div
            v-if="showDistrictDropdown && filteredDistricts.length > 0"
            class="absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg max-h-48 overflow-y-auto"
          >
            <ul class="py-1">
              <li
                v-for="district in filteredDistricts"
                :key="district.id"
                @mousedown="selectDistrict(district)"
                class="px-3 py-2 text-sm text-gray-900 hover:bg-gray-100 cursor-pointer"
              >
                {{ district.name }}
              </li>
            </ul>
          </div>
        </div>

        <div v-if="districtsError" class="text-red-500 text-xs mt-1">
          {{ districtsError }}
        </div>
      </div>

      <div>
        <label for="subject" class="block text-xs font-medium text-gray-700 mb-1">
          Subject <span class="text-red-500">*</span>
        </label>
        <select
          id="subject"
          v-model="filters.subject"
          @change="onSubjectChange"
          :disabled="isLoading || !filters.examType"
          class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-maneb-primary focus:border-maneb-primary block w-full p-2 disabled:bg-gray-100 disabled:cursor-not-allowed"
        >
          <option value="">
            {{ isLoading ? 'Loading...' : 'Select Subject' }}
          </option>
          <option v-for="subject in subjects" :key="subject.id" :value="subject.id">
            {{ subject.name }}
          </option>
        </select>
      </div>

      <div>
        <label for="paper" class="block text-xs font-medium text-gray-700 mb-1">
          Paper <span class="text-red-500">*</span>
        </label>
        <select
          id="paper"
          v-model="filters.paper"
          @change="onPaperChange"
          :disabled="isLoading || !filters.subject"
          class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-maneb-primary focus:border-maneb-primary block w-full p-2 disabled:bg-gray-100 disabled:cursor-not-allowed"
        >
          <option value="">
            {{ isLoading ? 'Loading...' : 'Select Paper' }}
          </option>
          <option v-for="paper in papers" :key="paper.id" :value="paper.id">
            {{ paper.name }}
          </option>
        </select>
      </div>

      <div>
        <label for="centerNumber" class="block text-xs font-medium text-gray-700 mb-1">
          Center Number <span class="text-red-500">*</span>
        </label>

        <div class="relative">
          <input
            type="text"
            v-model="centerSearchQuery"
            @focus="showCenterDropdown = true"
            @blur="handleCenterBlur"
            @input="filterCenters"
            @keydown="handleCenterKeydown"
            :placeholder="isLoadingCenters ? 'Loading centers...' : 'Search center number...'"
            :disabled="isLoadingCenters || (!isPrimarySchoolExamType && !filters.examType)"
            class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-maneb-primary focus:border-maneb-primary block w-full p-2 disabled:bg-gray-100 disabled:cursor-not-allowed"
          />

          <div
            v-if="showCenterDropdown && filteredCenters.length > 0"
            class="absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg max-h-48 overflow-y-auto"
          >
            <ul class="py-1">
              <li
                v-for="center in filteredCenters"
                :key="center.id"
                @mousedown="selectCenter(center)"
                class="px-3 py-2 text-sm text-gray-900 hover:bg-gray-100 cursor-pointer"
              >
                <div class="font-medium">{{ center.centerNo }}</div>
                <div v-if="center.schoolName" class="text-xs text-gray-500">{{ center.schoolName }}</div>
              </li>
            </ul>
          </div>
        </div>

        <div v-if="centersError" class="text-red-500 text-xs mt-1">
          {{ centersError }}
        </div>
      </div>

      <div class="flex space-x-2">
        <button
          @click="loadCandidates"
          :disabled="!allRequiredFiltersSelected || isLoading"
          class="px-4 py-2 bg-maneb-primary text-white text-sm font-medium rounded-lg hover:bg-maneb-primary/90 focus:outline-none focus:ring-2 focus:ring-maneb-primary focus:ring-offset-2 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors"
        >
          {{ isLoading ? 'Loading...' : 'Load Candidates' }}
        </button>

        <button
          @click="clearFilters"
          :disabled="isLoading"
          class="px-4 py-2 bg-gray-500 text-white text-sm font-medium rounded-lg hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors"
        >
          Clear
        </button>
      </div>

    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  hasRouteParams: boolean
  filters: any
  examTypes: any[]
  subjects: any[]
  papers: any[]
  isPrimarySchoolExamType: boolean
  isLoading: boolean
  isLoadingDistricts: boolean
  isLoadingCenters: boolean
  districts: any[]
  filteredDistricts: any[]
  filteredCenters: any[]
  districtSearchQuery: string
  centerSearchQuery: string
  showDistrictDropdown: boolean
  showCenterDropdown: boolean
  districtsError: string | null
  centersError: string | null
  allRequiredFiltersSelected: boolean
}

interface Emits {
  (e: 'examTypeChange'): void
  (e: 'subjectChange'): void
  (e: 'paperChange'): void
  (e: 'loadCandidates'): void
  (e: 'clearFilters'): void
  (e: 'filterDistricts'): void
  (e: 'filterCenters'): void
  (e: 'selectDistrict', district: any): void
  (e: 'selectCenter', center: any): void
  (e: 'districtBlur'): void
  (e: 'centerBlur'): void
  (e: 'districtKeydown', event: KeyboardEvent): void
  (e: 'centerKeydown', event: KeyboardEvent): void
}

defineProps<Props>()
const emit = defineEmits<Emits>()

const onExamTypeChange = () => emit('examTypeChange')
const onSubjectChange = () => emit('subjectChange')
const onPaperChange = () => emit('paperChange')
const loadCandidates = () => emit('loadCandidates')
const clearFilters = () => emit('clearFilters')
const filterDistricts = () => emit('filterDistricts')
const filterCenters = () => emit('filterCenters')
const selectDistrict = (district: any) => emit('selectDistrict', district)
const selectCenter = (center: any) => emit('selectCenter', center)
const handleDistrictBlur = () => emit('districtBlur')
const handleCenterBlur = () => emit('centerBlur')
const handleDistrictKeydown = (event: KeyboardEvent) => emit('districtKeydown', event)
const handleCenterKeydown = (event: KeyboardEvent) => emit('centerKeydown', event)
</script>

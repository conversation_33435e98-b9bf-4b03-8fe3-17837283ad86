/**
 * @fileoverview Centralized authentication error handling service
 * @description Single source of truth for all authentication error handling and token management
 */

/**
 * Authentication error types
 */
export enum AuthErrorType {
  UNAUTHORIZED = 'UNAUTHORIZED',
  TOKEN_EXPIRED = 'TOKEN_EXPIRED',
  REFRESH_FAILED = 'REFRESH_FAILED',
  INVALID_CREDENTIALS = 'INVALID_CREDENTIALS',
  SESSION_TIMEOUT = 'SESSION_TIMEOUT',
  PERMISSION_DENIED = 'PERMISSION_DENIED'
}

/**
 * Authentication error details
 */
export interface AuthErrorDetails {
  type: AuthErrorType;
  message: string;
  statusCode?: number;
  originalError?: any;
  timestamp: Date;
  source: string; // Which service/component triggered the error
}

/**
 * Authentication error handler configuration
 */
export interface AuthErrorHandlerConfig {
  /** Whether to automatically redirect to login on auth failures */
  autoRedirect?: boolean;
  /** Login page URL */
  loginUrl?: string;
  /** Whether to show error notifications */
  showNotifications?: boolean;
  /** Custom error message formatter */
  errorFormatter?: (error: AuthErrorDetails) => string;
  /** Custom redirect handler */
  redirectHandler?: (url: string) => void;
  /** Whether to clear all storage on auth failure */
  clearStorage?: boolean;
}

/**
 * Centralized authentication error handler service
 */
class AuthErrorHandlerService {
  private static instance: AuthErrorHandlerService;
  private config: Required<AuthErrorHandlerConfig>;
  private errorHistory: AuthErrorDetails[] = [];

  constructor(config: AuthErrorHandlerConfig = {}) {
    this.config = {
      autoRedirect: true,
      loginUrl: '/auth/login',
      showNotifications: true,
      clearStorage: true,
      errorFormatter: (error: AuthErrorDetails) => error.message,
      redirectHandler: (url: string) => {
        if (typeof window !== 'undefined') {
          window.location.href = url;
        }
      },
      ...config
    };
  }

  /**
   * Get singleton instance
   */
  public static getInstance(config?: AuthErrorHandlerConfig): AuthErrorHandlerService {
    if (!AuthErrorHandlerService.instance) {
      AuthErrorHandlerService.instance = new AuthErrorHandlerService(config);
    }
    return AuthErrorHandlerService.instance;
  }

  /**
   * Handle 401 Unauthorized errors
   */
  handle401Error(source: string, originalError?: any): void {
    const errorDetails: AuthErrorDetails = {
      type: AuthErrorType.UNAUTHORIZED,
      message: 'Authentication required. Please log in again.',
      statusCode: 401,
      originalError,
      timestamp: new Date(),
      source
    };

    this.handleAuthError(errorDetails);
  }

  /**
   * Handle token expiration
   */
  handleTokenExpired(source: string, originalError?: any): void {
    const errorDetails: AuthErrorDetails = {
      type: AuthErrorType.TOKEN_EXPIRED,
      message: 'Your session has expired. Please log in again.',
      statusCode: 401,
      originalError,
      timestamp: new Date(),
      source
    };

    this.handleAuthError(errorDetails);
  }

  /**
   * Handle refresh token failure
   */
  handleRefreshFailure(source: string, originalError?: any): void {
    const errorDetails: AuthErrorDetails = {
      type: AuthErrorType.REFRESH_FAILED,
      message: 'Session refresh failed. Please log in again.',
      statusCode: 401,
      originalError,
      timestamp: new Date(),
      source
    };

    this.handleAuthError(errorDetails);
  }

  /**
   * Handle invalid credentials
   */
  handleInvalidCredentials(source: string, originalError?: any): void {
    const errorDetails: AuthErrorDetails = {
      type: AuthErrorType.INVALID_CREDENTIALS,
      message: 'Invalid credentials. Please check your username and password.',
      statusCode: 401,
      originalError,
      timestamp: new Date(),
      source
    };

    // Don't auto-redirect for invalid credentials - user is already on login page
    this.logError(errorDetails);
    if (this.config.showNotifications) {
      this.showErrorNotification(errorDetails);
    }
  }

  /**
   * Handle permission denied errors
   */
  handlePermissionDenied(source: string, originalError?: any): void {
    const errorDetails: AuthErrorDetails = {
      type: AuthErrorType.PERMISSION_DENIED,
      message: 'You do not have permission to access this resource.',
      statusCode: 403,
      originalError,
      timestamp: new Date(),
      source
    };

    this.logError(errorDetails);
    if (this.config.showNotifications) {
      this.showErrorNotification(errorDetails);
    }
  }

  /**
   * Generic authentication error handler
   */
  handleAuthError(errorDetails: AuthErrorDetails): void {
    this.logError(errorDetails);

    // Clear authentication data
    if (this.config.clearStorage) {
      this.clearAuthenticationData();
    }

    // Show notification if enabled
    if (this.config.showNotifications) {
      this.showErrorNotification(errorDetails);
    }

    // Redirect to login if enabled and appropriate
    if (this.config.autoRedirect && this.shouldRedirect(errorDetails)) {
      this.redirectToLogin(errorDetails);
    }
  }

  /**
   * Clear all authentication data
   */
  private clearAuthenticationData(): void {
    if (typeof localStorage !== 'undefined') {
      // Clear authentication tokens
      localStorage.removeItem('auth_token');
      localStorage.removeItem('refresh_token');
      localStorage.removeItem('user');
      
      // Clear any other auth-related data
      localStorage.removeItem('user_preferences');
      localStorage.removeItem('session_data');
    }

    if (typeof sessionStorage !== 'undefined') {
      // Clear session storage as well
      sessionStorage.removeItem('temp_auth_data');
      sessionStorage.removeItem('auth_state');
    }
  }

  /**
   * Show error notification to user
   */
  private showErrorNotification(errorDetails: AuthErrorDetails): void {
    const message = this.config.errorFormatter(errorDetails);
    
    // Try to use SweetAlert if available
    if (typeof window !== 'undefined' && (window as any).Swal) {
      (window as any).Swal.fire({
        icon: 'warning',
        title: 'Authentication Error',
        text: message,
        confirmButtonText: 'OK',
        confirmButtonColor: '#a12c2c' // MANEB theme color
      });
    } else {
      // Fallback to alert (console logging removed)
      if (typeof window !== 'undefined') {
        alert(message);
      }
    }
  }

  /**
   * Redirect to login page
   */
  private redirectToLogin(errorDetails: AuthErrorDetails): void {
    const loginUrl = `${this.config.loginUrl}?authFailure=true&reason=${errorDetails.type}`;
    
    console.log(`Authentication failure (${errorDetails.type}) from ${errorDetails.source} - redirecting to login`);
    
    // Use custom redirect handler if provided
    this.config.redirectHandler(loginUrl);
  }

  /**
   * Determine if we should redirect based on error type
   */
  private shouldRedirect(errorDetails: AuthErrorDetails): boolean {
    // Don't redirect for invalid credentials (user is already on login page)
    if (errorDetails.type === AuthErrorType.INVALID_CREDENTIALS) {
      return false;
    }

    // Don't redirect for permission denied (user is authenticated but lacks permission)
    if (errorDetails.type === AuthErrorType.PERMISSION_DENIED) {
      return false;
    }

    return true;
  }

  /**
   * Log error for debugging and monitoring (console logging removed)
   */
  private logError(errorDetails: AuthErrorDetails): void {
    // Add to error history (keep last 10 errors)
    this.errorHistory.push(errorDetails);
    if (this.errorHistory.length > 10) {
      this.errorHistory.shift();
    }

    // Error logging with detailed information (console logging removed)
    // Error details are still tracked in errorHistory but not logged to console
  }

  /**
   * Get error history for debugging
   */
  getErrorHistory(): AuthErrorDetails[] {
    return [...this.errorHistory];
  }

  /**
   * Clear error history
   */
  clearErrorHistory(): void {
    this.errorHistory = [];
  }

  /**
   * Update configuration
   */
  updateConfig(newConfig: Partial<AuthErrorHandlerConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }

  /**
   * Check if error is authentication-related
   */
  static isAuthError(statusCode: number): boolean {
    return statusCode === 401 || statusCode === 403;
  }

  /**
   * Create error details from HTTP error
   */
  static createErrorFromHttpStatus(
    statusCode: number, 
    source: string, 
    originalError?: any
  ): AuthErrorDetails | null {
    switch (statusCode) {
      case 401:
        return {
          type: AuthErrorType.UNAUTHORIZED,
          message: 'Authentication required. Please log in again.',
          statusCode,
          originalError,
          timestamp: new Date(),
          source
        };
      case 403:
        return {
          type: AuthErrorType.PERMISSION_DENIED,
          message: 'You do not have permission to access this resource.',
          statusCode,
          originalError,
          timestamp: new Date(),
          source
        };
      default:
        return null;
    }
  }
}

// Export singleton instance with default configuration
export const authErrorHandler = AuthErrorHandlerService.getInstance({
  autoRedirect: true,
  loginUrl: '/auth/login',
  showNotifications: true,
  clearStorage: true
});

// Export class for custom instances
export { AuthErrorHandlerService };

// Export types
export type { AuthErrorDetails, AuthErrorHandlerConfig };

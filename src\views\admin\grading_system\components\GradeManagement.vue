<template>
  <div class="space-y-6">
    <!-- Header with Actions -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div class="flex items-center justify-between mb-6">
        <div>
          <h2 class="text-xl font-semibold text-gray-900">Grade Management</h2>
          <p class="text-sm text-gray-600 mt-1">Manage student grades and academic records</p>
        </div>
        <button
          @click="showGradeModal = true"
          class="inline-flex items-center px-4 py-2 bg-maneb-primary text-white text-sm font-medium rounded-lg hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-maneb-primary focus:ring-offset-2 transition-colors duration-200"
        >
          <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
          </svg>
          Add Grade
        </button>
      </div>

      <!-- Filters -->
      <div class="grid grid-cols-1 md:grid-cols-5 gap-4 mb-6">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">Search</label>
          <input
            v-model="gradingStore.searchQuery"
            type="text"
            placeholder="Search by student ID or grade..."
            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-maneb-primary focus:border-maneb-primary text-sm"
          />
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">Year</label>
          <select
            v-model="gradingStore.yearFilter"
            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-maneb-primary focus:border-maneb-primary text-sm"
          >
            <option value="All">All Years</option>
            <option v-for="year in availableYears" :key="year" :value="year">{{ year }}</option>
          </select>
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">Subject</label>
          <select
            v-model="gradingStore.subjectFilter"
            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-maneb-primary focus:border-maneb-primary text-sm"
          >
            <option value="All">All Subjects</option>
            <option v-for="subject in gradingStore.activeSubjects" :key="subject.id" :value="subject.id">
              {{ subject.name }}
            </option>
          </select>
        </div>
        <!-- Paper filter removed - system is now subject-based -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">Grade</label>
          <select
            v-model="gradingStore.gradeFilter"
            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-maneb-primary focus:border-maneb-primary text-sm"
          >
            <option value="All">All Grades</option>
            <option value="A">Grade A</option>
            <option value="B">Grade B</option>
            <option value="C">Grade C</option>
            <option value="D">Grade D</option>
            <option value="F">Grade F</option>
          </select>
        </div>
      </div>
    </div>

    <!-- Grades Table -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
      <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900">Grades List</h3>
      </div>
      
      <div v-if="gradingStore.isLoading" class="p-8 text-center">
        <div class="inline-flex items-center">
          <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-maneb-primary" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          Loading grades...
        </div>
      </div>

      <div v-else-if="gradingStore.filteredGrades.length === 0" class="p-8 text-center text-gray-500">
        <svg class="mx-auto h-12 w-12 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
        </svg>
        <p class="text-lg font-medium text-gray-900 mb-2">No grades found</p>
        <p class="text-gray-600">Get started by adding your first grade record.</p>
      </div>

      <div v-else class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Student ID</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Subject</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Year</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Score Range</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Grade</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr v-for="grade in paginatedGrades" :key="grade.id" class="hover:bg-gray-50">
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                {{ grade.studentId }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                {{ getSubjectName(grade.subjectId) }}
              </td>
              <!-- Paper column removed - system is now subject-based -->
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                {{ grade.year }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                {{ grade.startScore }} - {{ grade.endScore }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span :class="getGradeBadgeClass(grade.assignedGrade)" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">
                  {{ grade.assignedGrade }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span :class="grade.failResult ? 'bg-red-100 text-red-800' : 'bg-green-100 text-green-800'" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">
                  {{ grade.failResult ? 'Fail' : 'Pass' }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                <button
                  @click="editGrade(grade)"
                  class="text-maneb-primary hover:text-red-700 transition-colors duration-200"
                >
                  Edit
                </button>
                <button
                  @click="deleteGrade(grade)"
                  class="text-red-600 hover:text-red-800 transition-colors duration-200"
                >
                  Delete
                </button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- Pagination -->
      <div v-if="gradingStore.filteredGrades.length > 0" class="px-6 py-4 border-t border-gray-200">
        <div class="flex items-center justify-between">
          <div class="text-sm text-gray-700">
            Showing {{ startIndex + 1 }} to {{ endIndex }} of {{ gradingStore.filteredGrades.length }} results
          </div>
          <div class="flex items-center space-x-2">
            <button
              @click="previousPage"
              :disabled="currentPage === 1"
              class="px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Previous
            </button>
            <span class="px-3 py-1 text-sm text-gray-700">
              Page {{ currentPage }} of {{ totalPages }}
            </span>
            <button
              @click="nextPage"
              :disabled="currentPage === totalPages"
              class="px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Next
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Grade Modal -->
    <GradeModal
      :is-open="showGradeModal"
      :grade="selectedGrade"
      @close="closeGradeModal"
      @success="handleGradeSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useGradingStore } from '@/stores'
import type { GradeDto } from '@/interfaces'
import GradeModal from './GradeModal.vue'
import sweetAlert from '@/utils/ui/sweetAlert'

// Store
const gradingStore = useGradingStore()

// State
const showGradeModal = ref(false)
const selectedGrade = ref<GradeDto | null>(null)
const currentPage = ref(1)
const itemsPerPage = ref(10)

// Computed
const availableYears = computed(() => {
  const years = [...new Set(gradingStore.grades.map(g => g.year))].sort((a, b) => b - a)
  return years
})

const availablePapers = computed(() => {
  if (gradingStore.subjectFilter !== 'All') {
    return gradingStore.papers.filter(paper =>
      paper.subjectId === gradingStore.subjectFilter && paper.isActive && !paper.isDeleted
    )
  }
  return gradingStore.activePapers
})

const paginatedGrades = computed(() => {
  const start = (currentPage.value - 1) * itemsPerPage.value
  const end = start + itemsPerPage.value
  return gradingStore.filteredGrades.slice(start, end)
})

const totalPages = computed(() => 
  Math.ceil(gradingStore.filteredGrades.length / itemsPerPage.value)
)

const startIndex = computed(() => (currentPage.value - 1) * itemsPerPage.value)
const endIndex = computed(() => 
  Math.min(startIndex.value + itemsPerPage.value, gradingStore.filteredGrades.length)
)

// Methods
const getSubjectName = (subjectId: string): string => {
  const subject = gradingStore.subjects.find(s => s.id === subjectId)
  return subject?.name || 'Unknown Subject'
}

const getPaperName = (paperId: string): string => {
  const paper = gradingStore.papers.find(p => p.id === paperId)
  return paper?.name || 'Unknown Paper'
}

const getGradeBadgeClass = (grade: string): string => {
  const classes = {
    'A': 'bg-green-100 text-green-800',
    'B': 'bg-blue-100 text-blue-800',
    'C': 'bg-yellow-100 text-yellow-800',
    'D': 'bg-orange-100 text-orange-800',
    'F': 'bg-red-100 text-red-800'
  }
  return classes[grade as keyof typeof classes] || 'bg-gray-100 text-gray-800'
}

const editGrade = (grade: GradeDto) => {
  selectedGrade.value = grade
  showGradeModal.value = true
}

const deleteGrade = async (grade: GradeDto) => {
  const result = await sweetAlert.confirm(
    'Delete Grade',
    `Are you sure you want to delete this grade record? This action cannot be undone.`,
    'warning'
  )

  if (result.isConfirmed && grade.id) {
    try {
      await gradingStore.deleteGrade(grade.id)
      await sweetAlert.toast.success('Grade deleted successfully')
    } catch (error) {
      await sweetAlert.error('Error', 'Failed to delete grade')
    }
  }
}

const closeGradeModal = () => {
  showGradeModal.value = false
  selectedGrade.value = null
}

const handleGradeSuccess = () => {
  closeGradeModal()
  // Refresh grades list
  gradingStore.fetchGrades()
}

const previousPage = () => {
  if (currentPage.value > 1) {
    currentPage.value--
  }
}

const nextPage = () => {
  if (currentPage.value < totalPages.value) {
    currentPage.value++
  }
}

// Lifecycle
onMounted(() => {
  if (gradingStore.grades.length === 0) {
    gradingStore.fetchGrades()
  }
  if (gradingStore.subjects.length === 0) {
    gradingStore.fetchSubjects()
  }
  if (gradingStore.papers.length === 0) {
    gradingStore.fetchPapers()
  }
})
</script>

<style scoped>
/* MANEB Theme Colors */
.bg-maneb-primary {
  background-color: #a12c2c;
}

.text-maneb-primary {
  color: #a12c2c;
}

.border-maneb-primary {
  border-color: #a12c2c;
}

.hover\:bg-maneb-primary:hover {
  background-color: #a12c2c;
}

.hover\:text-maneb-primary:hover {
  color: #a12c2c;
}

.focus\:ring-maneb-primary:focus {
  --tw-ring-color: #a12c2c;
}

.focus\:border-maneb-primary:focus {
  border-color: #a12c2c;
}
</style>

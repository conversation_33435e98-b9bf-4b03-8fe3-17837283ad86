import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { gradingApiService } from '@/services/api/grading-api.service'
import { useAuthStore } from '../auth/auth.store'
import type {
  AuditLog,
  GradeBoundaryDto,
  CreateGradeBoundaryRequest,
  UpdateGradeBoundaryRequest,
  GradeBoundaryFilters,
  PaperDto,
  ScoreEntry,
  CreateScoreEntryRequest,
  UpdateScoreEntryRequest,
  ScoreApprovalRequest,
  ScoreVerificationRequest,
  ExamLevel,
  ScoreType,
  ApprovalStatus
} from '@/interfaces/external/results-api.interface'
import { ScoreStatus } from '@/interfaces/external/results-api.interface'

export const useGradingApiStore = defineStore('gradingApi', () => {
  // State
  const gradeBoundaries = ref<GradeBoundaryDto[]>([])
  const papers = ref<PaperDto[]>([])
  const scores = ref<ScoreEntry[]>([])
  const auditLogs = ref<AuditLog[]>([])
  const gradeAuditLogs = ref<AuditLog[]>([])
  const currentScore = ref<ScoreEntry | null>(null)
  const isLoading = ref(false)
  const isSubmitting = ref(false)
  const error = ref<string | null>(null)

  // Filters and search
  const gradeBoundaryFilters = ref<GradeBoundaryFilters>({})
  const scoreFilters = ref({
    scoreType: '' as ScoreType | '',
    status: '' as ScoreStatus | '',
    examLevel: '' as ExamLevel | ''
  })

  // Statistics
  const statistics = ref({
    totalScores: 0,
    totalGradeBoundaries: 0,
    scoresByType: {} as Record<ScoreType, number>,
    scoresByStatus: {} as Record<ScoreStatus, number>,
    boundariesByLevel: {} as Record<ExamLevel, number>,
    pendingApprovals: 0,
    recentActivity: 0
  })

  // Auth integration
  const authStore = useAuthStore()

  // Initialize API service with token
  const initializeApiService = () => {
    if (authStore.token) {
      gradingApiService.setToken(authStore.token)
    }
  }

  // Getters
  const filteredGradeBoundaries = computed(() => {
    let filtered = gradeBoundaries.value

    if (gradeBoundaryFilters.value.examLevel) {
      filtered = filtered.filter(boundary => 
        boundary.examLevel === gradeBoundaryFilters.value.examLevel
      )
    }

    if (gradeBoundaryFilters.value.subjectCode) {
      filtered = filtered.filter(boundary => 
        boundary.subjectCode === gradeBoundaryFilters.value.subjectCode
      )
    }

    return filtered
  })

  const filteredScores = computed(() => {
    let filtered = scores.value

    if (scoreFilters.value.scoreType) {
      filtered = filtered.filter(score => score.scoreType === scoreFilters.value.scoreType)
    }

    if (scoreFilters.value.status) {
      filtered = filtered.filter(score => score.status === scoreFilters.value.status)
    }

    return filtered
  })

  const pendingScores = computed(() => 
    scores.value.filter(score => score.status === ScoreStatus.PENDING)
  )

  const verifiedScores = computed(() => 
    scores.value.filter(score => score.status === ScoreStatus.VERIFIED)
  )

  const approvedScores = computed(() => 
    scores.value.filter(score => score.status === ScoreStatus.APPROVED)
  )

  // Actions - Grade Boundaries
  const fetchGradeBoundaries = async (filters?: GradeBoundaryFilters) => {
    try {
      isLoading.value = true
      error.value = null
      initializeApiService()

      const data = await gradingApiService.getGradeBoundaries(filters)
      gradeBoundaries.value = data
      updateStatistics()
    } catch (err: any) {
      error.value = err.message || 'Failed to fetch grade boundaries'
      console.error('Error fetching grade boundaries:', err)
    } finally {
      isLoading.value = false
    }
  }

  const createGradeBoundary = async (data: CreateGradeBoundaryRequest) => {
    try {
      isSubmitting.value = true
      error.value = null
      initializeApiService()

      await gradingApiService.createGradeBoundary(data)
      await fetchGradeBoundaries() // Refresh data
    } catch (err: any) {
      error.value = err.message || 'Failed to create grade boundary'
      throw err
    } finally {
      isSubmitting.value = false
    }
  }

  // Actions - Papers
  const fetchPapers = async (examLevel?: string) => {
    try {
      isLoading.value = true
      error.value = null
      initializeApiService()

      const data = await gradingApiService.getPapers(examLevel)
      papers.value = data
    } catch (err: any) {
      error.value = err.message || 'Failed to fetch papers'
      throw err
    } finally {
      isLoading.value = false
    }
  }

  // Actions - Scores
  const fetchScores = async () => {
    try {
      isLoading.value = true
      error.value = null
      initializeApiService()

      const data = await gradingApiService.getScores()
      scores.value = data
      updateStatistics()
    } catch (err: any) {
      error.value = err.message || 'Failed to fetch scores'
      console.error('Error fetching scores:', err)
    } finally {
      isLoading.value = false
    }
  }

  const fetchScoreById = async (id: number) => {
    try {
      isLoading.value = true
      error.value = null
      initializeApiService()

      const data = await gradingApiService.getScoreById(id)
      currentScore.value = data
      return data
    } catch (err: any) {
      error.value = err.message || 'Failed to fetch score'
      throw err
    } finally {
      isLoading.value = false
    }
  }

  const submitScore = async (data: CreateScoreEntryRequest) => {
    try {
      isSubmitting.value = true
      error.value = null
      initializeApiService()

      const response = await gradingApiService.submitScore(data)
      // No need to fetchScores() - score entry module uses the response directly
      return response
    } catch (err: any) {
      error.value = err.message || 'Failed to submit score'
      throw err
    } finally {
      isSubmitting.value = false
    }
  }

  const getPaginatedScoreEntries = async (params: {
    creator?: string
    centerNo?: string
    schoolId?: string
    examTypeId?: string
    examYear?: number
    subjectId?: string
    pageNumber?: number
    pageSize?: number
  }) => {
    try {
      isLoading.value = true
      error.value = null
      initializeApiService()

      const response = await gradingApiService.getPaginatedScoreEntries(params)
      return response
    } catch (err: any) {
      error.value = err.message || 'Failed to fetch paginated score entries'
      throw err
    } finally {
      isLoading.value = false
    }
  }

  const updateScore = async (id: number, data: UpdateScoreEntryRequest) => {
    try {
      isSubmitting.value = true
      error.value = null
      initializeApiService()

      await gradingApiService.updateScore(id, data)
      await fetchScores() // Refresh data
    } catch (err: any) {
      error.value = err.message || 'Failed to update score'
      throw err
    } finally {
      isSubmitting.value = false
    }
  }

  const deleteScore = async (id: number) => {
    try {
      isSubmitting.value = true
      error.value = null
      initializeApiService()

      await gradingApiService.deleteScore(id)
      await fetchScores() // Refresh data
    } catch (err: any) {
      error.value = err.message || 'Failed to delete score'
      throw err
    } finally {
      isSubmitting.value = false
    }
  }

  const approveScore = async (data: ScoreApprovalRequest) => {
    try {
      isSubmitting.value = true
      error.value = null
      initializeApiService()

      await gradingApiService.approveScore(data)
      await fetchScores() // Refresh data
    } catch (err: any) {
      error.value = err.message || 'Failed to approve score'
      throw err
    } finally {
      isSubmitting.value = false
    }
  }

  const verifyScore = async (data: ScoreVerificationRequest) => {
    try {
      isSubmitting.value = true
      error.value = null
      initializeApiService()

      await gradingApiService.verifyScore(data)
      await fetchScores() // Refresh data
    } catch (err: any) {
      error.value = err.message || 'Failed to verify score'
      throw err
    } finally {
      isSubmitting.value = false
    }
  }

  // Actions - Audit Logs
  const fetchAuditLogs = async () => {
    try {
      isLoading.value = true
      error.value = null
      initializeApiService()

      const data = await gradingApiService.getAuditLogs()
      auditLogs.value = data
    } catch (err: any) {
      error.value = err.message || 'Failed to fetch audit logs'
      // Error fetching audit logs (console logging removed)
    } finally {
      isLoading.value = false
    }
  }

  const fetchGradeAuditLogs = async () => {
    try {
      isLoading.value = true
      error.value = null
      initializeApiService()

      const data = await gradingApiService.getGradeAuditLogs()
      gradeAuditLogs.value = data
    } catch (err: any) {
      error.value = err.message || 'Failed to fetch grade audit logs'
      // Error fetching grade audit logs (console logging removed)
    } finally {
      isLoading.value = false
    }
  }

  // Utility functions
  const updateStatistics = () => {
    statistics.value.totalScores = scores.value.length
    statistics.value.totalGradeBoundaries = gradeBoundaries.value.length
    
    // Count by score type
    statistics.value.scoresByType = scores.value.reduce((acc, score) => {
      if (score.scoreType) {
        acc[score.scoreType as ScoreType] = (acc[score.scoreType as ScoreType] || 0) + 1
      }
      return acc
    }, {} as Record<ScoreType, number>)

    // Count by status
    statistics.value.scoresByStatus = scores.value.reduce((acc, score) => {
      if (score.status) {
        acc[score.status as ScoreStatus] = (acc[score.status as ScoreStatus] || 0) + 1
      }
      return acc
    }, {} as Record<ScoreStatus, number>)

    // Count boundaries by level
    statistics.value.boundariesByLevel = gradeBoundaries.value.reduce((acc, boundary) => {
      if (boundary.examLevel) {
        acc[boundary.examLevel as ExamLevel] = (acc[boundary.examLevel as ExamLevel] || 0) + 1
      }
      return acc
    }, {} as Record<ExamLevel, number>)

    statistics.value.pendingApprovals = scores.value.filter(
      score => score.status === ScoreStatus.PENDING
    ).length
  }

  const clearError = () => {
    error.value = null
  }

  const setFilters = (filters: Partial<typeof gradeBoundaryFilters.value>) => {
    gradeBoundaryFilters.value = { ...gradeBoundaryFilters.value, ...filters }
  }

  const setScoreFilters = (filters: Partial<typeof scoreFilters.value>) => {
    scoreFilters.value = { ...scoreFilters.value, ...filters }
  }

  const resetFilters = () => {
    gradeBoundaryFilters.value = {}
    scoreFilters.value = {
      scoreType: '',
      status: '',
      examLevel: ''
    }
  }

  return {
    // State
    gradeBoundaries,
    papers,
    scores,
    auditLogs,
    gradeAuditLogs,
    currentScore,
    isLoading,
    isSubmitting,
    error,
    gradeBoundaryFilters,
    scoreFilters,
    statistics,

    // Getters
    filteredGradeBoundaries,
    filteredScores,
    pendingScores,
    verifiedScores,
    approvedScores,

    // Actions
    fetchGradeBoundaries,
    createGradeBoundary,
    fetchPapers,
    fetchScores,
    fetchScoreById,
    submitScore,
    getPaginatedScoreEntries,
    updateScore,
    deleteScore,
    approveScore,
    verifyScore,
    fetchAuditLogs,
    fetchGradeAuditLogs,
    clearError,
    setFilters,
    setScoreFilters,
    resetFilters,
    initializeApiService
  }
})

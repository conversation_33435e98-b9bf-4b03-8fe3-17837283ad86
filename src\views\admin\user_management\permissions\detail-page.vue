<template>
  <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
    <!-- Breadcrumbs -->
    <BreadcrumbsActions :breadcrumbs="permissionDetailBreadcrumbs" />
    
    <!-- Loading State -->
    <div v-if="isLoading" class="flex justify-center items-center py-12">
      <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-maneb-primary"></div>
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
      <div class="flex">
        <div class="flex-shrink-0">
          <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
          </svg>
        </div>
        <div class="ml-3">
          <h3 class="text-sm font-medium text-red-800">Error loading permission details</h3>
          <div class="mt-2 text-sm text-red-700">{{ error }}</div>
        </div>
      </div>
    </div>

    <!-- Permission Detail Content -->
    <div v-else-if="permission" class="space-y-6">
      <!-- Header Section -->
      <div class="bg-white shadow-sm rounded-lg border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
          <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
              <div class="bg-red-50 p-3 rounded-full">
                <svg class="h-8 w-8 text-maneb-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                </svg>
              </div>
              <div>
                <h1 class="text-2xl font-bold text-gray-900">{{ getPermissionDisplayName(permission) }}</h1>
                <p class="text-gray-600">Permission Details</p>
              </div>
            </div>
            <div class="flex items-center space-x-3">
              <span :class="getStatusBadgeClass(permission.status)" class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium">
                {{ permission.status }}
              </span>
              <router-link 
                :to="{ name: 'admin.user-management.permissions.edit', params: { id: permission.id } }"
                class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-maneb-primary hover:bg-maneb-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-maneb-primary"
              >
                <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                </svg>
                Edit Permission
              </router-link>
            </div>
          </div>
        </div>
      </div>

      <!-- Permission Information Cards -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Permission Details -->
        <div class="bg-white shadow-sm rounded-lg border border-gray-200">
          <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Permission Details</h3>
          </div>
          <div class="px-6 py-4 space-y-4">
            <div>
              <label class="text-sm font-medium text-gray-500">Action</label>
              <div class="mt-1">
                <span :class="getActionBadgeClass(permission.action)" class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium">
                  {{ permission.action }}
                </span>
              </div>
            </div>
            <div>
              <label class="text-sm font-medium text-gray-500">Access Level</label>
              <div class="mt-1">
                <span :class="permission.canAccess ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'" class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium">
                  {{ permission.canAccess ? 'Allowed' : 'Denied' }}
                </span>
              </div>
            </div>
            <div>
              <label class="text-sm font-medium text-gray-500">Status</label>
              <div class="mt-1">
                <span :class="getStatusBadgeClass(permission.status)" class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium">
                  {{ permission.status }}
                </span>
              </div>
            </div>
          </div>
        </div>

        <!-- Associated Information -->
        <div class="bg-white shadow-sm rounded-lg border border-gray-200">
          <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Associated Information</h3>
          </div>
          <div class="px-6 py-4 space-y-4">
            <div>
              <label class="text-sm font-medium text-gray-500">Role</label>
              <div class="mt-1">
                <router-link
                  v-if="permission.roleId"
                  :to="{ name: 'admin.user-management.roles.detail', params: { id: permission.roleId } }"
                  class="text-sm text-maneb-primary hover:text-maneb-primary-dark font-medium"
                >
                  {{ getRoleName(permission.roleId) }}
                </router-link>
                <p v-else class="text-sm text-gray-900">N/A</p>
              </div>
            </div>
            <div>
              <label class="text-sm font-medium text-gray-500">Section</label>
              <div class="mt-1">
                <router-link 
                  v-if="permission.section"
                  :to="{ name: 'admin.user-management.sections.detail', params: { id: permission.sectionID } }"
                  class="text-sm text-maneb-primary hover:text-maneb-primary-dark font-medium"
                >
                  {{ permission.section.name }}
                </router-link>
                <p v-else class="text-sm text-gray-900">{{ permission.sectionID || 'N/A' }}</p>
              </div>
              <div v-if="permission.section?.controller" class="text-sm text-gray-500 mt-1">
                Controller: {{ permission.section.controller }}
              </div>
              <div v-if="permission.section?.area" class="text-sm text-gray-500">
                Area: {{ permission.section.area }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- System Information -->
      <div class="bg-white shadow-sm rounded-lg border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
          <h3 class="text-lg font-medium text-gray-900">System Information</h3>
        </div>
        <div class="px-6 py-4">
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label class="text-sm font-medium text-gray-500">Permission ID</label>
              <p class="mt-1 text-sm text-gray-900 font-mono">{{ permission.id || 'N/A' }}</p>
            </div>
            <div>
              <label class="text-sm font-medium text-gray-500">Created Date</label>
              <p class="mt-1 text-sm text-gray-900">{{ formatDate(permission.dateCreated) }}</p>
            </div>
            <div>
              <label class="text-sm font-medium text-gray-500">Created By</label>
              <p class="mt-1 text-sm text-gray-900">{{ permission.createdBy || 'System' }}</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Action Buttons -->
      <div class="flex justify-between items-center">
        <router-link 
          :to="{ name: 'admin.user-management.permissions' }"
          class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-maneb-primary"
        >
          <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
          </svg>
          Back to Permissions
        </router-link>
        
        <div class="flex space-x-3">
          <router-link 
            :to="{ name: 'admin.user-management.permissions.status', params: { id: permission.id } }"
            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            Manage Status
          </router-link>
          
          <button
            @click="deletePermission"
            type="button"
            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
          >
            <svg class="h-4 w-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd"></path>
            </svg>
            Delete Permission
          </button>
        </div>
      </div>
    </div>

    <!-- Permission Not Found -->
    <div v-else class="text-center py-12">
      <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
      </svg>
      <h3 class="mt-2 text-sm font-medium text-gray-900">Permission not found</h3>
      <p class="mt-1 text-sm text-gray-500">The requested permission could not be found.</p>
      <div class="mt-6">
        <router-link 
          :to="{ name: 'admin.user-management.permissions' }"
          class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-maneb-primary hover:bg-maneb-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-maneb-primary"
        >
          Back to Permissions
        </router-link>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useRoleStore } from '@/stores'
import BreadcrumbsActions from '@/components/shared/UI/breadcrumbs/breadcrumbs_actions.vue'
import { showConfirmDialog, showSuccessAlert } from '@/utils/ui/sweetAlert'
import type { RolePermissionDto, PermissionAction, RecordStatus } from '@/interfaces'

// Router
const route = useRoute()
const router = useRouter()

// Store
const roleStore = useRoleStore()

// Reactive state
const permission = ref<RolePermissionDto | null>(null)
const isLoading = ref(false)
const error = ref<string | null>(null)

// Computed
const permissionId = computed(() => route.params.id as string)

const permissionDetailBreadcrumbs = computed(() => [
  { name: 'Admin', href: '/admin', current: false },
  { name: 'User Management', href: '/admin/user-management', current: false },
  { name: 'Permissions', href: '/admin/user-management/permissions', current: false },
  { name: getPermissionDisplayName(permission.value) || 'Permission Details', href: '', current: true },
])

// Methods
const getRoleName = (roleId: string): string => {
  const role = roleStore.roles.find(r => r.id === roleId)
  return role?.name || 'Unknown Role'
}

const getPermissionDisplayName = (perm?: RolePermissionDto | null): string => {
  if (!perm) return 'Permission Details'

  const roleName = getRoleName(perm.roleId)
  const sectionName = perm.section?.name || 'Unknown Section'
  const action = perm.action || 'Unknown Action'

  return `${roleName} - ${sectionName} (${action})`
}

const getActionBadgeClass = (action?: PermissionAction) => {
  switch (action) {
    case 'Create': return 'bg-green-100 text-green-800'
    case 'View': case 'ViewAll': return 'bg-blue-100 text-blue-800'
    case 'Update': return 'bg-yellow-100 text-yellow-800'
    case 'Delete': return 'bg-red-100 text-red-800'
    case 'Approve': case 'SecondApprove': return 'bg-purple-100 text-purple-800'
    case 'Authorise': return 'bg-indigo-100 text-indigo-800'
    case 'Login': case 'Logout': return 'bg-gray-100 text-gray-800'
    case 'All': return 'bg-orange-100 text-orange-800'
    case 'Reject': return 'bg-red-100 text-red-800'
    default: return 'bg-gray-100 text-gray-800'
  }
}

const getStatusBadgeClass = (status?: RecordStatus) => {
  switch (status) {
    case 'Approved': return 'bg-green-100 text-green-800'
    case 'SecondApproved': return 'bg-blue-100 text-blue-800'
    case 'Unapproved': return 'bg-yellow-100 text-yellow-800'
    case 'Rejected': return 'bg-red-100 text-red-800'
    default: return 'bg-gray-100 text-gray-800'
  }
}

const formatDate = (date?: Date | string): string => {
  if (!date) return 'N/A'

  const dateObj = typeof date === 'string' ? new Date(date) : date
  return dateObj.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}

const deletePermission = async () => {
  if (!permission.value?.id) return

  const confirmed = await showConfirmDialog(
    'Delete Permission',
    `Are you sure you want to delete this permission? This action cannot be undone.`,
    'Yes, Delete Permission'
  )

  if (!confirmed) return

  try {
    await roleStore.deleteRolePermission(permission.value.id)
    await showSuccessAlert('Permission deleted successfully!')
    router.push({ name: 'admin.user-management.permissions' })
  } catch (error: any) {
    console.error('Error deleting permission:', error)
  }
}

const fetchPermissionDetails = async () => {
  if (!permissionId.value) {
    error.value = 'No permission ID provided'
    return
  }

  try {
    isLoading.value = true
    error.value = null
    permission.value = await roleStore.fetchRolePermissionById(permissionId.value)
  } catch (err: any) {
    error.value = err.message || 'Failed to load permission details'
    console.error('Error fetching permission details:', err)
  } finally {
    isLoading.value = false
  }
}

// Lifecycle
onMounted(() => {
  fetchPermissionDetails()
})
</script>

<style scoped>
/* MANEB Theme Colors */
.bg-maneb-primary {
  background-color: #a12c2c;
}

.text-maneb-primary {
  color: #a12c2c;
}

.bg-maneb-primary-dark {
  background-color: #8b2424;
}

.border-maneb-primary {
  border-color: #a12c2c;
}

.focus\:ring-maneb-primary:focus {
  --tw-ring-color: #a12c2c;
}

.focus\:border-maneb-primary:focus {
  border-color: #a12c2c;
}

.hover\:bg-maneb-primary-dark:hover {
  background-color: #8b2424;
}

.hover\:text-maneb-primary-dark:hover {
  color: #8b2424;
}
</style>

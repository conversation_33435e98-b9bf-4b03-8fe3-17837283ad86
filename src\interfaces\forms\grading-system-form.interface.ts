/**
 * @fileoverview Grading system form interfaces for MANEB Results Management System
 * @description Standardized interfaces for grading system configuration forms
 */

import type { ExamLevel, GradeLevel } from '../common/exam-types.interface';
import type { ValidationResult } from '../common/base.interface';

/**
 * Grading system configuration form data
 * @description Form data for configuring grading systems
 */
export interface GradingSystemFormData {
  /** System name */
  name: string;
  /** System description */
  description?: string;
  /** Examination level this system applies to */
  examLevel: ExamLevel | '';
  /** Academic year */
  year: number | '';
  /** Whether this system is active */
  isActive: boolean;
  /** Grading scale configuration */
  gradingScale: GradingScaleFormData;
  /** Pass/fail criteria */
  passCriteria: PassCriteriaFormData;
  /** Grade calculation method */
  calculationMethod: 'boundary_based' | 'percentage_based' | 'curve_based';
}

/**
 * Grading scale form data
 * @description Configuration for grading scale
 */
export interface GradingScaleFormData {
  /** Scale type */
  scaleType: 'letter' | 'numeric' | 'percentage';
  /** Minimum score */
  minScore: number;
  /** Maximum score */
  maxScore: number;
  /** Grade definitions */
  grades: GradeDefinitionFormData[];
  /** Whether to use plus/minus modifiers */
  usePlusMinusModifiers?: boolean;
}

/**
 * Grade definition form data
 * @description Individual grade definition within a scale
 */
export interface GradeDefinitionFormData {
  /** Grade level */
  gradeLevel: GradeLevel;
  /** Grade name/description */
  gradeName: string;
  /** Lower boundary */
  lowerBound: number;
  /** Upper boundary */
  upperBound: number;
  /** Grade point value (for GPA calculation) */
  gradePoints?: number;
  /** Whether this grade is passing */
  isPass: boolean;
  /** Display order */
  displayOrder: number;
}

/**
 * Pass criteria form data
 * @description Configuration for pass/fail criteria
 */
export interface PassCriteriaFormData {
  /** Minimum passing score */
  minimumPassScore: number;
  /** Minimum passing grade */
  minimumPassGrade: GradeLevel;
  /** Whether to require minimum in all subjects */
  requireMinimumInAllSubjects: boolean;
  /** Subjects that must be passed */
  requiredSubjects?: string[];
  /** Compensation rules */
  compensationRules?: CompensationRulesFormData;
}

/**
 * Compensation rules form data
 * @description Rules for grade compensation
 */
export interface CompensationRulesFormData {
  /** Whether compensation is allowed */
  allowCompensation: boolean;
  /** Maximum number of subjects that can be compensated */
  maxCompensatedSubjects?: number;
  /** Minimum score for compensation */
  minCompensationScore?: number;
  /** Required overall average for compensation */
  requiredOverallAverage?: number;
}

/**
 * Subject weighting form data
 * @description Configuration for subject weightings
 */
export interface SubjectWeightingFormData {
  /** Subject ID */
  subjectId: string;
  /** Weight factor */
  weight: number;
  /** Whether this subject is mandatory */
  isMandatory: boolean;
  /** Whether this subject can be compensated */
  canBeCompensated: boolean;
}

/**
 * Grade calculation configuration form data
 * @description Configuration for grade calculation methods
 */
export interface GradeCalculationConfigFormData {
  /** Calculation method */
  method: 'boundary_based' | 'percentage_based' | 'curve_based' | 'hybrid';
  /** Boundary-based settings */
  boundarySettings?: {
    /** Whether to use strict boundaries */
    useStrictBoundaries: boolean;
    /** Rounding method */
    roundingMethod: 'round' | 'floor' | 'ceil';
  };
  /** Percentage-based settings */
  percentageSettings?: {
    /** Grade percentages */
    gradePercentages: Record<GradeLevel, number>;
  };
  /** Curve-based settings */
  curveSettings?: {
    /** Curve type */
    curveType: 'normal' | 'linear' | 'custom';
    /** Standard deviation for normal curve */
    standardDeviation?: number;
    /** Custom curve points */
    customCurvePoints?: Array<{ score: number; grade: GradeLevel }>;
  };
}

/**
 * Grading system form validation rules
 * @description Validation configuration for grading system forms
 */
export interface GradingSystemFormValidation {
  /** Required fields */
  required: (keyof GradingSystemFormData)[];
  /** Score range validation */
  scoreRange: {
    min: number;
    max: number;
  };
  /** Year range validation */
  yearRange: {
    min: number;
    max: number;
  };
  /** Grade boundary validation */
  boundaryValidation: {
    /** Whether boundaries must be continuous */
    mustBeContinuous: boolean;
    /** Whether boundaries can overlap */
    allowOverlap: boolean;
  };
}

/**
 * Grading system form errors
 * @description Error state for grading system forms
 */
export interface GradingSystemFormErrors {
  /** System name error */
  name?: string;
  /** Description error */
  description?: string;
  /** Exam level error */
  examLevel?: string;
  /** Year error */
  year?: string;
  /** Grading scale errors */
  gradingScale?: {
    scaleType?: string;
    minScore?: string;
    maxScore?: string;
    grades?: Record<number, {
      gradeLevel?: string;
      gradeName?: string;
      lowerBound?: string;
      upperBound?: string;
      gradePoints?: string;
    }>;
  };
  /** Pass criteria errors */
  passCriteria?: {
    minimumPassScore?: string;
    minimumPassGrade?: string;
    requiredSubjects?: string;
  };
  /** General form error */
  general?: string;
}

/**
 * Grading system form state
 * @description Complete form state for grading system configuration
 */
export interface GradingSystemFormState {
  /** Form data */
  data: GradingSystemFormData;
  /** Form errors */
  errors: GradingSystemFormErrors;
  /** Whether form is being submitted */
  isSubmitting: boolean;
  /** Whether form has been touched */
  isTouched: boolean;
  /** Whether form is valid */
  isValid: boolean;
  /** Validation warnings */
  warnings: string[];
  /** Current configuration step */
  currentStep?: number;
  /** Total configuration steps */
  totalSteps?: number;
}

/**
 * Grading system form props
 * @description Props interface for grading system form components
 */
export interface GradingSystemFormProps {
  /** Whether modal/form is open */
  isOpen: boolean;
  /** Existing system data for editing */
  gradingSystem?: any | null;
  /** Whether form is in edit mode */
  isEditing?: boolean;
  /** Available subjects */
  availableSubjects?: Array<{ id: string; name: string; code: string }>;
  /** Form validation rules */
  validationRules?: GradingSystemFormValidation;
  /** Whether to show advanced options */
  showAdvancedOptions?: boolean;
}

/**
 * Grading system form events
 * @description Event interface for grading system form components
 */
export interface GradingSystemFormEvents {
  /** Form close event */
  close: [];
  /** Form success event */
  success: [systemId?: string];
  /** Form error event */
  error: [error: string];
  /** Form validation event */
  validate: [result: ValidationResult];
  /** Form data change event */
  change: [data: GradingSystemFormData];
  /** Step change event (for multi-step forms) */
  stepChange: [step: number];
}

/**
 * Grade boundary template form data
 * @description Form data for creating grade boundary templates
 */
export interface GradeBoundaryTemplateFormData {
  /** Template name */
  name: string;
  /** Template description */
  description?: string;
  /** Examination level */
  examLevel: ExamLevel | '';
  /** Template boundaries */
  boundaries: Array<{
    gradeLevel: GradeLevel;
    lowerBound: number;
    upperBound: number;
    description?: string;
  }>;
  /** Whether template is public */
  isPublic: boolean;
  /** Tags for categorization */
  tags?: string[];
}

/**
 * Grading system import form data
 * @description Form data for importing grading system configurations
 */
export interface GradingSystemImportFormData {
  /** File to import */
  file: File | null;
  /** File format */
  format: 'json' | 'xml' | 'csv';
  /** Whether to overwrite existing systems */
  overwriteExisting: boolean;
  /** Whether to validate before import */
  validateFirst: boolean;
  /** Import options */
  options?: {
    /** Whether to import grade boundaries */
    importBoundaries?: boolean;
    /** Whether to import pass criteria */
    importPassCriteria?: boolean;
    /** Whether to import subject weightings */
    importWeightings?: boolean;
  };
}

/**
 * Grading system export form data
 * @description Form data for exporting grading system configurations
 */
export interface GradingSystemExportFormData {
  /** Export format */
  format: 'json' | 'xml' | 'csv' | 'pdf';
  /** Systems to export */
  systemIds: string[];
  /** Export options */
  options?: {
    /** Include grade boundaries */
    includeBoundaries?: boolean;
    /** Include pass criteria */
    includePassCriteria?: boolean;
    /** Include subject weightings */
    includeWeightings?: boolean;
    /** Include metadata */
    includeMetadata?: boolean;
    /** File name */
    fileName?: string;
  };
}

import { computed, ref } from 'vue';
import type { PaperDto } from '@/interfaces/external/grading-api.interface';

/**
 * Extracted subject interface
 */
export interface ExtractedSubject {
  id: string;
  code: string;
  name: string;
  examLevel: string;
  isActive: boolean;
  papers: PaperDto[];
  paperCount: number;
}

/**
 * Subject extraction options
 */
export interface SubjectExtractionOptions {
  /** Whether to include papers array in the result */
  includePapers?: boolean;
  /** Whether to include paper count */
  includePaperCount?: boolean;
  /** Filter by exam level */
  examLevel?: string;
  /** Filter by active status */
  activeOnly?: boolean;
  /** Custom filter function */
  customFilter?: (subject: ExtractedSubject) => boolean;
}

/**
 * Subject extraction composable
 * Provides reusable logic for extracting unique subjects from papers data
 */
export function useSubjectExtraction(papers: PaperDto[] | (() => PaperDto[]), options: SubjectExtractionOptions = {}) {
  const searchQuery = ref('');
  
  const defaultOptions: Required<SubjectExtractionOptions> = {
    includePapers: true,
    includePaperCount: true,
    examLevel: '',
    activeOnly: true,
    customFilter: () => true,
    ...options
  };

  /**
   * Extract unique subjects from papers using Map-based deduplication
   */
  const extractedSubjects = computed((): ExtractedSubject[] => {
    const papersData = typeof papers === 'function' ? papers() : papers;
    const subjectMap = new Map<string, ExtractedSubject>();

    // Extract subjects using Map for deduplication
    papersData.forEach((paper: PaperDto) => {
      if (!subjectMap.has(paper.subjectCode)) {
        subjectMap.set(paper.subjectCode, {
          id: paper.subjectCode,
          code: paper.subjectCode,
          name: paper.subjectName,
          examLevel: paper.examLevel,
          isActive: true,
          papers: [],
          paperCount: 0
        });
      }

      const subject = subjectMap.get(paper.subjectCode)!;
      
      if (defaultOptions.includePapers) {
        subject.papers.push(paper);
      }
      
      if (defaultOptions.includePaperCount) {
        subject.paperCount++;
      }
    });

    let subjects = Array.from(subjectMap.values());

    // Apply filters
    if (defaultOptions.examLevel) {
      subjects = subjects.filter(subject => subject.examLevel === defaultOptions.examLevel);
    }

    if (defaultOptions.activeOnly) {
      subjects = subjects.filter(subject => subject.isActive);
    }

    if (defaultOptions.customFilter) {
      subjects = subjects.filter(defaultOptions.customFilter);
    }

    return subjects;
  });

  /**
   * Filtered subjects based on search query
   */
  const filteredSubjects = computed((): ExtractedSubject[] => {
    if (!searchQuery.value) return extractedSubjects.value;

    const query = searchQuery.value.toLowerCase().trim();
    return extractedSubjects.value.filter((subject: ExtractedSubject) =>
      subject.name.toLowerCase().includes(query) ||
      subject.code.toLowerCase().includes(query) ||
      subject.examLevel.toLowerCase().includes(query)
    );
  });

  /**
   * Get subjects grouped by exam level
   */
  const subjectsByExamLevel = computed(() => {
    const grouped: Record<string, ExtractedSubject[]> = {};
    
    extractedSubjects.value.forEach(subject => {
      if (!grouped[subject.examLevel]) {
        grouped[subject.examLevel] = [];
      }
      grouped[subject.examLevel].push(subject);
    });

    return grouped;
  });

  /**
   * Get unique exam levels from extracted subjects
   */
  const examLevels = computed((): string[] => {
    const levels = new Set<string>();
    extractedSubjects.value.forEach(subject => levels.add(subject.examLevel));
    return Array.from(levels).sort();
  });

  /**
   * Get subject statistics
   */
  const subjectStats = computed(() => ({
    totalSubjects: extractedSubjects.value.length,
    totalPapers: extractedSubjects.value.reduce((sum, subject) => sum + subject.paperCount, 0),
    examLevelCounts: Object.fromEntries(
      examLevels.value.map(level => [
        level,
        extractedSubjects.value.filter(s => s.examLevel === level).length
      ])
    ),
    averagePapersPerSubject: extractedSubjects.value.length > 0 
      ? Math.round(extractedSubjects.value.reduce((sum, subject) => sum + subject.paperCount, 0) / extractedSubjects.value.length * 100) / 100
      : 0
  }));

  /**
   * Utility methods
   */
  const utils = {
    /**
     * Find subject by code
     */
    findSubjectByCode: (code: string): ExtractedSubject | undefined => {
      return extractedSubjects.value.find(subject => subject.code === code);
    },

    /**
     * Get subject name by code
     */
    getSubjectName: (code: string): string => {
      const subject = extractedSubjects.value.find(s => s.code === code);
      return subject?.name || 'Unknown Subject';
    },

    /**
     * Get subject exam level by code
     */
    getSubjectExamLevel: (code: string): string => {
      const subject = extractedSubjects.value.find(s => s.code === code);
      return subject?.examLevel || 'Unknown';
    },

    /**
     * Get papers for a specific subject
     */
    getPapersForSubject: (code: string): PaperDto[] => {
      const subject = extractedSubjects.value.find(s => s.code === code);
      return subject?.papers || [];
    },

    /**
     * Check if subject exists
     */
    hasSubject: (code: string): boolean => {
      return extractedSubjects.value.some(s => s.code === code);
    },

    /**
     * Get subjects for specific exam level
     */
    getSubjectsForExamLevel: (examLevel: string): ExtractedSubject[] => {
      return extractedSubjects.value.filter(s => s.examLevel === examLevel);
    },

    /**
     * Export subjects as simple dropdown options
     */
    toDropdownOptions: (): Array<{ value: string; label: string; examLevel: string }> => {
      return extractedSubjects.value.map(subject => ({
        value: subject.code,
        label: `${subject.name} (${subject.code})`,
        examLevel: subject.examLevel
      }));
    },

    /**
     * Export subjects as detailed options with metadata
     */
    toDetailedOptions: (): Array<{ 
      value: string; 
      label: string; 
      examLevel: string; 
      paperCount: number;
      isActive: boolean;
    }> => {
      return extractedSubjects.value.map(subject => ({
        value: subject.code,
        label: subject.name,
        examLevel: subject.examLevel,
        paperCount: subject.paperCount,
        isActive: subject.isActive
      }));
    },

    /**
     * Clear search query
     */
    clearSearch: () => {
      searchQuery.value = '';
    },

    /**
     * Set search query
     */
    setSearch: (query: string) => {
      searchQuery.value = query;
    }
  };

  return {
    // Reactive state
    searchQuery,
    
    // Computed data
    extractedSubjects,
    filteredSubjects,
    subjectsByExamLevel,
    examLevels,
    subjectStats,
    
    // Utility methods
    ...utils,
    
    // Configuration
    options: defaultOptions
  };
}

/**
 * Specialized composable for subject management
 */
export function useSubjectManagement(papers: PaperDto[] | (() => PaperDto[])) {
  return useSubjectExtraction(papers, {
    includePapers: true,
    includePaperCount: true,
    activeOnly: true
  });
}

/**
 * Specialized composable for paper management (lighter version)
 */
export function usePaperSubjects(papers: PaperDto[] | (() => PaperDto[])) {
  return useSubjectExtraction(papers, {
    includePapers: false,
    includePaperCount: true,
    activeOnly: true
  });
}

/**
 * Specialized composable for dropdown generation
 */
export function useSubjectDropdown(papers: PaperDto[] | (() => PaperDto[]), examLevel?: string) {
  return useSubjectExtraction(papers, {
    includePapers: false,
    includePaperCount: false,
    examLevel,
    activeOnly: true
  });
}

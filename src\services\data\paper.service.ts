/**
 * @fileoverview Paper Service for MANEB Results Management System
 * @description Service for fetching papers from the API
 */

import apiClient from '../core/api-client';

/**
 * Paper interface matching the API response
 */
export interface PaperDto {
  examLevel: string;
  subjectName: string;
  subjectCode: string;
  paperName: string;
  paperId: string;
  paperMark: number;
  id: string;
  createdBy: string;
  dateCreated: string;
  status: string;
  isDeleted: boolean;
}

/**
 * Simplified paper option for dropdowns
 */
export interface PaperOption {
  id: string;
  name: string;
  code: string;
  examLevel: string;
  subjectCode: string;
  subjectName: string;
  paperMark: number;
}

/**
 * Paper search filters
 */
export interface PaperFilters {
  examLevel?: string;
  subjectCode?: string;
  search?: string;
}

/**
 * Paper Service
 * @description Handles all paper related API operations
 */
export class PaperService {
  private static instance: PaperService;
  private cachedPapers: PaperDto[] | null = null;
  private lastFetchTime: number = 0;
  private readonly CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

  private constructor() {}

  /**
   * Get singleton instance
   */
  static getInstance(): PaperService {
    if (!PaperService.instance) {
      PaperService.instance = new PaperService();
    }
    return PaperService.instance;
  }

  /**
   * Fetch papers from API with optional filters
   */
  async fetchPapers(filters: {
    subjectId?: string;
    examTypeId?: string;
    pageSize?: number;
    search?: string;
    forceRefresh?: boolean;
  } = {}): Promise<{ items: PaperDto[] }> {
    const now = Date.now();

    // Return cached data if available and not expired (unless force refresh)
    if (!filters.forceRefresh && this.cachedPapers && (now - this.lastFetchTime) < this.CACHE_DURATION) {
      // For filtered requests, we still need to filter the cached data
      if (filters.subjectId || filters.examTypeId || filters.search) {
        const filteredPapers = this.filterPapers(this.cachedPapers, filters);
        return { items: filteredPapers };
      }

      return { items: this.cachedPapers };
    }

    try {
      // Fetching papers from API (console logging removed)

      // Always fetch all papers (no server-side filtering)
      // We'll do client-side filtering instead
      const url = `/api/paper`;
      // Making API call (console logging removed)

      // The API returns a direct array of papers, apiClient already extracts the data
      const papers: PaperDto[] = await apiClient.get(url);
      // Paper API response (console logging removed)

      // Always cache the full dataset since we fetch all papers
      this.cachedPapers = papers;
      this.lastFetchTime = now;
      // Papers cached (console logging removed)

      // Apply client-side filtering to the API response
      const filteredPapers = this.filterPapers(papers, filters);
      // Client-side filtering applied (console logging removed)

      return { items: filteredPapers };
    } catch (error) {
      // Error fetching papers (console logging removed)

      // Return cached data if available, even if expired
      if (this.cachedPapers) {
        // Using cached papers due to API error (console logging removed)
        const filteredPapers = this.filterPapers(this.cachedPapers, filters);
        return { items: filteredPapers };
      }

      throw error;
    }
  }

  /**
   * Filter papers locally (for cached data)
   */
  private filterPapers(papers: PaperDto[], filters: {
    subjectId?: string;
    examTypeId?: string;
    search?: string;
  }): PaperDto[] {
    console.log('🔍 Filtering papers with filters:', filters);
    console.log('📋 Total papers before filtering:', papers.length);

    const filtered = papers.filter(paper => {
      // Subject filtering
      if (filters.subjectId && paper.subjectCode !== filters.subjectId) {
        console.log('❌ Paper filtered out by subject:', paper.paperName, 'subjectCode:', paper.subjectCode, 'vs filter:', filters.subjectId);
        return false;
      }

      // Exam type filtering
      if (filters.examTypeId && paper.examLevel !== filters.examTypeId) {
        console.log('❌ Paper filtered out by examType:', paper.paperName, 'examLevel:', paper.examLevel, 'vs filter:', filters.examTypeId);
        return false;
      }

      // Search filtering
      if (filters.search) {
        const searchLower = filters.search.toLowerCase();
        const matches = (
          paper.paperName.toLowerCase().includes(searchLower) ||
          paper.subjectName.toLowerCase().includes(searchLower) ||
          paper.paperId.toLowerCase().includes(searchLower) ||
          paper.subjectCode.toLowerCase().includes(searchLower)
        );
        if (!matches) {
          console.log('❌ Paper filtered out by search:', paper.paperName);
          return false;
        }
      }

      console.log('✅ Paper passed filters:', paper.paperName, 'subject:', paper.subjectCode);
      return true;
    });

    console.log('📋 Papers after filtering:', filtered.length);
    return filtered;
  }

  /**
   * Get active papers only
   */
  async getActivePapers(): Promise<PaperDto[]> {
    const response = await this.fetchPapers();
    const papers = response.items;

    // Ensure papers is an array before filtering
    if (!Array.isArray(papers)) {
      console.error('Papers is not an array:', papers);
      return [];
    }

    return papers.filter(paper => !paper.isDeleted);
  }

  /**
   * Get papers as dropdown options
   */
  async getPaperOptions(activeOnly: boolean = true): Promise<PaperOption[]> {
    const papers = activeOnly ? await this.getActivePapers() : (await this.fetchPapers()).items;

    return papers.map(paper => ({
      id: paper.id,
      name: paper.paperName,
      code: paper.paperId,
      examLevel: paper.examLevel,
      subjectCode: paper.subjectCode,
      subjectName: paper.subjectName,
      paperMark: paper.paperMark
    }));
  }

  /**
   * Get papers filtered by criteria
   */
  async getFilteredPapers(filters: PaperFilters): Promise<PaperDto[]> {
    const papers = await this.getActivePapers();
    
    return papers.filter(paper => {
      if (filters.examLevel && paper.examLevel !== filters.examLevel) {
        return false;
      }
      
      if (filters.subjectCode && paper.subjectCode !== filters.subjectCode) {
        return false;
      }
      
      if (filters.search) {
        const searchLower = filters.search.toLowerCase();
        return (
          paper.paperName.toLowerCase().includes(searchLower) ||
          paper.paperId.toLowerCase().includes(searchLower) ||
          paper.subjectName.toLowerCase().includes(searchLower) ||
          paper.subjectCode.toLowerCase().includes(searchLower)
        );
      }
      
      return true;
    });
  }

  /**
   * Search papers by query
   */
  async searchPapers(query: string): Promise<PaperDto[]> {
    return this.getFilteredPapers({ search: query });
  }

  /**
   * Get papers by exam level
   */
  async getPapersByExamLevel(examLevel: string): Promise<PaperDto[]> {
    return this.getFilteredPapers({ examLevel });
  }

  /**
   * Get papers by subject code
   */
  async getPapersBySubjectCode(subjectCode: string): Promise<PaperDto[]> {
    return this.getFilteredPapers({ subjectCode });
  }

  /**
   * Get papers by subject name
   */
  async getPapersBySubjectName(subjectName: string): Promise<PaperDto[]> {
    try {
      const papers = await this.getActivePapers();

      if (!Array.isArray(papers)) {
        console.error('Papers is not an array in getPapersBySubjectName:', papers);
        return [];
      }

      return papers.filter(paper => paper.subjectName === subjectName);
    } catch (error) {
      console.error('Error in getPapersBySubjectName:', error);
      return [];
    }
  }

  /**
   * Get papers by subject code and exam level (for filtering by both subject and exam type)
   */
  async getPapersBySubjectCodeAndExamLevel(subjectCode: string, examLevel: string): Promise<PaperDto[]> {
    try {
      const papers = await this.getActivePapers();

      if (!Array.isArray(papers)) {
        // Papers is not an array (console logging removed)
        return [];
      }

      // Papers in getPapersBySubjectCodeAndExamLevel (console logging removed)

      return papers.filter(paper =>
        paper.subjectCode === subjectCode && paper.examLevel === examLevel
      );
    } catch (error) {
      // Error in getPapersBySubjectCodeAndExamLevel (console logging removed)
      return [];
    }
  }

  /**
   * Get paper by ID
   */
  async getPaperById(id: string): Promise<PaperDto | undefined> {
    const response = await this.fetchPapers();
    return response.items.find(paper => paper.id === id);
  }

  /**
   * Get unique exam levels from papers
   */
  async getExamLevels(): Promise<string[]> {
    const papers = await this.getActivePapers();
    const examLevels = [...new Set(papers.map(paper => paper.examLevel))];
    return examLevels.sort();
  }

  /**
   * Get unique subjects from papers
   */
  async getSubjects(): Promise<Array<{code: string, name: string}>> {
    const papers = await this.getActivePapers();
    const subjectsMap = new Map<string, string>();
    
    papers.forEach(paper => {
      subjectsMap.set(paper.subjectCode, paper.subjectName);
    });
    
    return Array.from(subjectsMap.entries()).map(([code, name]) => ({ code, name }));
  }

  /**
   * Clear cache (useful for testing or forced refresh)
   */
  clearCache(): void {
    this.cachedPapers = null;
    this.lastFetchTime = 0;
  }
}

// Export singleton instance
export const paperService = PaperService.getInstance();

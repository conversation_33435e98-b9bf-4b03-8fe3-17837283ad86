import { defineStore } from 'pinia'

interface Tenant {
  id: string
  name: string
  domain: string
  isActive: boolean
}

interface TenantState {
  currentTenant: Tenant | null
  tenants: Tenant[]
  isLoading: boolean
  error: string | null
}

export const useTenantStore = defineStore('tenant', {
  state: (): TenantState => ({
    currentTenant: null,
    tenants: [],
    isLoading: false,
    error: null
  }),

  getters: {
    getCurrentTenant: (state) => state.currentTenant,
    getAllTenants: (state) => state.tenants,
    getIsLoading: (state) => state.isLoading,
    getError: (state) => state.error
  },

  actions: {
    async fetchTenants() {
      this.isLoading = true
      this.error = null
      try {
        // TODO: Replace with actual API call
        const response = await fetch('/api/tenants')
        const data = await response.json()
        this.tenants = data
      } catch (error) {
        this.error = error instanceof Error ? error.message : 'Failed to fetch tenants'
      } finally {
        this.isLoading = false
      }
    },

    setCurrentTenant(tenant: Tenant) {
      this.currentTenant = tenant
      // Persist tenant selection in localStorage
      localStorage.setItem('currentTenant', JSON.stringify(tenant))
    },

    loadSavedTenant() {
      const savedTenant = localStorage.getItem('currentTenant')
      if (savedTenant) {
        this.currentTenant = JSON.parse(savedTenant)
      }
    },

    clearCurrentTenant() {
      this.currentTenant = null
      localStorage.removeItem('currentTenant')
    },

    async switchTenant(tenantId: string) {
      const tenant = this.tenants.find((t) => t.id === tenantId)
      if (tenant) {
        this.setCurrentTenant(tenant)
        // TODO: Add any additional logic needed when switching tenants
        // e.g., refreshing application state, updating API configurations, etc.
      } else {
        this.error = 'Tenant not found'
      }
    }
  }
})
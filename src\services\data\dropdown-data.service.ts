/**
 * @fileoverview Centralized dropdown data service for MANEB Results Management System
 * @description Single source of truth for all dropdown options with hierarchical relationships
 */

/**
 * Base interface for dropdown options
 */
export interface DropdownOption {
  id: string;
  name: string;
  code?: string;
  parentId?: string;
  isActive?: boolean;
  metadata?: Record<string, any>;
}

/**
 * Division data structure
 */
export interface Division extends DropdownOption {
  region: 'North' | 'Central' | 'South';
}

/**
 * District data structure
 */
export interface District extends DropdownOption {
  divisionId: string;
  population?: number;
}

/**
 * Center data structure
 */
export interface Center extends DropdownOption {
  districtId: string;
  type: 'Primary' | 'Secondary' | 'Mixed';
  capacity?: number;
}

/**
 * School data structure
 */
export interface School extends DropdownOption {
  centerId: string;
  type: 'Primary' | 'Secondary' | 'Mixed';
  examLevels: string[];
}

/**
 * Subject data structure
 */
export interface Subject extends DropdownOption {
  code: string;
  examLevels: string[];
  category: 'Core' | 'Optional' | 'Practical';
}

/**
 * Centralized dropdown data service
 */
class DropdownDataService {
  private static instance: DropdownDataService;

  /**
   * Divisions in Malawi
   */
  private readonly divisions: Division[] = [
    { id: '1', name: 'Northern Division', code: 'NORTH', region: 'North', isActive: true },
    { id: '2', name: 'Central Division', code: 'CENTRAL', region: 'Central', isActive: true },
    { id: '3', name: 'Southern Division', code: 'SOUTH', region: 'South', isActive: true }
  ];

  /**
   * Districts in Malawi
   */
  private readonly districts: District[] = [
    // Northern Division
    { id: '1', name: 'Karonga', code: 'KA', divisionId: '1', parentId: '1', isActive: true },
    { id: '2', name: 'Chitipa', code: 'CH', divisionId: '1', parentId: '1', isActive: true },
    { id: '3', name: 'Rumphi', code: 'RU', divisionId: '1', parentId: '1', isActive: true },
    { id: '4', name: 'Mzimba', code: 'MZ', divisionId: '1', parentId: '1', isActive: true },
    { id: '5', name: 'Nkhata Bay', code: 'NB', divisionId: '1', parentId: '1', isActive: true },
    { id: '6', name: 'Likoma', code: 'LK', divisionId: '1', parentId: '1', isActive: true },

    // Central Division
    { id: '7', name: 'Kasungu', code: 'KS', divisionId: '2', parentId: '2', isActive: true },
    { id: '8', name: 'Nkhotakota', code: 'NK', divisionId: '2', parentId: '2', isActive: true },
    { id: '9', name: 'Ntchisi', code: 'NT', divisionId: '2', parentId: '2', isActive: true },
    { id: '10', name: 'Dowa', code: 'DW', divisionId: '2', parentId: '2', isActive: true },
    { id: '11', name: 'Salima', code: 'SA', divisionId: '2', parentId: '2', isActive: true },
    { id: '12', name: 'Lilongwe', code: 'LL', divisionId: '2', parentId: '2', isActive: true },
    { id: '13', name: 'Mchinji', code: 'MC', divisionId: '2', parentId: '2', isActive: true },
    { id: '14', name: 'Dedza', code: 'DD', divisionId: '2', parentId: '2', isActive: true },
    { id: '15', name: 'Ntcheu', code: 'NC', divisionId: '2', parentId: '2', isActive: true },

    // Southern Division
    { id: '16', name: 'Mangochi', code: 'MG', divisionId: '3', parentId: '3', isActive: true },
    { id: '17', name: 'Machinga', code: 'MA', divisionId: '3', parentId: '3', isActive: true },
    { id: '18', name: 'Zomba', code: 'ZB', divisionId: '3', parentId: '3', isActive: true },
    { id: '19', name: 'Chiradzulu', code: 'CR', divisionId: '3', parentId: '3', isActive: true },
    { id: '20', name: 'Blantyre', code: 'BT', divisionId: '3', parentId: '3', isActive: true },
    { id: '21', name: 'Mwanza', code: 'MW', divisionId: '3', parentId: '3', isActive: true },
    { id: '22', name: 'Thyolo', code: 'TH', divisionId: '3', parentId: '3', isActive: true },
    { id: '23', name: 'Chikwawa', code: 'CK', divisionId: '3', parentId: '3', isActive: true },
    { id: '24', name: 'Nsanje', code: 'NS', divisionId: '3', parentId: '3', isActive: true },
    { id: '25', name: 'Balaka', code: 'BA', divisionId: '3', parentId: '3', isActive: true },
    { id: '26', name: 'Neno', code: 'NE', divisionId: '3', parentId: '3', isActive: true },
    { id: '27', name: 'Phalombe', code: 'PH', divisionId: '3', parentId: '3', isActive: true }
  ];

  /**
   * Examination centers
   */
  private readonly centers: Center[] = [
    // Major centers in key districts
    { id: '1', name: 'Lilongwe Central Center', code: 'LLC', districtId: '12', parentId: '12', type: 'Mixed', isActive: true },
    { id: '2', name: 'Lilongwe Area 25 Center', code: 'LLA25', districtId: '12', parentId: '12', type: 'Secondary', isActive: true },
    { id: '3', name: 'Blantyre Central Center', code: 'BTC', districtId: '20', parentId: '20', type: 'Mixed', isActive: true },
    { id: '4', name: 'Blantyre Limbe Center', code: 'BTL', districtId: '20', parentId: '20', type: 'Secondary', isActive: true },
    { id: '5', name: 'Mzuzu Central Center', code: 'MZC', districtId: '4', parentId: '4', type: 'Mixed', isActive: true },
    { id: '6', name: 'Zomba Central Center', code: 'ZBC', districtId: '18', parentId: '18', type: 'Mixed', isActive: true },
    { id: '7', name: 'Kasungu Center', code: 'KSC', districtId: '7', parentId: '7', type: 'Mixed', isActive: true },
    { id: '8', name: 'Karonga Center', code: 'KAC', districtId: '1', parentId: '1', type: 'Mixed', isActive: true },
    { id: '9', name: 'Dedza Center', code: 'DDC', districtId: '14', parentId: '14', type: 'Mixed', isActive: true },
    { id: '10', name: 'Mangochi Center', code: 'MGC', districtId: '16', parentId: '16', type: 'Mixed', isActive: true }
  ];

  /**
   * Core subjects for MANEB examinations
   */
  private readonly subjects: Subject[] = [
    // Core subjects for all levels
    { id: '1', name: 'Mathematics', code: 'MATH', examLevels: ['PLCE', 'JCE', 'MSCE'], category: 'Core', isActive: true },
    { id: '2', name: 'English Language', code: 'ENG', examLevels: ['PLCE', 'JCE', 'MSCE'], category: 'Core', isActive: true },
    { id: '3', name: 'Chichewa', code: 'CHI', examLevels: ['PLCE', 'JCE', 'MSCE'], category: 'Core', isActive: true },

    // Science subjects
    { id: '4', name: 'Physical Science', code: 'PHYS', examLevels: ['JCE', 'MSCE'], category: 'Core', isActive: true },
    { id: '5', name: 'Biology', code: 'BIO', examLevels: ['MSCE'], category: 'Core', isActive: true },
    { id: '6', name: 'Chemistry', code: 'CHEM', examLevels: ['MSCE'], category: 'Optional', isActive: true },
    { id: '7', name: 'Physics', code: 'PHY', examLevels: ['MSCE'], category: 'Optional', isActive: true },

    // Social studies
    { id: '8', name: 'Social Studies', code: 'SS', examLevels: ['PLCE', 'JCE'], category: 'Core', isActive: true },
    { id: '9', name: 'Geography', code: 'GEO', examLevels: ['MSCE'], category: 'Optional', isActive: true },
    { id: '10', name: 'History', code: 'HIST', examLevels: ['MSCE'], category: 'Optional', isActive: true },

    // Other subjects
    { id: '11', name: 'Agriculture', code: 'AGR', examLevels: ['JCE', 'MSCE'], category: 'Optional', isActive: true },
    { id: '12', name: 'Life Skills', code: 'LS', examLevels: ['PLCE', 'JCE'], category: 'Core', isActive: true },
    { id: '13', name: 'Bible Knowledge', code: 'BK', examLevels: ['MSCE'], category: 'Optional', isActive: true },
    { id: '14', name: 'French', code: 'FR', examLevels: ['MSCE'], category: 'Optional', isActive: true },
    { id: '15', name: 'Computer Studies', code: 'CS', examLevels: ['MSCE'], category: 'Optional', isActive: true }
  ];

  /**
   * Get singleton instance
   */
  public static getInstance(): DropdownDataService {
    if (!DropdownDataService.instance) {
      DropdownDataService.instance = new DropdownDataService();
    }
    return DropdownDataService.instance;
  }

  /**
   * Get all divisions
   */
  getDivisions(): Division[] {
    return this.divisions.filter(d => d.isActive !== false);
  }

  /**
   * Get districts by division
   */
  getDistrictsByDivision(divisionId: string): District[] {
    return this.districts.filter(d => d.divisionId === divisionId && d.isActive !== false);
  }

  /**
   * Get all districts
   */
  getDistricts(): District[] {
    return this.districts.filter(d => d.isActive !== false);
  }

  /**
   * Get centers by district
   */
  getCentersByDistrict(districtId: string): Center[] {
    return this.centers.filter(c => c.districtId === districtId && c.isActive !== false);
  }

  /**
   * Get all centers
   */
  getCenters(): Center[] {
    return this.centers.filter(c => c.isActive !== false);
  }

  /**
   * Get subjects by exam level
   */
  getSubjectsByExamLevel(examLevel: string): Subject[] {
    return this.subjects.filter(s => 
      s.examLevels.includes(examLevel) && s.isActive !== false
    );
  }

  /**
   * Get all subjects
   */
  getSubjects(): Subject[] {
    return this.subjects.filter(s => s.isActive !== false);
  }

  /**
   * Get core subjects for exam level
   */
  getCoreSubjects(examLevel: string): Subject[] {
    return this.subjects.filter(s => 
      s.examLevels.includes(examLevel) && 
      s.category === 'Core' && 
      s.isActive !== false
    );
  }

  /**
   * Convert to dropdown format for UI components
   */
  toDropdownOptions<T extends DropdownOption>(items: T[]): Array<{id: string, name: string, parentId?: string}> {
    return items.map(item => ({
      id: item.id,
      name: item.name,
      parentId: item.parentId
    }));
  }

  /**
   * Get hierarchical data for filtering components
   */
  getHierarchicalData() {
    return {
      divisions: this.toDropdownOptions(this.getDivisions()),
      districts: this.toDropdownOptions(this.getDistricts()),
      centers: this.toDropdownOptions(this.getCenters()),
      subjects: this.toDropdownOptions(this.getSubjects())
    };
  }

  /**
   * Search functionality
   */
  searchItems<T extends DropdownOption>(items: T[], query: string): T[] {
    const searchTerm = query.toLowerCase().trim();
    if (!searchTerm) return items;

    return items.filter(item =>
      item.name.toLowerCase().includes(searchTerm) ||
      item.code?.toLowerCase().includes(searchTerm)
    );
  }

  /**
   * Get item by ID
   */
  getItemById<T extends DropdownOption>(items: T[], id: string): T | undefined {
    return items.find(item => item.id === id);
  }

  /**
   * Get division by ID
   */
  getDivisionById(id: string): Division | undefined {
    return this.getItemById(this.divisions, id);
  }

  /**
   * Get district by ID
   */
  getDistrictById(id: string): District | undefined {
    return this.getItemById(this.districts, id);
  }

  /**
   * Get center by ID
   */
  getCenterById(id: string): Center | undefined {
    return this.getItemById(this.centers, id);
  }

  /**
   * Get subject by ID
   */
  getSubjectById(id: string): Subject | undefined {
    return this.getItemById(this.subjects, id);
  }

  /**
   * Get subject by code
   */
  getSubjectByCode(code: string): Subject | undefined {
    return this.subjects.find(s => s.code === code && s.isActive !== false);
  }
}

// Export singleton instance
export const dropdownDataService = DropdownDataService.getInstance();

// Export types
export type { DropdownOption, Division, District, Center, School, Subject };

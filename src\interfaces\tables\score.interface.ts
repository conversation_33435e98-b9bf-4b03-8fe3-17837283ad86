/**
 * @fileoverview Score table interfaces for MANEB Results Management System
 * @description Standardized interfaces for score-related data models
 */

import type { BaseEntity } from '../common/base.interface';
import type { ExamLevel, GradeLevel, ScoreType, ScoreStatus } from '../common/exam-types.interface';

/**
 * Score entry entity (Internal EMS API)
 * @description Score entry as stored in the internal EMS database
 */
export interface ScoreEntity extends BaseEntity {
  /** Student ID reference */
  studentId: string;
  /** Subject ID reference */
  subjectId: string;
  /** Academic year */
  year: number;
  /** Score value */
  score: number;
  /** Type of score entry */
  scoreType: ScoreType;
  /** Current status of the score */
  status: ScoreStatus;
  /** Optional correction notes */
  correctionNote?: string;
  /** Examination number */
  examinationNumber?: string;
  /** Timestamp when score was entered */
  timestamp: Date;
  /** User who verified the score */
  verifiedByUserId?: string;
  /** Timestamp when score was verified */
  verifiedAt?: Date;
  /** User who approved the score */
  approvedByUserId?: string;
  /** Timestamp when score was approved */
  approvedAt?: Date;
  /** User who gave final approval */
  finalApprovedByUserId?: string;
  /** Timestamp of final approval */
  finalApprovedAt?: Date;
}

/**
 * Score DTO for API responses
 * @description Data transfer object for score data with populated references
 */
export interface ScoreDto extends ScoreEntity {
  /** Student information (populated) */
  student?: StudentDto;
  /** Subject information (populated) */
  subject?: SubjectDto;
  /** User who entered the score (populated) */
  enteredByUser?: UserDto;
  /** User who verified the score (populated) */
  verifiedByUser?: UserDto;
  /** User who approved the score (populated) */
  approvedByUser?: UserDto;
  /** User who gave final approval (populated) */
  finalApprovedByUser?: UserDto;
  /** Calculated grade based on boundaries */
  calculatedGrade?: GradeLevel;
  /** Whether this score results in a pass */
  passStatus?: boolean;
}

/**
 * Legacy ScoreEntry alias for backward compatibility
 * @deprecated Use ScoreDto instead
 */
export interface ScoreEntry extends ScoreDto {}

/**
 * External score entry entity (External Grading API)
 * @description Score entry as stored in the external grading system
 */
export interface ExternalScoreEntity {
  /** Score entry ID */
  scoreEntryId: number;
  /** Paper code identifier */
  paperId?: number;
  /** User who entered the score */
  enteredByUserId?: string;
  /** Type of score entry */
  scoreType?: string;
  /** Score value */
  score?: number;
  /** Correction notes */
  correctionNote?: string;
  /** Examination number */
  examinationNumber?: string;
  /** Entry timestamp */
  timestamp?: Date;
  /** User who verified the score */
  verifiedByUserId?: string;
  /** Verification timestamp */
  verifiedAt?: Date;
  /** User who approved the score */
  approvedByUserId?: string;
  /** Approval timestamp */
  approvedAt?: Date;
  /** User who gave final approval */
  finalApprovedByUserId?: string;
  /** Final approval timestamp */
  finalApprovedAt?: Date;
  /** Current status */
  status?: string;
  /** Related script information */
  script?: ScriptEntity;
}

/**
 * Script entity (External Grading API)
 * @description Examination script information
 */
export interface ScriptEntity {
  /** Script ID */
  scriptId: number;
  /** Candidate ID */
  candidateId?: number;
  /** Subject */
  subject?: string;
  /** Script number */
  scriptNumber?: string;
  /** Examination session */
  examSession?: string;
  /** Creation timestamp */
  createdAt?: Date;
  /** Related candidate information */
  candidate?: CandidateEntity;
  /** Score entries for this script */
  scoreEntries?: ExternalScoreEntity[];
}

/**
 * Candidate entity (External Grading API)
 * @description Candidate/student information in external system
 */
export interface CandidateEntity {
  /** Candidate ID */
  candidateId: number;
  /** Examination type */
  examType?: string;
  /** District */
  district?: string;
  /** Examination center */
  center?: string;
  /** Center number */
  centerNumber?: string;
  /** Candidate number */
  candidateNumber?: string;
  /** Subject */
  subject?: string;
  /** First name */
  firstName?: string;
  /** Other names */
  otherNames?: string;
  /** Last name */
  lastName?: string;
  /** Cohort ID */
  cohortId?: string;
  /** Related scripts */
  scripts?: ScriptEntity[];
}

/**
 * Grade entity
 * @description Final grade assignment for a student in a subject
 */
export interface GradeEntity extends BaseEntity {
  /** Student ID reference */
  studentId: string;
  /** Subject ID reference */
  subjectId: string;
  /** Academic year */
  year: number;
  /** Starting score (for range-based grading) */
  startScore: number;
  /** Ending score (for range-based grading) */
  endScore: number;
  /** Assigned grade level */
  assignedGrade: GradeLevel;
  /** Whether this is a failing result */
  failResult: boolean;
}

/**
 * Student result entity
 * @description Comprehensive result for a student in a subject
 */
export interface StudentResultEntity extends BaseEntity {
  /** Student ID reference */
  studentId: string;
  /** Subject ID reference */
  subjectId: string;
  /** Academic year */
  year: number;
  /** Final score */
  score: number;
  /** Calculated grade */
  calculatedGrade: GradeLevel;
  /** Pass/fail status */
  passStatus: boolean;
  /** Date when grade was calculated */
  dateCalculated?: Date;
}

/**
 * Score filter options
 * @description Filtering options for score queries
 */
export interface ScoreFilter {
  /** Filter by student ID */
  studentId?: string | 'All';
  /** Filter by subject ID */
  subjectId?: string | 'All';
  /** Filter by academic year */
  year?: number | 'All';
  /** Filter by score type */
  scoreType?: ScoreType | 'All';
  /** Filter by status */
  status?: ScoreStatus | 'All';
  /** Filter by examination number */
  examinationNumber?: string;
  /** Filter by score range */
  scoreRange?: {
    min?: number;
    max?: number;
  };
  /** Filter by grade level */
  gradeLevel?: GradeLevel | 'All';
  /** Filter by pass status */
  passStatus?: boolean | 'All';
  /** Date range filter */
  dateRange?: {
    from?: Date;
    to?: Date;
  };
  /** Search query */
  searchQuery?: string;
}

/**
 * Score statistics
 * @description Statistical information about scores
 */
export interface ScoreStats {
  /** Total number of scores */
  totalScores: number;
  /** Average score */
  averageScore: number;
  /** Median score */
  medianScore: number;
  /** Score distribution by grade */
  gradeDistribution: Record<GradeLevel, number>;
  /** Score distribution by range */
  scoreRanges: {
    range: string;
    count: number;
    percentage: number;
  }[];
  /** Pass rate */
  passRate: number;
  /** Scores by status */
  byStatus: Record<ScoreStatus, number>;
  /** Scores by type */
  byType: Record<ScoreType, number>;
}

// Simplified related interfaces
interface StudentDto {
  id: string;
  firstName: string;
  lastName: string;
  fullName?: string;
  examinationNumber?: string;
}

interface SubjectDto {
  id: string;
  name: string;
  code: string;
  description?: string;
}

interface UserDto {
  id: string;
  firstName: string;
  lastName: string;
  fullName?: string;
  email?: string;
}

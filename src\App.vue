<script setup lang="ts">
import { computed, watch } from 'vue';
import { useRoute } from 'vue-router';

import DefaultLayout from '@/components/layouts/public/PublicLayoutPage.vue';
import AdminLayoutPage from './components/layouts/admin/AdminLayoutPage.vue';

const layouts = {
  DefaultLayout,
  AdminLayoutPage,
};

const route = useRoute();

const currentLayout = computed(() => {
  const layoutName = route.meta.layout || 'DefaultLayout';
  return layouts[layoutName as keyof typeof layouts] || layouts['DefaultLayout'];
});

watch(currentLayout, () => {
});
</script>

<template>
  <component :is="currentLayout">
    <router-view /> </component>
</template>

<style>
/* Global styles */
body {
  margin: 0;
  font-family: Arial, sans-serif;
}
</style>

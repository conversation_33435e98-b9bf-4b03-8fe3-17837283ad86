<template>
  <div class="min-h-screen bg-gray-50">
    <!-- Header -->
    <div class="bg-white shadow-sm border-b border-gray-200">
      <div class="px-6 py-4">
        <div class="flex items-center justify-between">
          <div>
            <h1 class="text-2xl font-semibold text-gray-900">Results Management</h1>
            <p class="text-sm text-gray-600 mt-1">View automatically calculated student grades and generate certificates</p>
          </div>
          <div class="flex items-center space-x-3">
            <button
              @click="refreshResults"
              :disabled="resultsStore.isLoading"
              class="inline-flex items-center px-4 py-2 bg-maneb-primary text-white text-sm font-medium rounded-lg hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-maneb-primary focus:ring-offset-2 transition-colors duration-200 disabled:opacity-50"
            >
              <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
              </svg>
              Refresh Results
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Content -->
    <div class="p-6">
      <!-- Filters -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Filter Results</h3>
        <div class="grid grid-cols-1 md:grid-cols-6 gap-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Search</label>
            <input
              v-model="resultsStore.examinationFilters.searchQuery"
              type="text"
              placeholder="Search by student ID or exam number..."
              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-maneb-primary focus:border-maneb-primary text-sm"
            />
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Year</label>
            <select
              v-model="resultsStore.examinationFilters.year"
              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-maneb-primary focus:border-maneb-primary text-sm"
            >
              <option value="All">All Years</option>
              <option v-for="year in resultsStore.availableExaminationYears" :key="year" :value="year">{{ year }}</option>
            </select>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Level</label>
            <select
              v-model="resultsStore.examinationFilters.level"
              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-maneb-primary focus:border-maneb-primary text-sm"
            >
              <option value="All">All Levels</option>
              <option v-for="level in resultsStore.availableLevels" :key="level" :value="level">{{ level }}</option>
            </select>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Grade</label>
            <select
              v-model="resultsStore.examinationFilters.gradeLevel"
              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-maneb-primary focus:border-maneb-primary text-sm"
            >
              <option value="All">All Grades</option>
              <option value="A">Grade A</option>
              <option value="B">Grade B</option>
              <option value="C">Grade C</option>
              <option value="D">Grade D</option>
              <option value="F">Grade F</option>
            </select>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Pass Status</label>
            <select
              v-model="resultsStore.examinationFilters.passStatus"
              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-maneb-primary focus:border-maneb-primary text-sm"
            >
              <option value="All">All Status</option>
              <option :value="true">Pass</option>
              <option :value="false">Fail</option>
            </select>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Exam Status</label>
            <select
              v-model="resultsStore.examinationFilters.status"
              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-maneb-primary focus:border-maneb-primary text-sm"
            >
              <option value="All">All Status</option>
              <option value="Complete">Complete</option>
              <option value="Incomplete">Incomplete</option>
              <option value="Pending">Pending</option>
            </select>
          </div>
        </div>
        <div class="mt-4 flex items-center space-x-3">
          <button
            @click="resultsStore.resetExaminationFilters()"
            class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-maneb-primary focus:ring-offset-2"
          >
            Reset Filters
          </button>
        </div>
      </div>

      <!-- Statistics Cards -->
      <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-10 h-10 bg-red-50 rounded-lg flex items-center justify-center">
                <svg class="w-6 h-6 text-maneb-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                </svg>
              </div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">Total Students</p>
              <p class="text-2xl font-semibold text-gray-900">{{ filteredTotalStudents }}</p>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-10 h-10 bg-red-50 rounded-lg flex items-center justify-center">
                <svg class="w-6 h-6 text-maneb-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
              </div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">Passed Students</p>
              <p class="text-2xl font-semibold text-gray-900">{{ filteredPassedStudents }}</p>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-10 h-10 bg-red-50 rounded-lg flex items-center justify-center">
                <svg class="w-6 h-6 text-maneb-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
              </div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">Failed Students</p>
              <p class="text-2xl font-semibold text-gray-900">{{ filteredFailedStudents }}</p>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-10 h-10 bg-red-50 rounded-lg flex items-center justify-center">
                <svg class="w-6 h-6 text-maneb-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                </svg>
              </div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">Pass Rate</p>
              <p class="text-2xl font-semibold text-gray-900">{{ filteredOverallPassRate.toFixed(1) }}%</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Examination Results Table -->
      <ExaminationResultsTable />
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, computed } from 'vue'
import { useResultsStore, useGradingStore } from '@/stores'
import ExaminationResultsTable from './components/ExaminationResultsTable.vue'
import sweetAlert from '@/utils/ui/sweetAlert'

// Stores
const resultsStore = useResultsStore()
const gradingStore = useGradingStore()

// Computed statistics for examination-level data (based on filtered results)
const filteredTotalStudents = computed(() => {
  return resultsStore.filteredExaminationResults.length
})

const filteredPassedStudents = computed(() => {
  // Consider a student "passed" if their pass rate is >= 50%
  return resultsStore.filteredExaminationResults.filter(result => result.passRate >= 50).length
})

const filteredFailedStudents = computed(() => {
  // Consider a student "failed" if their pass rate is < 50%
  return resultsStore.filteredExaminationResults.filter(result => result.passRate < 50).length
})

const filteredOverallPassRate = computed(() => {
  if (resultsStore.filteredExaminationResults.length === 0) return 0
  return (filteredPassedStudents.value / filteredTotalStudents.value) * 100
})

// Keep level-based statistics for reference if needed
const filteredMsceCount = computed(() => {
  return resultsStore.filteredExaminationResults.filter(result => result.level === 'MSCE').length
})

const filteredPlceCount = computed(() => {
  return resultsStore.filteredExaminationResults.filter(result => result.level === 'PLCE').length
})

const filteredAveragePassRate = computed(() => {
  if (resultsStore.filteredExaminationResults.length === 0) return 0
  const totalPassRate = resultsStore.filteredExaminationResults.reduce((sum, result) => sum + result.passRate, 0)
  return totalPassRate / resultsStore.filteredExaminationResults.length
})

// Keep original statistics for reference (unfiltered)
const msceCount = computed(() => {
  return resultsStore.examinationResults.filter(result => result.level === 'MSCE').length
})

const plceCount = computed(() => {
  return resultsStore.examinationResults.filter(result => result.level === 'PLCE').length
})

const averagePassRate = computed(() => {
  if (resultsStore.examinationResults.length === 0) return 0
  const totalPassRate = resultsStore.examinationResults.reduce((sum, result) => sum + result.passRate, 0)
  return totalPassRate / resultsStore.examinationResults.length
})

// Methods
const refreshResults = async () => {
  try {
    await resultsStore.generateResults(
      gradingStore.gradeBoundaries,
      gradingStore.subjects,
      gradingStore.papers
    )
    await sweetAlert.toast.success('Results refreshed successfully')
  } catch (error) {
    await sweetAlert.error('Error', 'Failed to refresh results')
  }
}

// Lifecycle
onMounted(async () => {
  try {
    // Initialize grading store data first
    await gradingStore.initializeStore()

    // Then generate results
    await resultsStore.generateResults(
      gradingStore.gradeBoundaries,
      gradingStore.subjects,
      gradingStore.papers
    )
  } catch (error) {
    // Failed to load results data (console logging removed)
  }
})
</script>

<style scoped>
/* MANEB Theme Colors */
.bg-maneb-primary {
  background-color: #a12c2c;
}

.text-maneb-primary {
  color: #a12c2c;
}

.border-maneb-primary {
  border-color: #a12c2c;
}

.hover\:bg-maneb-primary:hover {
  background-color: #a12c2c;
}

.focus\:ring-maneb-primary:focus {
  --tw-ring-color: #a12c2c;
}

.focus\:border-maneb-primary:focus {
  border-color: #a12c2c;
}
</style>

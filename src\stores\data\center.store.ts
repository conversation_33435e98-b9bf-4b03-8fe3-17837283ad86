import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { centerService } from '@/services/data/center.service'
import type { CenterDto } from '@/services/data/center.service'
import { useAuthStore } from '../auth/auth.store'

export const useCenterStore = defineStore('center', () => {
  // State
  const centers = ref<CenterDto[]>([])
  const isLoading = ref(false)
  const isSearching = ref(false)
  const error = ref<string | null>(null)
  const centersCache = ref(new Map<string, CenterDto[]>())

  // Getters
  const getCenterById = computed(() => {
    return (id: string) => centers.value.find(center => center.id === id)
  })

  const getCenterByNumber = computed(() => {
    return (centerNumber: string) => centers.value.find(center => center.manebcentreNo === centerNumber)
  })

  const getActiveCenters = computed(() => {
    return centers.value.filter(center => center.isActive)
  })

  const getCentersByExamType = computed(() => {
    return (examTypeId: string) => centers.value.filter(center => 
      center.examTypeId === examTypeId && center.isActive
    )
  })

  const getCentersBySchool = computed(() => {
    return (schoolId: string) => centers.value.filter(center => 
      center.schoolId === schoolId && center.isActive
    )
  })

  const hasError = computed(() => !!error.value)

  // Actions
  const clearError = () => {
    error.value = null
  }

  const clearCenters = () => {
    centers.value = []
  }

  const clearCache = () => {
    centersCache.value.clear()
  }

  /**
   * Fetch centers with optional filtering
   */
  const fetchCenters = async (filters: {
    pageSize?: number;
    search?: string;
    examTypeId?: string;
    schoolId?: string;
    isActive?: boolean;
  } = {}) => {
    isLoading.value = true
    error.value = null
    
    try {
      const authStore = useAuthStore()
      if (!authStore.isAuthenticated) {
        throw new Error('Authentication required')
      }

      // Create cache key for this filter combination
      const cacheKey = JSON.stringify(filters)
      
      // Check cache first
      if (centersCache.value.has(cacheKey)) {
        centers.value = centersCache.value.get(cacheKey)!
        console.log('✅ Centers loaded from cache:', centers.value.length, 'items')
        return centers.value
      }

      console.log('🔍 Fetching centers with filters:', filters)
      const response = await centerService.fetchCenters(filters)
      
      centers.value = response
      centersCache.value.set(cacheKey, response)
      
      console.log('✅ Centers loaded from API:', response.length, 'items')
      return response
    } catch (err: any) {
      error.value = err.message || 'Failed to fetch centers'
      console.error('❌ Error fetching centers:', err)
      throw err
    } finally {
      isLoading.value = false
    }
  }

  /**
   * Search centers by center number
   */
  const searchCentersByNumber = async (searchQuery: string, filters: {
    examTypeId?: string;
    schoolId?: string;
  } = {}) => {
    isSearching.value = true
    error.value = null
    
    try {
      const authStore = useAuthStore()
      if (!authStore.isAuthenticated) {
        throw new Error('Authentication required')
      }

      console.log('🔍 Searching centers by number:', searchQuery, 'with filters:', filters)
      
      const searchFilters = {
        pageSize: 100,
        search: searchQuery,
        ...filters
      }
      
      const response = await centerService.fetchCenters(searchFilters)
      
      // Apply client-side filtering for more precise results
      const filteredResults = response.filter((center: CenterDto) => {
        const centerNumberMatches = center.manebcentreNo.toLowerCase().includes(searchQuery.toLowerCase())
        const isActiveCenter = center.isActive
        const examTypeMatches = !filters.examTypeId || center.examTypeId === filters.examTypeId
        const schoolMatches = !filters.schoolId || center.schoolId === filters.schoolId
        
        return centerNumberMatches && isActiveCenter && examTypeMatches && schoolMatches
      })
      
      console.log('✅ Center search results:', filteredResults.length, 'centers found')
      return filteredResults
    } catch (err: any) {
      error.value = err.message || 'Failed to search centers'
      console.error('❌ Error searching centers:', err)
      throw err
    } finally {
      isSearching.value = false
    }
  }

  /**
   * Load centers for specific exam type and school combination
   */
  const loadCentersForFilters = async (examTypeId: string, schoolId?: string) => {
    try {
      console.log('🔍 Loading centers for exam type:', examTypeId, 'and school:', schoolId)
      
      const filters: any = {
        examTypeId,
        pageSize: 500,
        isActive: true
      }
      
      if (schoolId) {
        filters.schoolId = schoolId
      }
      
      const response = await fetchCenters(filters)
      console.log('✅ Centers loaded for filters:', response.length, 'centers')
      return response
    } catch (err: any) {
      console.error('❌ Error loading centers for filters:', err)
      throw err
    }
  }

  /**
   * Get center options for dropdowns
   */
  const getCenterOptions = (examTypeId?: string, schoolId?: string) => {
    let filteredCenters = getActiveCenters.value
    
    if (examTypeId) {
      filteredCenters = filteredCenters.filter(center => center.examTypeId === examTypeId)
    }
    
    if (schoolId) {
      filteredCenters = filteredCenters.filter(center => center.schoolId === schoolId)
    }
    
    return filteredCenters.map(center => ({
      id: center.id,
      name: `Center ${center.manebcentreNo}`,
      description: center.name || '',
      value: center.id,
      centerNumber: center.manebcentreNo
    }))
  }

  /**
   * Reset store state
   */
  const resetStore = () => {
    centers.value = []
    isLoading.value = false
    isSearching.value = false
    error.value = null
    centersCache.value.clear()
  }

  return {
    // State
    centers,
    isLoading,
    isSearching,
    error,
    
    // Getters
    getCenterById,
    getCenterByNumber,
    getActiveCenters,
    getCentersByExamType,
    getCentersBySchool,
    hasError,
    
    // Actions
    clearError,
    clearCenters,
    clearCache,
    fetchCenters,
    searchCentersByNumber,
    loadCentersForFilters,
    getCenterOptions,
    resetStore
  }
})

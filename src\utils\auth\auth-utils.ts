/**
 * Authentication and user utilities
 */

/**
 * Get current user ID from JWT token
 */
export function getCurrentUserId(): string | null {
  try {
    const token = localStorage.getItem('auth_token');
    if (!token) return null;
    
    // Decode JWT token to get user ID
    const payload = JSON.parse(atob(token.split('.')[1]));
    return payload['http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier'] || 
           payload.sub || 
           payload.user_id || 
           null;
  } catch (error) {
    console.error('Error getting current user ID:', error);
    return null;
  }
}

/**
 * Get current user email from JWT token
 */
export function getCurrentUserEmail(): string | null {
  try {
    const token = localStorage.getItem('auth_token');
    if (!token) return null;
    
    // Decode JWT token to get user email
    const payload = JSON.parse(atob(token.split('.')[1]));
    return payload['http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress'] || 
           payload.email || 
           null;
  } catch (error) {
    console.error('Error getting current user email:', error);
    return null;
  }
}

/**
 * Get current user name from JWT token
 */
export function getCurrentUserName(): string | null {
  try {
    const token = localStorage.getItem('auth_token');
    if (!token) return null;
    
    // Decode JWT token to get user name
    const payload = JSON.parse(atob(token.split('.')[1]));
    return payload['http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name'] || 
           payload.full_name || 
           payload.name || 
           null;
  } catch (error) {
    console.error('Error getting current user name:', error);
    return null;
  }
}

/**
 * Add audit fields to data for create operations
 */
export function addCreateAuditFields<T extends Record<string, any>>(data: T): T & {
  createdBy: string;
  dateCreated: string;
} {
  const currentUserId = getCurrentUserId();
  if (!currentUserId) {
    throw new Error('Unable to identify current user. Please log in again.');
  }

  return {
    ...data,
    createdBy: currentUserId,
    dateCreated: new Date().toISOString()
  };
}

/**
 * Add audit fields to data for update operations
 */
export function addUpdateAuditFields<T extends Record<string, any>>(
  data: T, 
  originalData: { createdBy?: string; dateCreated?: string }
): T & {
  createdBy: string;
  dateCreated: string;
} {
  const currentUserId = getCurrentUserId();
  if (!currentUserId) {
    throw new Error('Unable to identify current user. Please log in again.');
  }

  return {
    ...data,
    createdBy: originalData.createdBy || currentUserId, // Preserve original creator
    dateCreated: originalData.dateCreated || new Date().toISOString()
  };
}

/**
 * Validate that user is authenticated
 */
export function validateAuthentication(): void {
  const userId = getCurrentUserId();
  if (!userId) {
    throw new Error('User not authenticated. Please log in again.');
  }
}

/**
 * Check if token is expired
 */
export function isTokenExpired(): boolean {
  try {
    const token = localStorage.getItem('auth_token');
    if (!token) return true;
    
    const payload = JSON.parse(atob(token.split('.')[1]));
    const exp = payload.exp;
    
    if (!exp) return true;
    
    // Check if token is expired (exp is in seconds, Date.now() is in milliseconds)
    return Date.now() >= exp * 1000;
  } catch (error) {
    console.error('Error checking token expiration:', error);
    return true;
  }
}

/**
 * Get token expiration time
 */
export function getTokenExpiration(): Date | null {
  try {
    const token = localStorage.getItem('auth_token');
    if (!token) return null;
    
    const payload = JSON.parse(atob(token.split('.')[1]));
    const exp = payload.exp;
    
    if (!exp) return null;
    
    return new Date(exp * 1000);
  } catch (error) {
    console.error('Error getting token expiration:', error);
    return null;
  }
}

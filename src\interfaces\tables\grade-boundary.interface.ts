/**
 * @fileoverview Grade boundary table interfaces for MANEB Results Management System
 * @description Standardized interfaces for grade boundary data models
 */

import type { BaseEntity } from '../common/base.interface';
import type { ExamLevel, GradeLevel, ApprovalStatus } from '../common/exam-types.interface';

/**
 * Grade boundary entity (Internal EMS API)
 * @description Grade boundary as stored in the internal EMS database
 */
export interface GradeBoundaryEntity extends BaseEntity {
  /** Examination level (standardized naming) */
  examLevel: ExamLevel;
  /** Subject ID reference */
  subjectId: string;
  /** Academic year */
  year: number;
  /** Grade level */
  gradeLevel: GradeLevel;
  /** Lower score boundary (standardized naming) */
  lowerBound: number;
  /** Upper score boundary (standardized naming) */
  upperBound: number;
  /** Optional description */
  description?: string;
  /** Whether this boundary is currently active */
  isActive: boolean;
}

/**
 * Grade boundary DTO for API responses
 * @description Data transfer object for grade boundary data
 */
export interface GradeBoundaryDto extends GradeBoundaryEntity {
  /** Related subject information (populated) */
  subject?: SubjectDto;
  /** User who created this boundary (populated) */
  createdByUser?: UserDto;
}

/**
 * External grade boundary entity (External Grading API)
 * @description Grade boundary as stored in the external grading system
 */
export interface ExternalGradeBoundaryEntity {
  /** Unique boundary identifier (UUID) */
  boundaryId: string;
  /** Examination level */
  examLevel: string;
  /** Subject code */
  subjectCode: string;
  /** Grade letter */
  gradeLetter: string;
  /** Lower score boundary */
  lowerBound: number;
  /** Upper score boundary */
  upperBound: number;
  /** Approval status */
  approvalStatus: ApprovalStatus;
  /** ID of user who set this boundary */
  setById: string;
}

/**
 * Grade boundary set for comprehensive creation
 * @description A single grade boundary within a set
 */
export interface GradeBoundarySet {
  /** Grade level */
  gradeLevel: GradeLevel;
  /** Lower score boundary */
  lowerBound: number;
  /** Upper score boundary */
  upperBound: number;
  /** Optional description */
  description?: string;
  /** Award for this grade (manual override) */
  award?: string;
}

/**
 * Grade boundary configuration
 * @description Complete grade boundary configuration for a subject/year
 */
export interface GradeBoundaryConfiguration extends BaseEntity {
  /** Academic year */
  year: number;
  /** Whether this configuration is active */
  isActive: boolean;
  /** Optional description */
  description?: string;
  /** Array of grade boundaries */
  gradeBoundaries: GradeBoundaryEntity[];
}

/**
 * Grade boundary filter options
 * @description Filtering options for grade boundary queries
 */
export interface GradeBoundaryFilter {
  /** Filter by examination level */
  examLevel?: ExamLevel | 'All';
  /** Filter by subject ID */
  subjectId?: string | 'All';
  /** Filter by subject code (for external API) */
  subjectCode?: string | 'All';
  /** Filter by academic year */
  year?: number | 'All';
  /** Filter by grade level */
  gradeLevel?: GradeLevel | 'All';
  /** Filter by active status */
  isActive?: boolean | 'All';
  /** Filter by approval status (external API) */
  approvalStatus?: ApprovalStatus | 'All';
  /** Search query */
  searchQuery?: string;
}

/**
 * Grade boundary validation result
 * @description Result of grade boundary validation
 */
export interface GradeBoundaryValidation {
  /** Whether the boundaries are valid */
  isValid: boolean;
  /** Validation errors */
  errors: string[];
  /** Validation warnings */
  warnings: string[];
  /** Overlapping boundaries */
  overlaps?: Array<{
    grade1: GradeLevel;
    grade2: GradeLevel;
    overlapRange: [number, number];
  }>;
  /** Gaps between boundaries */
  gaps?: Array<{
    lowerGrade: GradeLevel;
    upperGrade: GradeLevel;
    gapRange: [number, number];
  }>;
}

/**
 * Grade boundary statistics
 * @description Statistical information about grade boundaries
 */
export interface GradeBoundaryStats {
  /** Total number of boundaries */
  totalBoundaries: number;
  /** Boundaries by exam level */
  byExamLevel: Record<ExamLevel, number>;
  /** Boundaries by grade level */
  byGradeLevel: Record<GradeLevel, number>;
  /** Boundaries by year */
  byYear: Record<number, number>;
  /** Active vs inactive boundaries */
  activeCount: number;
  /** Inactive boundaries count */
  inactiveCount: number;
}

// Re-export related interfaces for convenience
export type { UserDto } from './user.interface';

/**
 * Subject DTO (simplified for grade boundary context)
 * @description Subject information used in grade boundary context
 */
export interface SubjectDto {
  id: string;
  name: string;
  code: string;
  description?: string;
  isActive: boolean;
}

/**
 * @deprecated This local UserDto interface is no longer needed
 * Use the standardized UserDto from './user.interface' instead
 * This interface is maintained for backward compatibility only
 */

import apiClient from '@/services/core/api-client';
import type {
  RolePermissionDto,
  CreateRolePermissionRequest,
  UpdateRolePermissionRequest
} from '@/interfaces';

export class RolePermissionService {
  /**
   * Get all role permissions
   */
  async getRolePermissions(): Promise<RolePermissionDto[]> {
    try {
      return await apiClient.get<RolePermissionDto[]>('/api/RolePermission');
    } catch (error: any) {
      console.error('Get role permissions failed:', error);
      throw new Error(error.response?.data?.message || 'Failed to fetch role permissions');
    }
  }

  /**
   * Get role permission by ID
   */
  async getRolePermissionById(id: string): Promise<RolePermissionDto> {
    try {
      return await apiClient.get<RolePermissionDto>(`/api/RolePermission/${id}`);
    } catch (error: any) {
      console.error('Get role permission by ID failed:', error);
      throw new Error(error.response?.data?.message || 'Failed to fetch role permission');
    }
  }

  /**
   * Create new role permission
   */
  async createRolePermission(rolePermission: CreateRolePermissionRequest): Promise<RolePermissionDto> {
    try {
      return await apiClient.post<RolePermissionDto>('/api/RolePermission', rolePermission);
    } catch (error: any) {
      console.error('Create role permission failed:', error);
      throw new Error(error.response?.data?.message || 'Failed to create role permission');
    }
  }

  /**
   * Update existing role permission
   */
  async updateRolePermission(id: string, rolePermission: UpdateRolePermissionRequest): Promise<RolePermissionDto> {
    try {
      return await apiClient.put<RolePermissionDto>(`/api/RolePermission/${id}`, rolePermission);
    } catch (error: any) {
      console.error('Update role permission failed:', error);
      throw new Error(error.response?.data?.message || 'Failed to update role permission');
    }
  }

  /**
   * Delete role permission
   */
  async deleteRolePermission(id: string): Promise<void> {
    try {
      await apiClient.delete(`/api/RolePermission/${id}`);
    } catch (error: any) {
      console.error('Delete role permission failed:', error);
      throw new Error(error.response?.data?.message || 'Failed to delete role permission');
    }
  }

  /**
   * Get role permissions by role ID
   * Since the API doesn't have a dedicated endpoint for this, we fetch all permissions and filter
   */
  async getRolePermissionsByRoleId(roleId: string): Promise<RolePermissionDto[]> {
    try {
      console.log(`Fetching permissions for role ID: ${roleId}`);

      // The API doesn't have a dedicated endpoint for role permissions by role ID
      // So we fetch all permissions and filter by roleId
      const allPermissions = await apiClient.get<RolePermissionDto[]>('/api/RolePermission');
      const rolePermissions = allPermissions.filter(permission => permission.roleId === roleId);

      console.log(`Found ${rolePermissions.length} permissions for role ${roleId}`);
      return rolePermissions;
    } catch (error: any) {
      console.error('Get role permissions by role ID failed:', error);
      throw new Error(error.response?.data?.message || 'Failed to fetch role permissions by role ID');
    }
  }

  /**
   * Get role permissions by section ID
   * Since the API doesn't have a dedicated endpoint for this, we fetch all permissions and filter
   */
  async getRolePermissionsBySectionId(sectionId: string): Promise<RolePermissionDto[]> {
    try {
      console.log(`Fetching permissions for section ID: ${sectionId}`);

      // The API doesn't have a dedicated endpoint for section permissions
      // So we fetch all permissions and filter by sectionID
      const allPermissions = await apiClient.get<RolePermissionDto[]>('/api/RolePermission');
      const sectionPermissions = allPermissions.filter(permission => permission.sectionID === sectionId);

      console.log(`Found ${sectionPermissions.length} permissions for section ${sectionId}`);
      return sectionPermissions;
    } catch (error: any) {
      console.error('Get role permissions by section ID failed:', error);
      throw new Error(error.response?.data?.message || 'Failed to fetch role permissions by section ID');
    }
  }

  /**
   * Validate permissions before creation with comprehensive checks
   */
  private validatePermissions(permissions: CreateRolePermissionRequest[]): CreateRolePermissionRequest[] {
    const validPermissions: CreateRolePermissionRequest[] = [];
    const validActions = ['Login', 'Logout', 'Update', 'Delete', 'Authorise', 'Approve', 'Reject', 'SecondApprove', 'All', 'View', 'ViewAll', 'Create', 'Check', 'Verify', 'Active'];
    const validStatuses = ['Approved', 'Unapproved', 'Rejected', 'SecondApproved'];

    permissions.forEach((permission, index) => {
      const errors: string[] = [];

      // Check required fields
      if (!permission.roleId || permission.roleId.trim() === '') {
        errors.push('roleId is required and cannot be empty');
      }
      if (!permission.sectionID || permission.sectionID.trim() === '') {
        errors.push('sectionID is required and cannot be empty');
      }
      if (!permission.action || permission.action.trim() === '') {
        errors.push('action is required and cannot be empty');
      }

      // Validate action
      if (permission.action && !validActions.includes(permission.action)) {
        errors.push(`Invalid action: ${permission.action}. Valid actions are: ${validActions.join(', ')}`);
      }

      // Validate status
      if (permission.status && !validStatuses.includes(permission.status)) {
        errors.push(`Invalid status: ${permission.status}. Valid statuses are: ${validStatuses.join(', ')}`);
      }

      // Validate canAccess (should be boolean)
      if (typeof permission.canAccess !== 'boolean') {
        errors.push('canAccess must be a boolean value');
      }

      // If no errors, add to valid permissions
      if (errors.length === 0) {
        validPermissions.push({
          ...permission,
          roleId: permission.roleId.trim(),
          sectionID: permission.sectionID.trim(),
          action: permission.action.trim() as any,
          status: permission.status || 'Approved'
        });
      } else {
        console.warn(`Permission ${index + 1} validation failed:`, errors);
      }
    });

    console.log(`Validation complete: ${validPermissions.length}/${permissions.length} permissions are valid`);
    return validPermissions;
  }

  /**
   * Bulk assign permissions to a role (enhanced version)
   */
  async bulkAssignPermissionsToRole(roleId: string, permissions: CreateRolePermissionRequest[]): Promise<RolePermissionDto[]> {
    try {
      console.log(`Bulk assigning ${permissions.length} permissions to role ${roleId}`);

      // Validate permissions before creating
      const validPermissions = this.validatePermissions(permissions);
      if (validPermissions.length === 0) {
        throw new Error('No valid permissions to assign');
      }

      // Set roleId for all permissions
      const permissionsWithRoleId = validPermissions.map(permission => ({
        ...permission,
        roleId
      }));

      // The API doesn't have a bulk assign endpoint, so we create permissions individually
      console.log('Creating permissions individually (no bulk endpoint available)');
      return await this.bulkCreateRolePermissionsIndividually(permissionsWithRoleId);
    } catch (error: any) {
      console.error('Bulk assign permissions failed:', error);
      throw new Error(error.response?.data?.message || error.message || 'Failed to bulk assign permissions to role');
    }
  }

  /**
   * Replace all permissions for a role (remove existing and add new)
   */
  async replaceRolePermissions(roleId: string, newPermissions: CreateRolePermissionRequest[]): Promise<RolePermissionDto[]> {
    try {
      console.log(`Replacing all permissions for role ${roleId} with ${newPermissions.length} new permissions`);

      // Remove all existing permissions
      await this.bulkRemovePermissionsFromRole(roleId);

      // Add new permissions
      return await this.bulkAssignPermissionsToRole(roleId, newPermissions);
    } catch (error: any) {
      console.error('Replace role permissions failed:', error);
      throw new Error(error.response?.data?.message || error.message || 'Failed to replace role permissions');
    }
  }

  /**
   * Bulk remove permissions from a role
   */
  async bulkRemovePermissionsFromRole(roleId: string, permissionIds?: string[]): Promise<void> {
    try {
      console.log(`Bulk removing permissions from role ${roleId}`);

      // The API doesn't have a bulk remove endpoint, so we delete permissions individually
      console.log('Removing permissions individually (no bulk endpoint available)');

      // Fallback to individual deletion
      if (permissionIds && permissionIds.length > 0) {
        for (const permissionId of permissionIds) {
          await this.deleteRolePermission(permissionId);
        }
      } else {
        // Remove all permissions for the role
        const allPermissions = await this.getRolePermissionsByRoleId(roleId);
        for (const permission of allPermissions) {
          if (permission.id) {
            await this.deleteRolePermission(permission.id);
          }
        }
      }
    } catch (error: any) {
      console.error('Bulk remove permissions failed:', error);
      throw new Error(error.response?.data?.message || error.message || 'Failed to bulk remove permissions from role');
    }
  }

  /**
   * Helper method for individual permission creation
   */
  private async bulkCreateRolePermissionsIndividually(permissions: CreateRolePermissionRequest[]): Promise<RolePermissionDto[]> {
    const createdPermissions: RolePermissionDto[] = [];
    const failedPermissions: Array<{ permission: any; error: string }> = [];

    // Create permissions sequentially to avoid API rate limiting
    for (let i = 0; i < permissions.length; i++) {
      const permission = permissions[i];
      try {
        const created = await this.createRolePermission(permission);
        createdPermissions.push(created);
        
        // Small delay to prevent API rate limiting
        if (i < permissions.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 100));
        }
      } catch (permError: any) {
        const errorMessage = permError.response?.data?.message || permError.message || 'Unknown error';
        failedPermissions.push({
          permission,
          error: errorMessage
        });
      }
    }

    if (failedPermissions.length > 0) {
      console.warn('Failed permissions details:', failedPermissions);
      
      // If more than 50% failed, throw an error
      if (failedPermissions.length > permissions.length / 2) {
        const errorDetails = failedPermissions.map(fp =>
          `${fp.permission?.sectionID || 'unknown'}:${fp.permission?.action || 'unknown'} - ${fp.error}`
        ).join('; ');
        throw new Error(`Bulk creation failed: ${failedPermissions.length} out of ${permissions.length} permissions failed. Errors: ${errorDetails}`);
      }
    }

    return createdPermissions;
  }

  /**
   * Check if a role has a specific permission
   */
  async checkRolePermission(roleId: string, sectionId: string, action: string): Promise<boolean> {
    try {
      const rolePermissions = await this.getRolePermissionsByRoleId(roleId);
      const permission = rolePermissions.find(rp => 
        rp.sectionID === sectionId && 
        (rp.action === action || rp.action === 'All')
      );
      return permission ? permission.canAccess : false;
    } catch (error: any) {
      console.error('Check role permission failed:', error);
      return false;
    }
  }
}

export const rolePermissionService = new RolePermissionService();

<template>
  <div class="bg-white rounded-lg shadow-sm border border-gray-200">
    <!-- Table Header -->
    <div class="px-6 py-4 border-b border-gray-200">
      <div class="flex items-center justify-between">
        <h3 class="text-lg font-medium text-gray-900">Student Examination Results</h3>
        <div class="text-sm text-gray-600">
          Showing {{ resultsStore.filteredExaminationResults.length }} of {{ resultsStore.examinationResults.length }} students
        </div>
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="resultsStore.isLoading" class="p-8 text-center">
      <div class="inline-flex items-center">
        <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-maneb-primary" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
          <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
        Generating examination results...
      </div>
    </div>

    <!-- Empty State -->
    <div v-else-if="resultsStore.filteredExaminationResults.length === 0" class="p-8 text-center text-gray-500">
      <svg class="mx-auto h-12 w-12 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
      </svg>
      <p class="text-lg font-medium text-gray-900 mb-2">No examination results found</p>
      <p class="text-gray-600">Try adjusting your filters or refresh the results.</p>
    </div>

    <!-- Results Table -->
    <div v-else class="overflow-x-auto">
      <table class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
          <tr>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Student ID</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Level</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Year</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Average Score</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Average Grade</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
          </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
          <tr v-for="result in paginatedResults" :key="`${result.studentId}-${result.year}`" class="hover:bg-gray-50">
            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
              {{ result.studentId }}
              <div class="text-xs text-gray-500" v-if="result.examNumber">
                Exam #{{ result.examNumber }}
              </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
              <span :class="getLevelBadgeClass(result.level)" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">
                {{ result.level }}
              </span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
              {{ result.year }}
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
              <span class="font-medium">{{ result.averageScore.toFixed(1) }}%</span>
              <div class="text-xs text-gray-500">
                {{ result.passedSubjects }}/{{ result.totalSubjects }} subjects passed
              </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <span :class="resultsStore.getGradeBadgeClass(result.averageGrade)" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">
                {{ result.averageGrade }}
              </span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <span :class="getStatusBadgeClass(result.status)" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">
                {{ result.status }}
              </span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
              <div class="flex items-center justify-end space-x-1">
                <button
                  @click="viewSubjects(result)"
                  class="text-blue-600 hover:text-blue-900 p-2 rounded-md hover:bg-blue-50 transition-colors duration-200"
                  title="View Subjects"
                >
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                  </svg>
                </button>
                <button
                  @click="printTranscript(result)"
                  class="text-green-600 hover:text-green-700 p-2 rounded-md hover:bg-green-50 transition-colors duration-200"
                  title="Print Transcript"
                >
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                  </svg>
                </button>
                <button
                  @click="printCertificate(result)"
                  class="text-maneb-primary hover:text-red-700 p-2 rounded-md hover:bg-red-50 transition-colors duration-200"
                  title="Print Certificate"
                >
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z"></path>
                  </svg>
                </button>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- Pagination -->
    <div v-if="resultsStore.filteredExaminationResults.length > 0" class="bg-white px-4 py-3 border-t border-gray-200 sm:px-6">
      <div class="flex items-center justify-between">
        <div class="flex items-center">
          <p class="text-sm text-gray-700">
            Showing
            <span class="font-medium">{{ startIndex + 1 }}</span>
            to
            <span class="font-medium">{{ Math.min(endIndex, resultsStore.filteredExaminationResults.length) }}</span>
            of
            <span class="font-medium">{{ resultsStore.filteredExaminationResults.length }}</span>
            results
          </p>
        </div>
        <div class="flex items-center space-x-2">
          <button
            @click="previousPage"
            :disabled="currentPage === 1"
            class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
            </svg>
          </button>
          
          <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700">
            Page {{ currentPage }} of {{ totalPages }}
          </span>
          
          <button
            @click="nextPage"
            :disabled="currentPage === totalPages"
            class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
            </svg>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { useResultsStore, useGradingStore } from '@/stores'
import type { ExaminationResultDto } from '@/interfaces'
import sweetAlert from '@/utils/ui/sweetAlert'

// Router and stores
const router = useRouter()
const resultsStore = useResultsStore()
const gradingStore = useGradingStore()

// Dropdown state
const activeDropdown = ref<string | null>(null)

// Pagination
const currentPage = ref(1)
const itemsPerPage = ref(10)

const totalPages = computed(() => 
  Math.ceil(resultsStore.filteredExaminationResults.length / itemsPerPage.value)
)

const startIndex = computed(() => 
  (currentPage.value - 1) * itemsPerPage.value
)

const endIndex = computed(() => 
  startIndex.value + itemsPerPage.value
)

const paginatedResults = computed(() => 
  resultsStore.filteredExaminationResults.slice(startIndex.value, endIndex.value)
)

// Methods
const nextPage = () => {
  if (currentPage.value < totalPages.value) {
    currentPage.value++
  }
}

const previousPage = () => {
  if (currentPage.value > 1) {
    currentPage.value--
  }
}

const toggleDropdown = (id: string) => {
  activeDropdown.value = activeDropdown.value === id ? null : id
}

const closeDropdowns = (event?: Event) => {
  // Don't close if clicking inside a dropdown
  if (event && event.target) {
    const target = event.target as Element
    if (target.closest('.relative.inline-block.text-left')) {
      return
    }
  }
  activeDropdown.value = null
}

const getLevelBadgeClass = (level: string): string => {
  const classes = {
    'PLCE': 'bg-purple-100 text-purple-800',
    'JCE': 'bg-green-100 text-green-800',
    'MSCE': 'bg-blue-100 text-blue-800',
    'TTC': 'bg-orange-100 text-orange-800',
    'PSLCE': 'bg-gray-100 text-gray-800'
  }
  return classes[level as keyof typeof classes] || 'bg-gray-100 text-gray-800'
}

const getStatusBadgeClass = (status: string): string => {
  const classes = {
    'Complete': 'bg-green-100 text-green-800',
    'Incomplete': 'bg-yellow-100 text-yellow-800',
    'Pending': 'bg-gray-100 text-gray-800'
  }
  return classes[status as keyof typeof classes] || 'bg-gray-100 text-gray-800'
}

const viewSubjects = (result: ExaminationResultDto) => {
  closeDropdowns()
  router.push({
    name: 'admin.results.student-subjects',
    params: {
      studentId: result.studentId,
      year: result.year.toString()
    }
  })
}

const printTranscript = async (result: ExaminationResultDto) => {
  closeDropdowns()
  try {
    await sweetAlert.info(
      'Generating Transcript',
      `Generating academic transcript for ${result.studentId} for ${result.year} examination...`
    )

    // Generate comprehensive transcript for the student's entire exam year
    const transcriptWindow = window.open('', '_blank', 'width=800,height=600')
    if (transcriptWindow) {
      transcriptWindow.document.write(`
        <!DOCTYPE html>
        <html>
        <head>
          <title>MANEB Academic Transcript - ${result.studentId}</title>
          <style>
            body { font-family: Arial, sans-serif; margin: 0; padding: 20px; line-height: 1.6; }
            .transcript { max-width: 800px; margin: 0 auto; }
            .header { text-align: center; border-bottom: 3px solid #a12c2c; padding: 20px; margin-bottom: 30px; }
            .logo { width: 80px; height: 80px; margin: 0 auto 15px; }
            .student-info { background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 30px; }
            .info-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 15px; }
            .subject-section { margin-bottom: 30px; }
            .subject-header { background: #a12c2c; color: white; padding: 15px; border-radius: 8px 8px 0 0; }
            .papers-table { width: 100%; border-collapse: collapse; border: 1px solid #ddd; }
            .papers-table th, .papers-table td { padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }
            .papers-table th { background: #f8f9fa; font-weight: 600; }
            .grade-a { background: #dcfce7; color: #166534; padding: 4px 8px; border-radius: 4px; }
            .grade-b { background: #dbeafe; color: #1e40af; padding: 4px 8px; border-radius: 4px; }
            .grade-c { background: #fef3c7; color: #92400e; padding: 4px 8px; border-radius: 4px; }
            .grade-d { background: #fed7aa; color: #c2410c; padding: 4px 8px; border-radius: 4px; }
            .grade-f { background: #fecaca; color: #dc2626; padding: 4px 8px; border-radius: 4px; }
            .summary { background: #f8f9fa; padding: 20px; border-radius: 8px; margin-top: 30px; }
            .footer { text-align: center; margin-top: 40px; padding-top: 20px; border-top: 1px solid #ddd; }
            @media print { body { margin: 0; } .no-print { display: none; } }
          </style>
        </head>
        <body>
          <div class="transcript">
            <div class="header">
              <h1>MALAWI NATIONAL EXAMINATIONS BOARD</h1>
              <h2>ACADEMIC TRANSCRIPT</h2>
              <p>${result.level} Examination - ${result.year}</p>
            </div>

            <div class="student-info">
              <h3>Student Information</h3>
              <div class="info-grid">
                <div><strong>Student ID:</strong> ${result.studentId}</div>
                <div><strong>Exam Number:</strong> ${result.examNumber || 'N/A'}</div>
                <div><strong>Examination Level:</strong> ${result.level}</div>
                <div><strong>Examination Year:</strong> ${result.year}</div>
                <div><strong>Total Subjects:</strong> ${result.totalSubjects}</div>
                <div><strong>Overall Average:</strong> ${result.averageScore.toFixed(1)}% (Grade ${result.averageGrade})</div>
              </div>
            </div>

            ${result.subjects.map(subject => `
              <div class="subject-section">
                <div class="subject-header">
                  <h3>${subject.subjectName} (${subject.subjectCode})</h3>
                  <p>Subject Average: ${subject.averageScore.toFixed(1)}% | Grade: ${subject.overallGrade} | Status: ${subject.passStatus ? 'PASS' : 'FAIL'}</p>
                </div>
                <table class="papers-table">
                  <thead>
                    <tr>
                      <th>Paper</th>
                      <th>Code</th>
                      <th>Score</th>
                      <th>Grade</th>
                      <th>Status</th>
                    </tr>
                  </thead>
                  <tbody>
                    ${subject.papers.map(paper => `
                      <tr>
                        <td>${paper.paperName}</td>
                        <td>${paper.paperId}</td>
                        <td>${paper.score}%</td>
                        <td><span class="grade-${paper.grade.toLowerCase()}">${paper.grade}</span></td>
                        <td>${paper.passStatus ? 'Pass' : 'Fail'}</td>
                      </tr>
                    `).join('')}
                  </tbody>
                </table>
              </div>
            `).join('')}

            <div class="summary">
              <h3>Overall Performance Summary</h3>
              <div class="info-grid">
                <div><strong>Total Subjects Taken:</strong> ${result.totalSubjects}</div>
                <div><strong>Subjects Passed:</strong> ${result.passedSubjects}</div>
                <div><strong>Subjects Failed:</strong> ${result.failedSubjects}</div>
                <div><strong>Pass Rate:</strong> ${result.passRate.toFixed(1)}%</div>
                <div><strong>Overall Average Score:</strong> ${result.averageScore.toFixed(1)}%</div>
                <div><strong>Overall Grade:</strong> ${result.averageGrade}</div>
              </div>
            </div>

            <div class="footer">
              <p><strong>Generated on:</strong> ${new Date().toLocaleDateString()}</p>
              <p><strong>Malawi National Examinations Board</strong></p>
              <p>This is an official academic transcript</p>
            </div>
          </div>
          <scr` + `ipt>
            window.onload = function() {
              window.print();
              window.onafterprint = function() {
                window.close();
              }
            }
          </scr` + `ipt>
        </body>
        </html>
      `)
      transcriptWindow.document.close()
    } else {
      await sweetAlert.error('Error', 'Unable to open transcript window. Please check your popup blocker settings.')
    }

  } catch (error: any) {
    await sweetAlert.error('Error', error.message || 'Failed to generate transcript')
  }
}

const printCertificate = async (result: ExaminationResultDto) => {
  closeDropdowns()
  try {
    await sweetAlert.info(
      'Generating Certificate',
      `Generating certificate for ${result.studentId} for ${result.year} examination...`
    )

    // Generate comprehensive certificate for the student's entire exam year
    const certificate = {
      studentId: result.studentId,
      examNumber: result.examNumber,
      year: result.year,
      examSession: `${result.year} ${result.level} Main Session`,
      subjects: result.subjects,
      overallPerformance: {
        totalSubjects: result.totalSubjects,
        passedSubjects: result.passedSubjects,
        failedSubjects: result.failedSubjects,
        overallGrade: result.averageGrade,
        passRate: result.passRate
      },
      certificateNumber: `MANEB/${result.level}/${result.year}/${result.studentId}`,
      issueDate: new Date(),
      issuedBy: 'Malawi National Examinations Board'
    }

    // Open certificate in new window for printing
    const certificateWindow = window.open('', '_blank', 'width=800,height=600')
    if (certificateWindow) {
      certificateWindow.document.write(`
        <!DOCTYPE html>
        <html>
        <head>
          <title>MANEB Certificate - ${certificate.studentId}</title>
          <style>
            body { font-family: 'Times New Roman', serif; margin: 0; padding: 40px; background: #f8f9fa; }
            .certificate { max-width: 800px; margin: 0 auto; background: white; padding: 60px; border: 8px solid #a12c2c; border-radius: 15px; box-shadow: 0 0 20px rgba(0,0,0,0.1); }
            .header { text-align: center; margin-bottom: 40px; }
            .logo { width: 100px; height: 100px; margin: 0 auto 20px; background: #a12c2c; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-size: 24px; font-weight: bold; }
            .title { font-size: 32px; font-weight: bold; color: #a12c2c; margin: 20px 0; text-transform: uppercase; letter-spacing: 2px; }
            .subtitle { font-size: 18px; color: #666; margin-bottom: 30px; }
            .certificate-body { text-align: center; margin: 40px 0; }
            .student-name { font-size: 28px; font-weight: bold; color: #333; margin: 20px 0; text-decoration: underline; }
            .achievement { font-size: 16px; line-height: 1.8; color: #555; margin: 30px 0; }
            .subjects-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 30px 0; text-align: left; }
            .subject-item { background: #f8f9fa; padding: 15px; border-radius: 8px; border-left: 4px solid #a12c2c; }
            .subject-name { font-weight: bold; color: #333; margin-bottom: 5px; }
            .subject-grade { color: #a12c2c; font-size: 18px; font-weight: bold; }
            .performance-summary { background: #a12c2c; color: white; padding: 20px; border-radius: 8px; margin: 30px 0; }
            .footer { display: flex; justify-content: space-between; align-items: center; margin-top: 60px; padding-top: 30px; border-top: 2px solid #a12c2c; }
            .signature-section { text-align: center; }
            .signature-line { border-bottom: 2px solid #333; width: 200px; margin: 30px auto 10px; }
            .date { font-style: italic; color: #666; }
            .certificate-number { position: absolute; top: 20px; right: 20px; font-size: 12px; color: #666; }
            @media print { body { background: white; } .certificate { border: 3px solid #a12c2c; box-shadow: none; } }
          </style>
        </head>
        <body>
          <div class="certificate-number">Certificate No: ${certificate.certificateNumber}</div>
          <div class="certificate">
            <div class="header">
              <div class="logo">MANEB</div>
              <h1 class="title">Malawi National Examinations Board</h1>
              <h2 class="subtitle">Certificate of Examination</h2>
              <p class="date">${certificate.examSession}</p>
            </div>

            <div class="certificate-body">
              <p class="achievement">This is to certify that</p>
              <div class="student-name">${certificate.studentId}</div>
              <p class="achievement">
                has successfully completed the ${result.level} examination in ${certificate.year}
                and has been awarded the following grades:
              </p>

              <div class="subjects-grid">
                ${certificate.subjects.map(subject => `
                  <div class="subject-item">
                    <div class="subject-name">${subject.subjectName}</div>
                    <div class="subject-grade">Grade ${subject.overallGrade}</div>
                    <div style="font-size: 12px; color: #666;">${subject.passStatus ? 'PASS' : 'FAIL'}</div>
                  </div>
                `).join('')}
              </div>

              <div class="performance-summary">
                <h3 style="margin-top: 0;">Overall Performance</h3>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 15px; text-align: center;">
                  <div>
                    <div style="font-size: 24px; font-weight: bold;">${certificate.overallPerformance.totalSubjects}</div>
                    <div>Total Subjects</div>
                  </div>
                  <div>
                    <div style="font-size: 24px; font-weight: bold;">${certificate.overallPerformance.passedSubjects}</div>
                    <div>Subjects Passed</div>
                  </div>
                  <div>
                    <div style="font-size: 24px; font-weight: bold;">${certificate.overallPerformance.overallGrade}</div>
                    <div>Overall Grade</div>
                  </div>
                  <div>
                    <div style="font-size: 24px; font-weight: bold;">${certificate.overallPerformance.passRate.toFixed(1)}%</div>
                    <div>Pass Rate</div>
                  </div>
                </div>
              </div>
            </div>

            <div class="footer">
              <div class="signature-section">
                <div class="signature-line"></div>
                <p><strong>Director of Examinations</strong></p>
                <p>Malawi National Examinations Board</p>
              </div>
              <div class="signature-section">
                <div class="signature-line"></div>
                <p><strong>Date of Issue</strong></p>
                <p>${new Date(certificate.issueDate).toLocaleDateString()}</p>
              </div>
            </div>
          </div>
          <scr` + `ipt>
            window.onload = function() {
              window.print();
              window.onafterprint = function() {
                window.close();
              }
            }
          </scr` + `ipt>
        </body>
        </html>
      `)
      certificateWindow.document.close()
    } else {
      await sweetAlert.error('Error', 'Unable to open certificate window. Please check your popup blocker settings.')
    }

  } catch (error: any) {
    await sweetAlert.error('Error', error.message || 'Failed to generate certificate')
  }
}

// Reset pagination when filters change
const resetPagination = () => {
  currentPage.value = 1
}

// Watch for filter changes to reset pagination
import { watch } from 'vue'
watch(() => resultsStore.examinationFilters, resetPagination, { deep: true })

// Close dropdowns when clicking outside
onMounted(() => {
  const handleClickOutside = (event: Event) => {
    closeDropdowns(event)
  }
  document.addEventListener('click', handleClickOutside)

  // Store the handler for cleanup
  ;(document as any)._dropdownClickHandler = handleClickOutside
})

onUnmounted(() => {
  if ((document as any)._dropdownClickHandler) {
    document.removeEventListener('click', (document as any)._dropdownClickHandler)
    delete (document as any)._dropdownClickHandler
  }
})
</script>

<style scoped>
/* MANEB Theme Colors */
.bg-maneb-primary {
  background-color: #a12c2c;
}

.text-maneb-primary {
  color: #a12c2c;
}

.focus\:ring-maneb-primary:focus {
  --tw-ring-color: #a12c2c;
}
</style>

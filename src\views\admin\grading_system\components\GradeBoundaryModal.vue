<template>
  <div class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50" v-if="isOpen">
    <div class="relative top-20 mx-auto p-5 border w-11/12 max-w-2xl shadow-lg rounded-md bg-white">
      <!-- Header -->
      <div class="flex items-center justify-between pb-4 border-b border-gray-200">
        <h3 class="text-lg font-semibold text-gray-900">
          {{ isEditing ? 'Edit Grade Boundary' : 'Add Grade Boundary' }}
        </h3>
        <button
          @click="closeModal"
          class="text-gray-400 hover:text-gray-600 transition-colors duration-200"
        >
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>

      <!-- Form -->
      <div class="mt-6">
        <form @submit.prevent="handleSubmit" class="space-y-4">
          <!-- Exam Level -->
          <div>
            <label for="examLevel" class="block text-sm font-medium text-gray-700 mb-1">
              Exam Level <span class="text-red-500">*</span>
            </label>
            <select
              id="examLevel"
              v-model="formData.examLevel"
              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-maneb-primary focus:border-maneb-primary"
              required
            >
              <option value="">Select Exam Level</option>
              <option value="PLCE">PLCE</option>
              <option value="MSCE">MSCE</option>
              <option value="TTC">TTC</option>
            </select>
          </div>

          <!-- Subject -->
          <div>
            <label for="subjectCode" class="block text-sm font-medium text-gray-700 mb-1">
              Subject <span class="text-red-500">*</span>
            </label>
            <select
              id="subjectCode"
              v-model="formData.subjectCode"
              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-maneb-primary focus:border-maneb-primary"
              required
            >
              <option value="">Select Subject</option>
              <option
                v-for="subject in availableSubjects"
                :key="subject.id"
                :value="subject.id"
              >
                {{ subject.name }} ({{ subject.code }})
              </option>
            </select>
          </div>

          <!-- Grade -->
          <div>
            <label for="grade" class="block text-sm font-medium text-gray-700 mb-1">
              Grade <span class="text-red-500">*</span>
            </label>
            <select
              id="grade"
              v-model="formData.grade"
              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-maneb-primary focus:border-maneb-primary"
              required
            >
              <option value="">Select Grade</option>
              <option value="A">A - Distinction</option>
              <option value="B">B - Credit</option>
              <option value="C">C - Pass</option>
              <option value="D">D - Pass</option>
              <option value="F">F - Fail</option>
              <option value="1">1 - Distinction (MSCE)</option>
              <option value="2">2 - Credit (MSCE)</option>
              <option value="3">3 - Credit (MSCE)</option>
              <option value="4">4 - Pass (MSCE)</option>
              <option value="5">5 - Pass (MSCE)</option>
              <option value="6">6 - Pass (MSCE)</option>
              <option value="7">7 - Pass (MSCE)</option>
              <option value="8">8 - Fail (MSCE)</option>
              <option value="9">9 - Fail (MSCE)</option>
            </select>

            <!-- Award field with auto-suggestion -->
            <div v-if="formData.grade" class="mt-3">
              <label for="award" class="block text-sm font-medium text-gray-700 mb-1">
                Award
              </label>
              <div class="relative">
                <input
                  type="text"
                  id="award"
                  v-model="formData.award"
                  :placeholder="getAwardForGrade(formData.grade)"
                  class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-maneb-primary focus:border-maneb-primary block w-full p-2.5"
                />
                <button
                  type="button"
                  @click="formData.award = getAwardForGrade(formData.grade)"
                  class="absolute inset-y-0 right-0 flex items-center pr-3 text-sm text-gray-500 hover:text-maneb-primary"
                  title="Use auto-generated award"
                >
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                  </svg>
                </button>
              </div>
              <div class="mt-1 flex items-center justify-between">
                <span class="text-xs text-gray-500">
                  Auto-suggested: {{ getAwardForGrade(formData.grade) }}
                </span>
                <span :class="[
                  'text-xs px-2 py-1 rounded-full font-medium',
                  (formData.award || getAwardForGrade(formData.grade)) === 'Distinction' ? 'bg-green-100 text-green-700' :
                  (formData.award || getAwardForGrade(formData.grade)) === 'Credit' ? 'bg-blue-100 text-blue-700' :
                  (formData.award || getAwardForGrade(formData.grade)) === 'Pass' ? 'bg-yellow-100 text-yellow-700' :
                  'bg-red-100 text-red-700'
                ]">
                  {{ formData.award || getAwardForGrade(formData.grade) }}
                </span>
              </div>
            </div>
          </div>

          <!-- Score Range -->
          <div class="grid grid-cols-2 gap-4">
            <div>
              <label for="lowerBound" class="block text-sm font-medium text-gray-700 mb-1">
                Lower Bound (%) <span class="text-red-500">*</span>
              </label>
              <input
                id="lowerBound"
                type="number"
                v-model.number="formData.lowerBound"
                min="0"
                max="100"
                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-maneb-primary focus:border-maneb-primary"
                required
              />
            </div>
            <div>
              <label for="upperBound" class="block text-sm font-medium text-gray-700 mb-1">
                Upper Bound (%) <span class="text-red-500">*</span>
              </label>
              <input
                id="upperBound"
                type="number"
                v-model.number="formData.upperBound"
                min="0"
                max="100"
                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-maneb-primary focus:border-maneb-primary"
                required
              />
            </div>
          </div>

          <!-- Approval Status -->
          <div>
            <label for="approvalStatus" class="block text-sm font-medium text-gray-700 mb-1">
              Approval Status
            </label>
            <select
              id="approvalStatus"
              v-model="formData.approvalStatus"
              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-maneb-primary focus:border-maneb-primary"
            >
              <option value="Pending">Pending</option>
              <option value="Approved">Approved</option>
              <option value="Rejected">Rejected</option>
            </select>
          </div>

          <!-- Validation Errors -->
          <div v-if="validationErrors.length > 0" class="bg-red-50 border border-red-200 rounded-lg p-3">
            <div class="flex">
              <svg class="w-5 h-5 text-red-400 mr-2 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
              </svg>
              <div>
                <h4 class="text-sm font-medium text-red-800">Please fix the following errors:</h4>
                <ul class="mt-1 text-sm text-red-700 list-disc list-inside">
                  <li v-for="error in validationErrors" :key="error">{{ error }}</li>
                </ul>
              </div>
            </div>
          </div>

          <!-- Form Actions -->
          <div class="flex items-center justify-end space-x-3 pt-4 border-t border-gray-200">
            <button
              type="button"
              @click="closeModal"
              class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
            >
              Cancel
            </button>
            <button
              type="submit"
              :disabled="isLoading"
              class="px-4 py-2 text-sm font-medium text-white bg-maneb-primary border border-transparent rounded-lg hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-maneb-primary focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <span v-if="isLoading" class="flex items-center">
                <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                  <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Processing...
              </span>
              <span v-else>{{ isEditing ? 'Update Boundary' : 'Create Boundary' }}</span>
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { useGradingStore, useGradingApiStore } from '@/stores'
import type { CreateGradeBoundaryRequest, UpdateGradeBoundaryRequest } from '@/interfaces/external/results-api.interface'
import sweetAlert from '@/utils/ui/sweetAlert'

// Props
interface Props {
  isOpen: boolean
  boundary?: any | null
  preSelectedSubject?: string | null
  preSelectedYear?: number | null
}

const props = withDefaults(defineProps<Props>(), {
  isOpen: false,
  boundary: null,
  preSelectedSubject: null,
  preSelectedYear: null
})

// Emits
const emit = defineEmits<{
  close: []
  success: []
}>()

// Stores
const gradingStore = useGradingStore()
const gradingApiStore = useGradingApiStore()

// State
const isLoading = ref(false)
const validationErrors = ref<string[]>([])

// Form data
const formData = ref({
  examLevel: '',
  subjectCode: '',
  grade: '',
  lowerBound: 0,
  upperBound: 0,
  approvalStatus: 'Pending',
  award: '' // Manual override field for award
})

// Computed
const isEditing = computed(() => !!props.boundary?.id)

// Helper function to get numeric value for grades
const getGradeValue = (grade: string): number => {
  if (!grade) return 9 // Defensive check for undefined/empty grade

  const gradeValues: Record<string, number> = {
    'A': 1, '1': 1,
    'B': 2, '2': 2,
    'C': 3, '3': 3,
    'D': 4, '4': 4,
    'F': 9, '5': 5, '6': 6, '7': 7, '8': 8, '9': 9
  }
  return gradeValues[grade] || 9
}

// Helper function to get award based on grade level
const getAwardForGrade = (grade: string): string => {
  if (!grade) return 'Pass' // Defensive check for undefined/empty grade

  const gradeAwards: Record<string, string> = {
    // Letter grades (PLCE, TTC, JCE)
    'A': 'Distinction',
    'B': 'Credit',
    'C': 'Pass',
    'D': 'Pass',
    'F': 'Fail',
    // Numeric grades (MSCE)
    '1': 'Distinction',
    '2': 'Credit',
    '3': 'Credit',
    '4': 'Pass',
    '5': 'Pass',
    '6': 'Pass',
    '7': 'Pass',
    '8': 'Fail',
    '9': 'Fail'
  }
  return gradeAwards[grade] || 'Pass'
}

const availableSubjects = computed(() => {
  return gradingStore.subjects || []
})

// Methods
const validateForm = (): boolean => {
  validationErrors.value = []

  if (!formData.value.examLevel) {
    validationErrors.value.push('Exam level is required')
  }

  if (!formData.value.subjectCode) {
    validationErrors.value.push('Subject is required')
  }

  if (!formData.value.grade) {
    validationErrors.value.push('Grade is required')
  }

  if (formData.value.lowerBound < 0 || formData.value.lowerBound > 100) {
    validationErrors.value.push('Lower bound must be between 0 and 100')
  }

  if (formData.value.upperBound < 0 || formData.value.upperBound > 100) {
    validationErrors.value.push('Upper bound must be between 0 and 100')
  }

  if (formData.value.lowerBound >= formData.value.upperBound) {
    validationErrors.value.push('Lower bound must be less than upper bound')
  }

  return validationErrors.value.length === 0
}

const handleSubmit = async () => {
  if (!validateForm()) {
    return
  }

  isLoading.value = true

  try {
    if (isEditing.value) {
      await updateBoundary()
    } else {
      await createBoundary()
    }

    await sweetAlert.success('Success', `Grade boundary ${isEditing.value ? 'updated' : 'created'} successfully`)
    resetForm() // Reset form after successful submission
    emit('success')
    closeModal() // Close modal after successful submission
  } catch (error) {
    console.error('Error saving grade boundary:', error)
    await sweetAlert.error('Error', `Failed to ${isEditing.value ? 'update' : 'create'} grade boundary`)
  } finally {
    isLoading.value = false
  }
}

const createBoundary = async () => {
  // Find the selected subject to get its ID
  const selectedSubject = gradingStore.subjects.find((s: any) => s.code === formData.value.subjectCode || s.id === formData.value.subjectCode)

  // Check if this is MSCE (uses numeric grades) or other exam types (uses letter grades)
  const isMSCE = formData.value.examLevel === 'MSCE'
  const isNumericGrade = /^\d+$/.test(formData.value.grade) // Check if grade is numeric

  const request = {
    examLevel: formData.value.examLevel,
    subjectCode: selectedSubject?.id || formData.value.subjectCode, // Use subject ID as subjectCode value
    grade: (isMSCE || isNumericGrade) ? null : formData.value.grade, // null for MSCE/numeric grades
    lowerBound: formData.value.lowerBound,
    upperBound: formData.value.upperBound,
    isActive: formData.value.approvalStatus === 'Approved',
    approvalStatus: formData.value.approvalStatus,
    examYear: new Date().getFullYear().toString(),
    cohort: new Date().getFullYear().toString(),
    award: formData.value.award || getAwardForGrade(formData.value.grade), // Use manual award or auto-generated
    gradeValue: (isMSCE || isNumericGrade) ? parseInt(formData.value.grade) : null // null for letter grades
  }

  await gradingApiStore.createGradeBoundary(request)
}

const updateBoundary = async () => {
  // Find the selected subject to get its ID
  const selectedSubject = gradingStore.subjects.find((s: any) => s.code === formData.value.subjectCode || s.id === formData.value.subjectCode)

  // Check if this is MSCE (uses numeric grades) or other exam types (uses letter grades)
  const isMSCE = formData.value.examLevel === 'MSCE'
  const isNumericGrade = /^\d+$/.test(formData.value.grade) // Check if grade is numeric

  const request: UpdateGradeBoundaryRequest = {
    examLevel: formData.value.examLevel,
    subjectCode: selectedSubject?.id || formData.value.subjectCode, // Use subject ID as subjectCode value
    grade: (isMSCE || isNumericGrade) ? null : formData.value.grade, // null for MSCE/numeric grades
    lowerBound: formData.value.lowerBound,
    upperBound: formData.value.upperBound,
    isActive: formData.value.approvalStatus === 'Approved',
    approvalStatus: formData.value.approvalStatus,
    award: formData.value.award || getAwardForGrade(formData.value.grade), // Use manual award or auto-generated
    gradeValue: (isMSCE || isNumericGrade) ? parseInt(formData.value.grade) : null // null for letter grades
  }

  // Note: updateGradeBoundary method needs to be implemented in the store
  console.log('Update boundary request:', request)
  await sweetAlert.info('Feature Not Available', 'Update functionality will be implemented when the API endpoint becomes available.')
}

const closeModal = () => {
  emit('close')
}

const resetForm = () => {
  formData.value = {
    examLevel: '',
    subjectCode: '',
    grade: '',
    lowerBound: 0,
    upperBound: 0,
    approvalStatus: 'Pending',
    award: ''
  }
  validationErrors.value = []
}

// Watchers
watch(() => props.isOpen, (newValue) => {
  if (newValue) {
    if (props.boundary) {
      // Find the subject by code or ID to get the correct ID for the form
      let subjectId = props.boundary.subjectCode || ''
      const subjectByCode = gradingStore.subjects.find((s: any) => s.code === props.boundary.subjectCode)
      const subjectById = gradingStore.subjects.find((s: any) => s.id === props.boundary.subjectCode)

      if (subjectByCode) {
        subjectId = subjectByCode.id
      } else if (subjectById) {
        subjectId = subjectById.id
      }

      // Populate form with existing boundary data
      formData.value = {
        examLevel: props.boundary.examType || '',
        subjectCode: subjectId,
        grade: props.boundary.gradeLevel || '',
        lowerBound: props.boundary.minScore || 0,
        upperBound: props.boundary.maxScore || 0,
        approvalStatus: props.boundary.isActive ? 'Approved' : 'Pending',
        award: props.boundary.award || getAwardForGrade(props.boundary.gradeLevel || '')
      }
    } else {
      resetForm()
      // Set pre-selected values if provided
      if (props.preSelectedSubject) {
        const subject = gradingStore.subjects.find((s: any) => s.id === props.preSelectedSubject)
        if (subject) {
          formData.value.subjectCode = subject.id // Use subject ID instead of code
        }
      }
    }
  }
})

// Auto-populate award when grade changes (only if award is empty)
watch(() => formData.value.grade, (newGrade) => {
  if (newGrade && !formData.value.award) {
    formData.value.award = getAwardForGrade(newGrade)
  }
})

// Lifecycle
onMounted(async () => {
  // Load subjects if not already loaded
  if (gradingStore.subjects.length === 0) {
    await gradingStore.fetchSubjects()
  }
})
</script>

<style scoped>
/* MANEB Theme Colors */
.bg-maneb-primary {
  background-color: #a12c2c;
}

.text-maneb-primary {
  color: #a12c2c;
}

.border-maneb-primary {
  border-color: #a12c2c;
}

.hover\:bg-maneb-primary:hover {
  background-color: #a12c2c;
}

.focus\:ring-maneb-primary:focus {
  --tw-ring-color: #a12c2c;
}

.focus\:border-maneb-primary:focus {
  border-color: #a12c2c;
}
</style>

import { computed } from 'vue';
import { useFormValidation, ValidationRules, type FormValidationConfig } from './useFormValidation';

/**
 * Grade boundary form data interface
 */
export interface GradeBoundaryFormData {
  examLevel: string;
  subjectCode: string;
  subjectName: string;
  paperId: string;
  paperName: string;
  grade: string;
  minScore: number;
  maxScore: number;
  description?: string;
  isActive?: boolean;
  examYear?: number;
}

/**
 * Grade boundary validation configuration
 */
const gradeBoundaryValidationConfig: FormValidationConfig = {
  fields: [
    {
      field: 'examLevel',
      rules: [
        ValidationRules.required('Exam level is required')
      ]
    },
    {
      field: 'subjectCode',
      rules: [
        ValidationRules.required('Subject code is required'),
        ValidationRules.minLength(2, 'Subject code must be at least 2 characters'),
        ValidationRules.maxLength(10, 'Subject code must be no more than 10 characters')
      ]
    },
    {
      field: 'subjectName',
      rules: [
        ValidationRules.required('Subject name is required'),
        ValidationRules.minLength(2, 'Subject name must be at least 2 characters'),
        ValidationRules.maxLength(100, 'Subject name must be no more than 100 characters')
      ]
    },
    {
      field: 'paperId',
      rules: [
        ValidationRules.required('Paper code is required'),
        ValidationRules.minLength(1, 'Paper code must be at least 1 character'),
        ValidationRules.maxLength(10, 'Paper code must be no more than 10 characters')
      ]
    },
    {
      field: 'paperName',
      rules: [
        ValidationRules.required('Paper name is required'),
        ValidationRules.minLength(2, 'Paper name must be at least 2 characters'),
        ValidationRules.maxLength(100, 'Paper name must be no more than 100 characters')
      ]
    },
    {
      field: 'grade',
      rules: [
        ValidationRules.required('Grade is required'),
        ValidationRules.minLength(1, 'Grade must be at least 1 character'),
        ValidationRules.maxLength(5, 'Grade must be no more than 5 characters')
      ]
    },
    {
      field: 'minScore',
      rules: [
        ValidationRules.required('Minimum score is required'),
        ValidationRules.numeric('Minimum score must be a valid number'),
        ValidationRules.min(0, 'Minimum score cannot be negative'),
        ValidationRules.max(100, 'Minimum score cannot exceed 100')
      ]
    },
    {
      field: 'maxScore',
      rules: [
        ValidationRules.required('Maximum score is required'),
        ValidationRules.numeric('Maximum score must be a valid number'),
        ValidationRules.min(0, 'Maximum score cannot be negative'),
        ValidationRules.max(100, 'Maximum score cannot exceed 100')
      ]
    },
    {
      field: 'examYear',
      rules: [
        ValidationRules.numeric('Exam year must be a valid number'),
        ValidationRules.custom(
          (value: number) => !value || (value >= 2000 && value <= new Date().getFullYear() + 5),
          'Exam year must be between 2000 and 5 years in the future'
        )
      ]
    }
  ],
  validateOnSubmit: true,
  stopOnFirstError: false
};

/**
 * Grade boundary validation composable
 */
export function useGradeBoundaryValidation() {
  const validation = useFormValidation(gradeBoundaryValidationConfig);

  /**
   * Validate grade boundary form data
   */
  const validateGradeBoundaryForm = (formData: GradeBoundaryFormData) => {
    const result = validation.validateForm(formData);
    
    // Add custom cross-field validations
    const warnings: string[] = [];
    
    // Check if min score is greater than max score
    if (formData.minScore > formData.maxScore) {
      validation.setFieldError('minScore', 'Minimum score cannot be greater than maximum score');
      result.isValid = false;
    }
    
    // Check for logical grade boundaries
    if (formData.minScore === formData.maxScore && formData.minScore > 0) {
      warnings.push('Minimum and maximum scores are the same - this creates a single-point grade boundary');
    }
    
    // Check for reasonable score ranges
    const scoreRange = formData.maxScore - formData.minScore;
    if (scoreRange > 0 && scoreRange < 5) {
      warnings.push('Score range is very narrow (less than 5 points) - consider if this is intentional');
    }
    
    // Add warnings to result
    if (warnings.length > 0) {
      result.warnings = { general: warnings.join('; ') };
    }
    
    return result;
  };

  /**
   * Validate specific grade boundary field
   */
  const validateGradeBoundaryField = (field: keyof GradeBoundaryFormData, value: any, formData: GradeBoundaryFormData) => {
    return validation.validateField(field, value, formData);
  };

  /**
   * Get validation warnings for grade boundaries
   */
  const getValidationWarnings = computed(() => {
    const warnings: string[] = [];
    
    // Add any general warnings from the validation state
    if (validation.warnings.general) {
      warnings.push(validation.warnings.general);
    }
    
    return warnings;
  });

  /**
   * Check if grade boundary data is logically consistent
   */
  const validateGradeBoundaryLogic = (formData: GradeBoundaryFormData): { isValid: boolean; warnings: string[] } => {
    const warnings: string[] = [];
    let isValid = true;

    // Score range validation
    if (formData.minScore > formData.maxScore) {
      warnings.push('Minimum score cannot be greater than maximum score');
      isValid = false;
    }

    // Grade naming conventions
    const gradePattern = /^[A-F][+-]?$|^[1-9]$|^PASS$|^FAIL$/i;
    if (!gradePattern.test(formData.grade)) {
      warnings.push('Grade should follow standard conventions (A-F, 1-9, PASS, FAIL)');
    }

    // Subject and paper code consistency
    if (formData.subjectCode && formData.paperId) {
      if (!formData.paperId.toLowerCase().includes(formData.subjectCode.toLowerCase().substring(0, 3))) {
        warnings.push('Paper code should typically include part of the subject code');
      }
    }

    // Exam level and score range consistency
    if (formData.examLevel === 'PLCE' && formData.maxScore > 100) {
      warnings.push('PLCE exams typically use a 0-100 scoring system');
    }

    return { isValid, warnings };
  };

  /**
   * Get suggested grade boundaries based on common patterns
   */
  const getSuggestedGradeBoundaries = (examLevel: string, totalMarks: number = 100) => {
    const suggestions: Array<{ grade: string; minScore: number; maxScore: number }> = [];

    // No predefined suggestions - users must set their own boundaries
    // This function is kept for potential future use but returns empty array

    return suggestions;
  };

  return {
    // Base validation methods
    ...validation,
    
    // Grade boundary specific methods
    validateGradeBoundaryForm,
    validateGradeBoundaryField,
    validateGradeBoundaryLogic,
    getSuggestedGradeBoundaries,
    
    // Computed properties
    validationWarnings: getValidationWarnings
  };
}

import { createRouter, createWebHistory } from 'vue-router'
// @ts-ignore
import { useAuthStore } from '@/stores/auth'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      redirect: '/auth/login',
    },
    {
      path: '/auth',
      children: [
        {
          path: 'login',
          name: 'auth.login',
          component: () => import('@/views/auth/login/LoginPage.vue'),
          meta: {
            title: 'Login',
            requiresGuest: false, // Temporarily allow authenticated users for debugging
          },
        },
        {
          path: 'register',
          name: 'auth.register',
          component: () => import('@/views/auth/register/RegisterPage.vue'),
          meta: {
            title: 'Register',
          },
        },
        {
          path: 'forgot-password',
          name: 'auth.forgot-password',
          component: () => import('@/views/auth/forgot_password/ForgotPasswordPage.vue'),
          meta: {
            title: 'Forgot Password',
          },
        },
      ],
    },
    {
      path: "/admin",
      meta: {
        layout: 'sigelegeLayoutPage',
        requiresAuth: true,
        requiresAdmin: true, // Require admin permissions (email-based)
      },
      children: [
        {
          path: 'dashboard',
          name: 'admin.dashboard',
          component: () => import('@/views/admin/dashboard/AdminDashboardPage.vue'),
          meta: {
            title: 'Administrator Dashboard'
          }
        },
        {
          path: 'user-management',
          meta: {
            title: 'User Management',
            requiresAuth: true
          },
          children: [
            // Default redirect to users
            {
              path: '',
              redirect: 'users'
            },
            // Users submodule
            {
              path: 'users',
              name: 'admin.user-management.users',
              component: () => import('@/views/admin/user_management/users/UsersListPage.vue'),
              meta: {
                title: 'Users',
                requiresAuth: true
              }
            },
            {
              path: 'users/create',
              name: 'admin.user-management.users.create',
              component: () => import('@/views/admin/user_management/users/CreateUserPage.vue'),
              meta: {
                title: 'Create User',
                requiresAuth: true
              }
            },
            {
              path: 'users/:id',
              name: 'admin.user-management.users.detail',
              component: () => import('@/views/admin/user_management/users/detail-page.vue'),
              meta: {
                title: 'User Details',
                requiresAuth: true
              }
            },
            {
              path: 'users/:id/edit',
              name: 'admin.user-management.users.edit',
              component: () => import('@/views/admin/user_management/users/edit-page.vue'),
              meta: {
                title: 'Edit User',
                requiresAuth: true
              }
            },
            {
              path: 'users/:id/status',
              name: 'admin.user-management.users.status',
              component: () => import('@/views/admin/user_management/users/status-page.vue'),
              meta: {
                title: 'Manage User Status',
                requiresAuth: true
              }
            },
            {
              path: 'users/:id/roles',
              name: 'admin.user-management.users.roles',
              component: () => import('@/views/admin/user_management/users/roles-page.vue'),
              meta: {
                title: 'Assign User Roles',
                requiresAuth: true
              }
            },
            // Roles submodule
            {
              path: 'roles',
              name: 'admin.user-management.roles',
              component: () => import('@/views/admin/user_management/roles/RolesListPage.vue'),
              meta: {
                title: 'Roles',
                requiresAuth: true
              }
            },
            {
              path: 'roles/create',
              name: 'admin.user-management.roles.create',
              component: () => import('@/views/admin/user_management/roles/create-page.vue'),
              meta: {
                title: 'Create Role',
                requiresAuth: true
              }
            },
            {
              path: 'roles/:id',
              name: 'admin.user-management.roles.detail',
              component: () => import('@/views/admin/user_management/roles/detail-page.vue'),
              meta: {
                title: 'Role Details',
                requiresAuth: true
              }
            },
            {
              path: 'roles/:id/edit',
              name: 'admin.user-management.roles.edit',
              component: () => import('@/views/admin/user_management/roles/edit-page.vue'),
              meta: {
                title: 'Edit Role',
                requiresAuth: true
              }
            },
            {
              path: 'roles/:id/status',
              name: 'admin.user-management.roles.status',
              component: () => import('@/views/admin/user_management/roles/status-page.vue'),
              meta: {
                title: 'Manage Role Status',
                requiresAuth: true
              }
            },
            {
              path: 'roles/permissions',
              name: 'admin.user-management.roles.permissions',
              component: () => import('@/views/admin/user_management/roles/permissions-page.vue'),
              meta: {
                title: 'Role Permissions',
                requiresAuth: true
              }
            },
            // Permissions submodule
            {
              path: 'permissions',
              name: 'admin.user-management.permissions',
              component: () => import('@/views/admin/user_management/permissions/index-page.vue'),
              meta: {
                title: 'Permissions',
                requiresAuth: true
              }
            },
            {
              path: 'permissions/create',
              name: 'admin.user-management.permissions.create',
              component: () => import('@/views/admin/user_management/permissions/create-page.vue'),
              meta: {
                title: 'Create Permission',
                requiresAuth: true
              }
            },
            {
              path: 'permissions/:id',
              name: 'admin.user-management.permissions.detail',
              component: () => import('@/views/admin/user_management/permissions/detail-page.vue'),
              meta: {
                title: 'Permission Details',
                requiresAuth: true
              }
            },
            {
              path: 'permissions/:id/edit',
              name: 'admin.user-management.permissions.edit',
              component: () => import('@/views/admin/user_management/permissions/edit-page.vue'),
              meta: {
                title: 'Edit Permission',
                requiresAuth: true
              }
            },
            {
              path: 'permissions/:id/status',
              name: 'admin.user-management.permissions.status',
              component: () => import('@/views/admin/user_management/permissions/status-page.vue'),
              meta: {
                title: 'Manage Permission Status',
                requiresAuth: true
              }
            },
            // Sections submodule
            {
              path: 'sections',
              name: 'admin.user-management.sections',
              component: () => import('@/views/admin/user_management/sections/index-page.vue'),
              meta: {
                title: 'Sections',
                requiresAuth: true
              }
            },
            {
              path: 'sections/create',
              name: 'admin.user-management.sections.create',
              component: () => import('@/views/admin/user_management/sections/create-page.vue'),
              meta: {
                title: 'Create Section',
                requiresAuth: true
              }
            },
            {
              path: 'sections/:id',
              name: 'admin.user-management.sections.detail',
              component: () => import('@/views/admin/user_management/sections/detail-page.vue'),
              meta: {
                title: 'Section Details',
                requiresAuth: true
              }
            },
            {
              path: 'sections/:id/edit',
              name: 'admin.user-management.sections.edit',
              component: () => import('@/views/admin/user_management/sections/edit-page.vue'),
              meta: {
                title: 'Edit Section',
                requiresAuth: true
              }
            },
            {
              path: 'sections/:id/status',
              name: 'admin.user-management.sections.status',
              component: () => import('@/views/admin/user_management/sections/status-page.vue'),
              meta: {
                title: 'Manage Section Status',
                requiresAuth: true
              }
            }
          ]
        },
        {
          path: 'score-entry',
          name: 'admin.score-entry',
          component: () => import('@/views/admin/score_entry/ScoreEntryPage.vue'),
          meta: {
            title: 'Score Entry'
          }
        },
        {
          path: 'score-entry/:examType/:year',
          name: 'admin.score-entry.submenu',
          component: () => import('@/views/admin/score_entry/ScoreEntryPage.vue'),
          meta: {
            title: 'Score Entry'
          }
        },
        {
          path: 'grading-system',
          name: 'admin.grading-system',
          component: () => import('@/views/admin/grading_system/GradingSystemPage.vue'),
          meta: {
            title: 'Grading System'
          }
        },
        {
          path: 'grading-system/grade-boundaries/create',
          name: 'admin.grading-system.grade-boundaries.create',
          component: () => import('@/views/admin/grading_system/grade-boundaries/create-page.vue'),
          meta: {
            title: 'Create Grade Boundary'
          }
        },
        {
          path: 'grading-system/grade-boundaries/:id/edit',
          name: 'admin.grading-system.grade-boundaries.edit',
          component: () => import('@/views/admin/grading_system/grade-boundaries/edit-page.vue'),
          meta: {
            title: 'Edit Grade Boundary'
          }
        },
        {
          path: 'grading-system/grade-boundaries/comprehensive/create',
          name: 'admin.grading-system.grade-boundaries.comprehensive.create',
          component: () => import('@/views/admin/grading_system/grade-boundaries/comprehensive-create-page.vue'),
          meta: {
            title: 'Setup Grade Boundaries'
          }
        },
        {
          path: 'grading-system/papers/create',
          name: 'admin.grading-system.papers.create',
          component: () => import('@/views/admin/grading_system/papers/create-page.vue'),
          meta: {
            title: 'Create Paper'
          }
        },
        {
          path: 'grading-system/papers/:id/edit',
          name: 'admin.grading-system.papers.edit',
          component: () => import('@/views/admin/grading_system/papers/edit-page.vue'),
          meta: {
            title: 'Edit Paper'
          }
        },
        {
          path: 'grading-system/subjects/create',
          name: 'admin.grading-system.subjects.create',
          component: () => import('@/views/admin/grading_system/subjects/create-page.vue'),
          meta: {
            title: 'Create Subject'
          }
        },
        {
          path: 'grading-system/subjects/:id/edit',
          name: 'admin.grading-system.subjects.edit',
          component: () => import('@/views/admin/grading_system/subjects/edit-page.vue'),
          meta: {
            title: 'Edit Subject'
          }
        },
        {
          path: 'results',
          name: 'admin.results',
          component: () => import('@/views/admin/results/ResultsManagementPage.vue'),
          meta: {
            title: 'Results'
          }
        },
        {
          path: 'results/student/:studentId/year/:year/subjects',
          name: 'admin.results.student-subjects',
          component: () => import('@/views/admin/results/student-subjects/index-page.vue'),
          meta: {
            title: 'Student Subject Details'
          }
        },
        // Debug routes (only for development/testing)
        {
          path: 'debug/navigation-test',
          name: 'admin.debug.navigation-test',
          component: () => import('@/views/admin/debug/navigation-test.vue'),
          meta: {
            title: 'Navigation Permissions Test',
            requiresAuth: true
          }
        }
      ]
    },
    {
      path: "/student",
      meta: {
        requiresAuth: true,
        requiresApproval: false, // Allow access for all authenticated users
      },
      children: [
        {
          path: 'dashboard',
          name: 'student.dashboard',
          component: () => import('@/views/student/dashboard/index-page.vue'),
          meta: {
            title: 'Student Dashboard'
          }
        },
        {
          path: 'results',
          name: 'student.results',
          component: () => import('@/views/student/results/index-page.vue'),
          meta: {
            title: 'My Results'
          }
        },
        {
          path: 'certificates',
          name: 'student.certificates',
          component: () => import('@/views/student/certificates/index-page.vue'),
          meta: {
            title: 'My Certificates'
          }
        },
        {
          path: 'profile',
          name: 'student.profile',
          component: () => import('@/views/student/profile/index-page.vue'),
          meta: {
            title: 'My Profile'
          }
        },
        {
          path: 'schedule',
          name: 'student.schedule',
          component: () => import('@/views/student/schedule/index-page.vue'),
          meta: {
            title: 'Exam Schedule'
          }
        }
      ]
    }
  ],
})

router.beforeEach(async (to, _from, next) => {
  const authStore = useAuthStore()

  if (!authStore.isAuthenticated && !authStore.isLoading) {
    authStore.initializeAuth()
  }

  // Check if route requires authentication
  if (to.meta.requiresAuth) {
    if (!authStore.isAuthenticated) {
      // Redirect to login if not authenticated
      return next({ name: 'auth.login', query: { redirect: to.fullPath } })
    }

    // Check if route requires admin permissions (any non-student user can access admin routes)
    if (to.meta.requiresAdmin && authStore.isStudent) {
      // Redirect students to student dashboard
      return next({ name: 'student.dashboard' })
    }

    // Check if route requires approval
    if (to.meta.requiresApproval && !authStore.isApproved) {
      // Redirect to a pending approval page or dashboard
      return next({ name: 'auth.login' }) // You can create a pending approval page
    }
  }

  // Check if route is for guests only (like login page)
  if (to.meta.requiresGuest && authStore.isAuthenticated) {
    // Redirect authenticated users to their appropriate dashboard
    const dashboardRoute = authStore.getDashboardRoute
    return next(dashboardRoute)
  }

  // Role-based access control
  if (to.meta.requiresAuth) {
    // Check if non-student trying to access student routes
    if (to.path.startsWith('/student') && !authStore.isStudent) {
      return next({ name: 'admin.dashboard' })
    }

    // Check if student trying to access admin routes
    if (to.path.startsWith('/admin') && authStore.isStudent) {
      return next({ name: 'student.dashboard' })
    }

    // Dynamic role-based access control for admin routes
    if (to.path.startsWith('/admin') && !authStore.isStudent) {
      // System Administrator has access to everything
      if (authStore.isSystemAdministrator) {
        // Allow access
      }
      // Data Clerk access control - only allow dashboard and score-entry
      else if (authStore.isDataClerk) {
        const allowedPaths = [
          '/admin/dashboard',
          '/admin/score-entry'
        ];

        const isAllowed = allowedPaths.some(path => to.path.startsWith(path));

        if (!isAllowed) {
          console.log('Data Clerk access denied to:', to.path);
          return next({ name: 'admin.dashboard' });
        }
      }
      // Dynamic role permission checking
      else if (authStore.userPermissions && authStore.userPermissions.length > 0) {
        // Map routes to section IDs for permission checking
        const routeToSectionMap: Record<string, string> = {
          '/admin/dashboard': 'dashboard',
          '/admin/score-entry': 'score_entry',
          '/admin/grading-system': 'grading',
          '/admin/results': 'results_management',
          '/admin/user-management': 'user_management',
          '/admin/reports': 'reports',
          '/admin/audit-logs': 'audit_logs',
          '/admin/system-settings': 'system_settings'
        };

        // Find the section for this route
        let hasAccess = false;
        for (const [routePrefix, sectionId] of Object.entries(routeToSectionMap)) {
          if (to.path.startsWith(routePrefix)) {
            hasAccess = authStore.hasModuleAccess ? authStore.hasModuleAccess(sectionId) : false;
            break;
          }
        }

        if (!hasAccess) {
          return next({ name: 'admin.dashboard' });
        }
      }
      else if (!authStore.hasAdminPrivileges) {
        return next({ name: 'admin.dashboard' });
      }
    }
  }

  next()
})

export default router

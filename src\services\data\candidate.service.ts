/**
 * @fileoverview Candidate Service for MANEB Results Management System
 * @description Service for fetching candidates (exam numbers) from the API
 */

import apiClient from '../core/api-client';

/**
 * Candidate interface matching the API response
 */
export interface CandidateDto {
  id: string;
  examNumber: string;
  firstName: string;
  lastName: string;
  middleName: string | null;
  gender: string;
  dateOfBirth: string;
  nationalId: string | null;
  centreId: string;
  examTypeId: string;
  candidateEntryTypeId: string;
  createdOn: string;
  createdBy: string;
  modifiedOn: string | null;
  modifiedBy: string | null;
  isActive: boolean;
  centre: any | null;
  examType: any | null;
  candidateEntryType: any | null;
}

/**
 * Score Candidate interface matching the ScoreCandidates API response structure
 */
export interface ScoreCandidateDto {
  id: string;
  firstname: string;
  surname: string;
  examinationNo: string;
  examYear: number;
  subjectName: string;
  manebcentreNo: string;
  candidateEntryTypeId: string;
  schoolName: string;
  schoolId: string;
  subjectId: string;
  sequenceNo: number;
  examTypeId: string;
}

/**
 * Candidate Summary interface matching the new API response structure
 */
export interface CandidateSummaryDto {
  districtName: string;
  districtId: string;
  school: string;
  schoolId: string;
  centreNo: string;
  cohortName: string;
  examYear: number;
  examTypeId: string;
  cohortId: string;
  examinationNumber: string;
  firstname: string;
  surname: string;
  id: string;
  dateOfBirth: string;
  gender: string;
}

/**
 * API Response interface for paginated candidates
 */
export interface CandidateResponse {
  totalCount: number;
  pageNumber: number;
  pageSize: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
  items: CandidateDto[];
}

/**
 * API Response interface for paginated candidate summary data
 */
export interface CandidateSummaryResponse {
  totalCount: number;
  pageNumber: number;
  pageSize: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
  items: CandidateSummaryDto[];
}

/**
 * Simplified candidate option for dropdowns
 */
export interface CandidateOption {
  id: string;
  examNumber: string;
  fullName: string;
  firstName: string;
  lastName: string;
  centreId: string;
  examTypeId: string;
  isActive: boolean;
}

/**
 * Candidate search filters (based on API documentation)
 */
export interface CandidateFilters {
  // Standard pagination and search parameters
  pageNumber?: number;
  pageSize?: number;
  search?: string;
  sortBy?: string;
  sortDescending?: boolean;

  // Candidate-specific filter parameters
  schoolId?: string;
  centerNo?: string;
  examYear?: number;
  subjectId?: string;

  // Legacy properties (for backward compatibility)
  examTypeId?: string;
  centreId?: string;
  activeOnly?: boolean;
}

/**
 * Candidate Service
 * @description Handles all candidate related API operations
 */
export class CandidateService {
  private static instance: CandidateService;
  private cachedCandidates: Map<string, CandidateDto[]> = new Map();
  private lastFetchTimes: Map<string, number> = new Map();
  private readonly CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

  private constructor() {}

  /**
   * Get singleton instance
   */
  static getInstance(): CandidateService {
    if (!CandidateService.instance) {
      CandidateService.instance = new CandidateService();
    }
    return CandidateService.instance;
  }

  /**
   * Generate cache key for filters
   */
  private getCacheKey(filters: CandidateFilters): string {
    return JSON.stringify(filters);
  }

  /**
   * Fetch candidates from API with filters
   */
  async fetchCandidates(filters: CandidateFilters = {}, forceRefresh: boolean = false): Promise<CandidateResponse> {
    const cacheKey = this.getCacheKey(filters);
    const now = Date.now();
    const lastFetchTime = this.lastFetchTimes.get(cacheKey) || 0;
    
    // Return cached data if available and not expired
    if (!forceRefresh && this.cachedCandidates.has(cacheKey) && (now - lastFetchTime) < this.CACHE_DURATION) {
      const cachedItems = this.cachedCandidates.get(cacheKey)!;
      return {
        totalCount: cachedItems.length,
        pageNumber: filters.pageNumber || 1,
        pageSize: filters.pageSize || 10,
        totalPages: Math.ceil(cachedItems.length / (filters.pageSize || 10)),
        hasNextPage: false,
        hasPreviousPage: false,
        items: cachedItems
      };
    }

    try {
      // Build query parameters using standardized structure
      const params = new URLSearchParams();

      // Standard pagination parameters
      if (filters.pageNumber) params.append('PageNumber', filters.pageNumber.toString());
      if (filters.pageSize) params.append('PageSize', filters.pageSize.toString());
      if (filters.search) params.append('Search', filters.search);
      if (filters.sortBy) params.append('SortBy', filters.sortBy);
      if (filters.sortDescending !== undefined) params.append('SortDescending', filters.sortDescending.toString());

      // Candidate-specific filter parameters (based on API documentation)
      if (filters.schoolId) params.append('SchoolId', filters.schoolId);
      if (filters.centerNo) params.append('CenterNo', filters.centerNo);
      if (filters.examYear) params.append('ExamYear', filters.examYear.toString());
      if (filters.subjectId) params.append('SubjectID', filters.subjectId);

      const url = `/api/candidate?${params.toString()}`;
      const response: CandidateResponse = await apiClient.get(url);

      // Cache the results
      this.cachedCandidates.set(cacheKey, response.items);
      this.lastFetchTimes.set(cacheKey, now);

      return response;
    } catch (error) {
      console.error('Error fetching candidates:', error);
      
      // Return cached data if available, even if expired
      if (this.cachedCandidates.has(cacheKey)) {
        console.warn('Using cached candidates due to API error');
        const cachedItems = this.cachedCandidates.get(cacheKey)!;
        return {
          totalCount: cachedItems.length,
          pageNumber: filters.pageNumber || 1,
          pageSize: filters.pageSize || 10,
          totalPages: Math.ceil(cachedItems.length / (filters.pageSize || 10)),
          hasNextPage: false,
          hasPreviousPage: false,
          items: cachedItems
        };
      }
      
      throw error;
    }
  }

  /**
   * Fetch candidates from CandidateSummary endpoint (for score entry)
   */
  async fetchCandidateSummary(filters: {
    schoolId?: string;
    centerNo?: string;
    examYear?: number;
    subjectId?: string;
    pageNumber?: number;
    pageSize?: number;
    search?: string;
  }): Promise<CandidateSummaryResponse> {
    try {
      // Build query parameters for CandidateSummary endpoint
      const params = new URLSearchParams();

      // Standard pagination parameters
      if (filters.pageNumber) params.append('PageNumber', filters.pageNumber.toString());
      if (filters.pageSize) params.append('PageSize', filters.pageSize.toString());
      if (filters.search) params.append('Search', filters.search);

      // CandidateSummary specific filter parameters
      if (filters.schoolId) params.append('SchoolId', filters.schoolId);
      if (filters.centerNo) params.append('CenterNo', filters.centerNo);
      if (filters.examYear) params.append('ExamYear', filters.examYear.toString());
      if (filters.subjectId) params.append('SubjectID', filters.subjectId);

      console.log('🔍 Fetching candidates from CandidateSummary with filters:', filters);
      console.log('📋 API URL params:', params.toString());

      const url = `/api/CandidateSummary?${params.toString()}`;
      const response: CandidateSummaryResponse = await apiClient.get(url);

      console.log('📥 CandidateSummary API response:', response);

      return response;
    } catch (error) {
      console.error('Error fetching candidate summary:', error);
      throw error;
    }
  }

  /**
   * Fetch score candidates with specific filtering (uses /api/Candidate/ScoreCandidates endpoint)
   * Returns a direct array, not paginated
   */
  async fetchScoreCandidates(filters: {
    schoolId?: string;
    centerNo?: string;
    examYear?: number;
    subjectId?: string;
    examTypeId?: string;
    search?: string;
  }): Promise<ScoreCandidateDto[]> {
    try {
      // Build query parameters using standardized structure
      const params = new URLSearchParams();

      // Score candidate specific filter parameters
      if (filters.schoolId) params.append('SchoolId', filters.schoolId);
      if (filters.centerNo) params.append('CenterNo', filters.centerNo);
      if (filters.examYear) params.append('ExamYear', filters.examYear.toString());
      if (filters.subjectId) params.append('SubjectID', filters.subjectId);
      if (filters.examTypeId) params.append('ExamTypeID', filters.examTypeId);
      if (filters.search) params.append('Search', filters.search);

      const url = `/api/Candidate/ScoreCandidates?${params.toString()}`;
      console.log('🔍 Fetching score candidates with URL:', url);
      const response: ScoreCandidateDto[] = await apiClient.get(url);

      return response;
    } catch (error) {
      console.error('Error fetching score candidates:', error);
      throw error;
    }
  }

  /**
   * Search candidates by exam number or name
   */
  async searchCandidates(query: string, filters: Omit<CandidateFilters, 'search'> = {}): Promise<CandidateDto[]> {
    const response = await this.fetchCandidates({ ...filters, search: query, pageSize: 50 });
    return response.items;
  }

  /**
   * Get candidates as dropdown options
   */
  async getCandidateOptions(filters: CandidateFilters = {}): Promise<CandidateOption[]> {
    const response = await this.fetchCandidates({ ...filters, activeOnly: true });
    
    return response.items.map(candidate => ({
      id: candidate.id,
      examNumber: candidate.examNumber,
      fullName: `${candidate.firstName} ${candidate.lastName}`.trim(),
      firstName: candidate.firstName,
      lastName: candidate.lastName,
      centreId: candidate.centreId,
      examTypeId: candidate.examTypeId,
      isActive: candidate.isActive
    }));
  }

  /**
   * Get candidate by exam number
   */
  async getCandidateByExamNumber(examNumber: string): Promise<CandidateDto | undefined> {
    const response = await this.fetchCandidates({ search: examNumber, pageSize: 1 });
    return response.items.find(candidate => candidate.examNumber === examNumber);
  }

  /**
   * Get candidate by ID
   */
  async getCandidateById(id: string): Promise<CandidateDto | undefined> {
    // Try to find in cache first
    for (const cachedItems of this.cachedCandidates.values()) {
      const candidate = cachedItems.find(c => c.id === id);
      if (candidate) return candidate;
    }
    
    // If not found in cache, search by ID
    const response = await this.fetchCandidates({ search: id, pageSize: 1 });
    return response.items.find(candidate => candidate.id === id);
  }

  /**
   * Get candidates by centre
   */
  async getCandidatesByCentre(centreId: string): Promise<CandidateDto[]> {
    const response = await this.fetchCandidates({ centreId, activeOnly: true });
    return response.items;
  }

  /**
   * Get candidates by exam type
   */
  async getCandidatesByExamType(examTypeId: string): Promise<CandidateDto[]> {
    const response = await this.fetchCandidates({ examTypeId, activeOnly: true });
    return response.items;
  }

  /**
   * Clear cache (useful for testing or forced refresh)
   */
  clearCache(): void {
    this.cachedCandidates.clear();
    this.lastFetchTimes.clear();
  }

  /**
   * Clear specific cache entry
   */
  clearCacheForFilters(filters: CandidateFilters): void {
    const cacheKey = this.getCacheKey(filters);
    this.cachedCandidates.delete(cacheKey);
    this.lastFetchTimes.delete(cacheKey);
  }
}

// Export singleton instance
export const candidateService = CandidateService.getInstance();

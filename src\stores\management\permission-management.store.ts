/**
 * @fileoverview Permission Management Store for MANEB Results Management System
 * @description Pinia store for managing role permissions with full CRUD operations
 */

import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import { rolePermissionManagementService } from '@/services';
import type { 
  RolePermissionDto, 
  CreateRolePermissionRequest, 
  UpdateRolePermissionRequest,
  RolePermissionFilterDto,
  PermissionAction,
  RecordStatus
} from '@/interfaces';

export const usePermissionManagementStore = defineStore('permissionManagement', () => {
  // State
  const permissions = ref<RolePermissionDto[]>([]);
  const currentPermission = ref<RolePermissionDto | null>(null);
  const isLoading = ref(false);
  const error = ref<string | null>(null);
  const searchQuery = ref('');
  const filters = ref<RolePermissionFilterDto>({
    searchQuery: '',
    roleId: 'All',
    sectionId: 'All',
    action: 'All',
    canAccess: 'All',
    status: 'All'
  });
  const pagination = ref({
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 0
  });

  // Getters
  const filteredPermissions = computed(() => {
    let result = permissions.value;

    if (searchQuery.value) {
      const query = searchQuery.value.toLowerCase();
      result = result.filter(permission =>
        permission.section?.name?.toLowerCase().includes(query) ||
        permission.action?.toLowerCase().includes(query)
      );
    }

    if (filters.value.roleId && filters.value.roleId !== 'All') {
      result = result.filter(permission => permission.roleId === filters.value.roleId);
    }

    if (filters.value.sectionId && filters.value.sectionId !== 'All') {
      result = result.filter(permission => permission.sectionID === filters.value.sectionId);
    }

    if (filters.value.action && filters.value.action !== 'All') {
      result = result.filter(permission => permission.action === filters.value.action);
    }

    if (filters.value.canAccess !== undefined && filters.value.canAccess !== 'All') {
      result = result.filter(permission => permission.canAccess === filters.value.canAccess);
    }

    if (filters.value.status && filters.value.status !== 'All') {
      result = result.filter(permission => permission.status === filters.value.status);
    }

    return result;
  });

  const permissionsByRole = computed(() => {
    return (roleId: string) => permissions.value.filter(permission => permission.roleId === roleId);
  });

  const permissionsBySection = computed(() => {
    return (sectionId: string) => permissions.value.filter(permission => permission.sectionID === sectionId);
  });

  const permissionsByStatus = computed(() => {
    return (status: RecordStatus) => permissions.value.filter(permission => permission.status === status);
  });

  const totalPermissions = computed(() => permissions.value.length);

  const activePermissions = computed(() => 
    permissions.value.filter(permission => permission.status === 'Approved' || permission.status === 'SecondApproved')
  );

  // Actions
  const fetchPermissions = async (): Promise<void> => {
    try {
      isLoading.value = true;
      error.value = null;
      permissions.value = await rolePermissionManagementService.getAllRolePermissions();
    } catch (err: any) {
      error.value = err.message || 'Failed to fetch permissions';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const fetchPermissionsPaginated = async (page: number = 1, limit: number = 10): Promise<void> => {
    try {
      isLoading.value = true;
      error.value = null;
      
      const response = await rolePermissionManagementService.getRolePermissionsPaginated(page, limit, {
        Search: searchQuery.value || undefined,
        status: filters.value.status !== 'All' ? filters.value.status as RecordStatus : undefined
      });
      
      permissions.value = response;
      pagination.value = {
        page,
        limit,
        total: response.length,
        totalPages: Math.ceil(response.length / limit)
      };
    } catch (err: any) {
      error.value = err.message || 'Failed to fetch permissions';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const fetchPermissionById = async (id: string): Promise<RolePermissionDto> => {
    try {
      isLoading.value = true;
      error.value = null;
      const permission = await rolePermissionManagementService.getRolePermissionById(id);
      currentPermission.value = permission;
      return permission;
    } catch (err: any) {
      error.value = err.message || 'Failed to fetch permission';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const createPermission = async (permissionData: CreateRolePermissionRequest): Promise<RolePermissionDto> => {
    try {
      isLoading.value = true;
      error.value = null;
      const newPermission = await rolePermissionManagementService.createRolePermission(permissionData);
      permissions.value.push(newPermission);
      return newPermission;
    } catch (err: any) {
      error.value = err.message || 'Failed to create permission';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const updatePermission = async (id: string, permissionData: UpdateRolePermissionRequest): Promise<void> => {
    try {
      isLoading.value = true;
      error.value = null;
      await rolePermissionManagementService.updateRolePermission(id, permissionData);
      
      // Update local state
      const index = permissions.value.findIndex(permission => permission.id === id);
      if (index !== -1) {
        permissions.value[index] = { ...permissions.value[index], ...permissionData };
      }
      
      if (currentPermission.value?.id === id) {
        currentPermission.value = { ...currentPermission.value, ...permissionData };
      }
    } catch (err: any) {
      error.value = err.message || 'Failed to update permission';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const deletePermission = async (id: string): Promise<void> => {
    try {
      isLoading.value = true;
      error.value = null;
      await rolePermissionManagementService.deleteRolePermission(id);
      
      // Remove from local state
      permissions.value = permissions.value.filter(permission => permission.id !== id);
      if (currentPermission.value?.id === id) {
        currentPermission.value = null;
      }
    } catch (err: any) {
      error.value = err.message || 'Failed to delete permission';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const approvePermission = async (id: string): Promise<void> => {
    try {
      isLoading.value = true;
      error.value = null;
      await rolePermissionManagementService.approveRolePermission(id);
      
      // Update local state
      const index = permissions.value.findIndex(permission => permission.id === id);
      if (index !== -1) {
        permissions.value[index].status = 'Approved';
      }
    } catch (err: any) {
      error.value = err.message || 'Failed to approve permission';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const fetchRolePermissions = async (roleId: string): Promise<RolePermissionDto[]> => {
    try {
      isLoading.value = true;
      error.value = null;
      return await rolePermissionManagementService.getRolePermissionsByRoleId(roleId);
    } catch (err: any) {
      error.value = err.message || 'Failed to fetch role permissions';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const fetchSectionPermissions = async (sectionId: string): Promise<RolePermissionDto[]> => {
    try {
      isLoading.value = true;
      error.value = null;
      return await rolePermissionManagementService.getRolePermissionsBySectionId(sectionId);
    } catch (err: any) {
      error.value = err.message || 'Failed to fetch section permissions';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const bulkCreatePermissions = async (permissionsData: CreateRolePermissionRequest[]): Promise<RolePermissionDto[]> => {
    try {
      isLoading.value = true;
      error.value = null;
      const newPermissions = await rolePermissionManagementService.bulkCreateRolePermissions(permissionsData);
      permissions.value.push(...newPermissions);
      return newPermissions;
    } catch (err: any) {
      error.value = err.message || 'Failed to bulk create permissions';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const bulkDeleteRolePermissions = async (roleId: string): Promise<void> => {
    try {
      isLoading.value = true;
      error.value = null;
      await rolePermissionManagementService.bulkDeleteRolePermissionsByRoleId(roleId);
      
      // Remove from local state
      permissions.value = permissions.value.filter(permission => permission.roleId !== roleId);
    } catch (err: any) {
      error.value = err.message || 'Failed to bulk delete role permissions';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const replaceRolePermissions = async (roleId: string, newPermissions: CreateRolePermissionRequest[]): Promise<RolePermissionDto[]> => {
    try {
      isLoading.value = true;
      error.value = null;
      const updatedPermissions = await rolePermissionManagementService.replaceRolePermissions(roleId, newPermissions);
      
      // Update local state
      permissions.value = permissions.value.filter(permission => permission.roleId !== roleId);
      permissions.value.push(...updatedPermissions);
      
      return updatedPermissions;
    } catch (err: any) {
      error.value = err.message || 'Failed to replace role permissions';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const checkRolePermission = async (roleId: string, sectionId: string, action: PermissionAction): Promise<boolean> => {
    try {
      return await rolePermissionManagementService.checkRolePermission(roleId, sectionId, action);
    } catch (err: any) {
      error.value = err.message || 'Failed to check role permission';
      throw err;
    }
  };

  const searchPermissions = async (query: string): Promise<void> => {
    searchQuery.value = query;
    await fetchPermissionsPaginated(1, pagination.value.limit);
  };

  const filterPermissions = async (newFilters: Partial<RolePermissionFilterDto>): Promise<void> => {
    filters.value = { ...filters.value, ...newFilters };
    await fetchPermissionsPaginated(1, pagination.value.limit);
  };

  const clearError = (): void => {
    error.value = null;
  };

  const clearCurrentPermission = (): void => {
    currentPermission.value = null;
  };

  const resetFilters = (): void => {
    filters.value = {
      searchQuery: '',
      roleId: 'All',
      sectionId: 'All',
      action: 'All',
      canAccess: 'All',
      status: 'All'
    };
    searchQuery.value = '';
  };

  return {
    // State
    permissions,
    currentPermission,
    isLoading,
    error,
    searchQuery,
    filters,
    pagination,
    
    // Getters
    filteredPermissions,
    permissionsByRole,
    permissionsBySection,
    permissionsByStatus,
    totalPermissions,
    activePermissions,
    
    // Actions
    fetchPermissions,
    fetchPermissionsPaginated,
    fetchPermissionById,
    createPermission,
    updatePermission,
    deletePermission,
    approvePermission,
    fetchRolePermissions,
    fetchSectionPermissions,
    bulkCreatePermissions,
    bulkDeleteRolePermissions,
    replaceRolePermissions,
    checkRolePermission,
    searchPermissions,
    filterPermissions,
    clearError,
    clearCurrentPermission,
    resetFilters,
  };
});

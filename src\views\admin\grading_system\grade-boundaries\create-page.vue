<template>
  <div class="mx-auto max-w-4xl px-4 sm:px-6 lg:px-8 py-6">
    <!-- Enhanced <PERSON><PERSON> with MANEB Branding -->
    <div class="mb-8">
      <nav class="flex mb-5" aria-label="Breadcrumb">
        <ol class="inline-flex items-center space-x-1 text-sm font-medium md:space-x-2">
          <li class="inline-flex items-center">
            <router-link
              to="/admin/dashboard"
              class="inline-flex items-center text-gray-700 hover:text-maneb-primary transition-colors duration-200"
            >
              <svg class="w-5 h-5 mr-2.5" fill="currentColor" viewBox="0 0 20 20">
                <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"/>
              </svg>
              Admin Dashboard
            </router-link>
          </li>
          <li>
            <div class="flex items-center">
              <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
              </svg>
              <router-link
                to="/admin/grading-system"
                class="ml-1 text-gray-700 hover:text-maneb-primary md:ml-2 transition-colors duration-200"
              >
                Grading System
              </router-link>
            </div>
          </li>
          <li>
            <div class="flex items-center">
              <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
              </svg>
              <span class="ml-1 text-gray-400 md:ml-2" aria-current="page">Create Grade Boundary</span>
            </div>
          </li>
        </ol>
      </nav>

      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-3xl font-bold text-gray-900">Create Grade Boundary</h1>
          <p class="mt-2 text-sm text-gray-600">Set up grade boundaries for exam subjects</p>
        </div>

        <!-- MANEB Logo/Branding -->
        <div class="hidden md:flex items-center space-x-3">
          <div class="flex items-center space-x-2 px-3 py-2 bg-red-50 rounded-lg border border-red-100">
            <div class="w-8 h-8 bg-maneb-primary rounded-full flex items-center justify-center">
              <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
              </svg>
            </div>
            <span class="text-sm font-medium text-maneb-primary">MANEB EMS</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Enhanced Create Grade Boundary Form -->
    <div class="bg-white shadow-sm rounded-lg border border-gray-200">
      <div class="px-6 py-5 border-b border-gray-200">
        <div class="flex items-center space-x-3">
          <div class="w-10 h-10 bg-red-50 rounded-lg flex items-center justify-center">
            <svg class="w-6 h-6 text-maneb-primary" fill="currentColor" viewBox="0 0 20 20">
              <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
            </svg>
          </div>
          <div>
            <h3 class="text-lg font-semibold text-gray-900">Grade Boundary Information</h3>
            <p class="mt-1 text-sm text-gray-600">Please fill in all required fields to create a new grade boundary.</p>
          </div>
        </div>
      </div>

      <form @submit.prevent="handleSubmit" class="px-6 py-6 space-y-8">
        <!-- Basic Information Section -->
        <div class="space-y-6">
          <div class="flex items-start justify-between">
            <div class="border-l-4 border-maneb-primary pl-4">
              <h4 class="text-lg font-semibold text-gray-900">Basic Information</h4>
              <p class="text-sm text-gray-600 mt-1">Configure the exam type, subject, and year</p>
            </div>

            <!-- Grading System Information -->
            <div class="hidden md:block">
              <div v-if="currentGradingSystem" class="flex items-center space-x-2 px-4 py-3 bg-blue-50 rounded-lg border border-blue-200">
                <div class="w-6 h-6 bg-blue-600 rounded-full flex items-center justify-center">
                  <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"/>
                  </svg>
                </div>
                <div class="text-sm">
                  <div class="font-semibold text-blue-800">{{ currentGradingSystem.name }}</div>
                  <div class="text-blue-600">{{ currentGradingSystem.description }}</div>
                </div>
              </div>

              <div v-else class="flex items-center space-x-2 px-4 py-3 bg-gray-50 rounded-lg border border-gray-200">
                <div class="w-6 h-6 bg-gray-400 rounded-full flex items-center justify-center">
                  <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd"/>
                  </svg>
                </div>
                <div class="text-sm">
                  <div class="font-medium text-gray-600">Select Exam Type</div>
                  <div class="text-gray-500">to see grading system</div>
                </div>
              </div>
            </div>
          </div>

          <!-- Mobile Grading System Information -->
          <div v-if="currentGradingSystem" class="md:hidden p-3 bg-blue-50 rounded-lg border border-blue-200">
            <div class="flex items-center space-x-2">
              <div class="w-5 h-5 bg-blue-600 rounded-full flex items-center justify-center">
                <svg class="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"/>
                </svg>
              </div>
              <div class="text-sm">
                <div class="font-semibold text-blue-800">{{ currentGradingSystem.name }}</div>
                <div class="text-blue-600">{{ currentGradingSystem.description }}</div>
              </div>
            </div>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Exam Type -->
            <div class="space-y-2">
              <label for="examType" class="block text-sm font-medium text-gray-700">
                Exam Type <span class="text-red-500">*</span>
              </label>
              <select
                id="examType"
                v-model="formData.examType"
                required
                class="block w-full rounded-lg border border-gray-300 bg-white px-3 py-2.5 text-sm text-gray-900 focus:ring-2 focus:ring-maneb-primary focus:border-maneb-primary transition-colors duration-200"
              >
                <option value="">Select exam type</option>
                <option v-for="examType in examTypes" :key="examType.value" :value="examType.value">
                  {{ examType.label }} - {{ examType.description }}
                </option>
              </select>
              <p v-if="errors.examType" class="text-sm text-red-600">{{ errors.examType }}</p>
            </div>

            <!-- Subject -->
            <div class="space-y-2">
              <label for="subjectCode" class="block text-sm font-medium text-gray-700">
                Subject <span class="text-red-500">*</span>
              </label>
              <select
                id="subjectCode"
                v-model="formData.subjectCode"
                required
                class="block w-full rounded-lg border border-gray-300 bg-white px-3 py-2.5 text-sm text-gray-900 focus:ring-2 focus:ring-maneb-primary focus:border-maneb-primary transition-colors duration-200"
              >
                <option value="">Select subject</option>
                <option v-for="subject in subjects" :key="subject.id" :value="subject.id">
                  {{ subject.name }} ({{ subject.shortName || subject.id }})
                </option>
              </select>
              <p v-if="errors.subjectCode" class="text-sm text-red-600">{{ errors.subjectCode }}</p>
            </div>

            <!-- Year -->
            <div class="space-y-2">
              <label for="year" class="block text-sm font-medium text-gray-700">
                Year <span class="text-red-500">*</span>
              </label>
              <input
                id="year"
                v-model.number="formData.year"
                type="number"
                required
                min="2020"
                max="2030"
                :class="[
                  'block w-full rounded-lg border px-3 py-2.5 text-sm transition-colors duration-200',
                  'focus:ring-2 focus:ring-maneb-primary focus:border-maneb-primary',
                  errors.year
                    ? 'border-red-300 bg-red-50 text-red-900 focus:border-red-500 focus:ring-red-500'
                    : 'border-gray-300 bg-white text-gray-900'
                ]"
                placeholder="Enter year"
              />
              <p v-if="errors.year" class="text-sm text-red-600">{{ errors.year }}</p>
            </div>

            <!-- Grade Level -->
            <div class="space-y-2">
              <label for="gradeLevel" class="block text-sm font-medium text-gray-700">
                Grade Level <span class="text-red-500">*</span>
              </label>
              <select
                id="gradeLevel"
                v-model="formData.gradeLevel"
                required
                :disabled="!formData.examType"
                class="block w-full rounded-lg border border-gray-300 bg-white px-3 py-2.5 text-sm text-gray-900 focus:ring-2 focus:ring-maneb-primary focus:border-maneb-primary transition-colors duration-200 disabled:bg-gray-100 disabled:cursor-not-allowed"
              >
                <option value="">
                  {{ formData.examType ? 'Select grade level' : 'Please select exam type first' }}
                </option>
                <option v-for="grade in availableGradeLevels" :key="grade.value" :value="grade.value">
                  {{ grade.label }}{{ grade.description ? ' - ' + grade.description : '' }}
                </option>
              </select>
              <p v-if="errors.gradeLevel" class="text-sm text-red-600">{{ errors.gradeLevel }}</p>

              <!-- Award field with auto-suggestion -->
              <div v-if="formData.gradeLevel" class="mt-4">
                <label for="award" class="block text-sm font-medium text-gray-700 mb-2">
                  Award
                </label>
                <div class="relative">
                  <input
                    type="text"
                    id="award"
                    v-model="formData.award"
                    :placeholder="getAwardForGrade(formData.gradeLevel)"
                    class="block w-full rounded-lg border border-gray-300 bg-white px-3 py-2.5 text-sm text-gray-900 focus:ring-2 focus:ring-maneb-primary focus:border-maneb-primary transition-colors duration-200"
                  />
                  <button
                    type="button"
                    @click="formData.award = getAwardForGrade(formData.gradeLevel)"
                    class="absolute inset-y-0 right-0 flex items-center pr-3 text-sm text-gray-500 hover:text-maneb-primary"
                    title="Use auto-generated award"
                  >
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                    </svg>
                  </button>
                </div>
                <div class="mt-1 flex items-center justify-between">
                  <span class="text-xs text-gray-500">
                    Auto-suggested: {{ getAwardForGrade(formData.gradeLevel) }}
                  </span>
                  <span :class="[
                    'text-xs px-2 py-1 rounded-full font-medium',
                    (formData.award || getAwardForGrade(formData.gradeLevel)) === 'Distinction' ? 'bg-green-100 text-green-700' :
                    (formData.award || getAwardForGrade(formData.gradeLevel)) === 'Credit' ? 'bg-blue-100 text-blue-700' :
                    (formData.award || getAwardForGrade(formData.gradeLevel)) === 'Pass' ? 'bg-yellow-100 text-yellow-700' :
                    'bg-red-100 text-red-700'
                  ]">
                    {{ formData.award || getAwardForGrade(formData.gradeLevel) }}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Score Range Section -->
        <div class="space-y-6">
          <div class="border-l-4 border-blue-500 pl-4">
            <h4 class="text-lg font-semibold text-gray-900">Score Range</h4>
            <p class="text-sm text-gray-600 mt-1">Define the minimum and maximum scores for this grade</p>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Minimum Score -->
            <div class="space-y-2">
              <label for="minScore" class="block text-sm font-medium text-gray-700">
                Minimum Score <span class="text-red-500">*</span>
              </label>
              <input
                id="minScore"
                v-model.number="formData.minScore"
                type="number"
                required
                min="0"
                max="100"
                :class="[
                  'block w-full rounded-lg border px-3 py-2.5 text-sm transition-colors duration-200',
                  'focus:ring-2 focus:ring-maneb-primary focus:border-maneb-primary',
                  errors.minScore
                    ? 'border-red-300 bg-red-50 text-red-900 focus:border-red-500 focus:ring-red-500'
                    : 'border-gray-300 bg-white text-gray-900'
                ]"
                placeholder="Enter minimum score"
              />
              <p v-if="errors.minScore" class="text-sm text-red-600">{{ errors.minScore }}</p>
            </div>

            <!-- Maximum Score -->
            <div class="space-y-2">
              <label for="maxScore" class="block text-sm font-medium text-gray-700">
                Maximum Score <span class="text-red-500">*</span>
              </label>
              <input
                id="maxScore"
                v-model.number="formData.maxScore"
                type="number"
                required
                min="0"
                max="100"
                :class="[
                  'block w-full rounded-lg border px-3 py-2.5 text-sm transition-colors duration-200',
                  'focus:ring-2 focus:ring-maneb-primary focus:border-maneb-primary',
                  errors.maxScore
                    ? 'border-red-300 bg-red-50 text-red-900 focus:border-red-500 focus:ring-red-500'
                    : 'border-gray-300 bg-white text-gray-900'
                ]"
                placeholder="Enter maximum score"
              />
              <p v-if="errors.maxScore" class="text-sm text-red-600">{{ errors.maxScore }}</p>
            </div>
          </div>
        </div>

        <!-- Additional Information Section -->
        <div class="space-y-6">
          <div class="border-l-4 border-green-500 pl-4">
            <h4 class="text-lg font-semibold text-gray-900">Additional Information</h4>
            <p class="text-sm text-gray-600 mt-1">Optional description and status settings</p>
          </div>

          <div class="space-y-6">
            <!-- Description -->
            <div class="space-y-2">
              <label for="description" class="block text-sm font-medium text-gray-700">Description</label>
              <textarea
                id="description"
                v-model="formData.description"
                rows="3"
                class="block w-full rounded-lg border border-gray-300 bg-white px-3 py-2.5 text-sm text-gray-900 placeholder-gray-500 focus:border-maneb-primary focus:ring-2 focus:ring-maneb-primary"
                placeholder="Enter description (optional)"
              />
            </div>

            <!-- Active Status -->
            <div class="flex items-center">
              <input
                id="isActive"
                v-model="formData.isActive"
                type="checkbox"
                class="h-4 w-4 text-maneb-primary focus:ring-maneb-primary border-gray-300 rounded"
              />
              <label for="isActive" class="ml-2 block text-sm text-gray-900">
                Active (grade boundary is currently in use)
              </label>
            </div>
          </div>
        </div>

        <!-- Enhanced Error Display -->
        <div v-if="submitError" class="bg-red-50 border border-red-200 rounded-lg p-4">
          <div class="flex">
            <div class="flex-shrink-0">
              <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
              </svg>
            </div>
            <div class="ml-3">
              <h3 class="text-sm font-medium text-red-800">Error creating grade boundary</h3>
              <div class="mt-2 text-sm text-red-700">{{ submitError }}</div>
            </div>
          </div>
        </div>

        <!-- Enhanced Form Actions -->
        <div class="flex flex-col sm:flex-row justify-end space-y-3 sm:space-y-0 sm:space-x-3 pt-6 border-t border-gray-200">
          <router-link
            :to="{ name: 'admin.grading-system' }"
            class="inline-flex items-center justify-center px-6 py-3 border border-gray-300 text-sm font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-maneb-primary transition-colors duration-200"
          >
            <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
            </svg>
            Cancel
          </router-link>
          <button
            type="submit"
            :disabled="isSubmitting"
            class="inline-flex items-center justify-center px-6 py-3 border border-transparent text-sm font-medium rounded-lg text-white bg-maneb-primary hover:bg-maneb-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-maneb-primary disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200 shadow-sm"
          >
            <svg v-if="isSubmitting" class="animate-spin -ml-1 mr-3 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            <svg v-else class="h-4 w-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
            </svg>
            {{ isSubmitting ? 'Creating Grade Boundary...' : 'Create Grade Boundary' }}
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useGradingStore, useGradingApiStore } from '@/stores'
import { useExamTypesAutoLoad } from '@/composables'
import { useSubjectsAutoLoad } from '@/composables'
import type { CreateGradeBoundaryRequest } from '@/interfaces/external/results-api.interface'
import type { GradeLevel } from '@/interfaces'
import { GRADE_LEVELS } from '@/interfaces/common/exam-types.interface'

// Exam-specific grade scales
interface ExamGradeLevel {
  value: string
  label: string
  description: string
  typicalRange: string
  isPass: boolean
  order: number
}

interface ExamGradingSystem {
  examType: string
  name: string
  description: string
  grades: ExamGradeLevel[]
}

// Define exam-specific grading systems
const EXAM_GRADING_SYSTEMS: ExamGradingSystem[] = [
  {
    examType: 'MSCE',
    name: 'MSCE Numeric Grading',
    description: 'Malawi School Certificate of Education - Numeric grades 1-9',
    grades: [
      { value: '1', label: 'Grade 1', description: '', typicalRange: '', isPass: true, order: 1 },
      { value: '2', label: 'Grade 2', description: '', typicalRange: '', isPass: true, order: 2 },
      { value: '3', label: 'Grade 3', description: '', typicalRange: '', isPass: true, order: 3 },
      { value: '4', label: 'Grade 4', description: '', typicalRange: '', isPass: true, order: 4 },
      { value: '5', label: 'Grade 5', description: '', typicalRange: '', isPass: true, order: 5 },
      { value: '6', label: 'Grade 6', description: '', typicalRange: '', isPass: true, order: 6 },
      { value: '7', label: 'Grade 7', description: '', typicalRange: '', isPass: false, order: 7 },
      { value: '8', label: 'Grade 8', description: '', typicalRange: '', isPass: false, order: 8 },
      { value: '9', label: 'Grade 9', description: '', typicalRange: '', isPass: false, order: 9 }
    ]
  },
  {
    examType: 'JCE',
    name: 'JCE Letter Grading',
    description: 'Junior Certificate of Education - Letter grades A-F',
    grades: [
      { value: 'A', label: 'Grade A', description: 'Distinction', typicalRange: '', isPass: true, order: 1 },
      { value: 'B', label: 'Grade B', description: 'Credit', typicalRange: '', isPass: true, order: 2 },
      { value: 'C', label: 'Grade C', description: 'Pass', typicalRange: '', isPass: true, order: 3 },
      { value: 'D', label: 'Grade D', description: 'Pass', typicalRange: '', isPass: true, order: 4 },
      { value: 'F', label: 'Grade F', description: 'Fail', typicalRange: '', isPass: false, order: 5 }
    ]
  },
  {
    examType: 'PLCE',
    name: 'PLCE Letter Grading',
    description: 'Primary Leaving Certificate Examination - Letter grades A-F',
    grades: [
      { value: 'A', label: 'Grade A', description: 'Distinction', typicalRange: '', isPass: true, order: 1 },
      { value: 'B', label: 'Grade B', description: 'Credit', typicalRange: '', isPass: true, order: 2 },
      { value: 'C', label: 'Grade C', description: 'Pass', typicalRange: '', isPass: true, order: 3 },
      { value: 'D', label: 'Grade D', description: 'Pass', typicalRange: '', isPass: true, order: 4 },
      { value: 'F', label: 'Grade F', description: 'Fail', typicalRange: '', isPass: false, order: 5 }
    ]
  },
  {
    examType: 'TTC',
    name: 'TTC Letter Grading',
    description: 'Teacher Training College - Letter grades A-F',
    grades: [
      { value: 'A', label: 'Grade A', description: '', typicalRange: '', isPass: true, order: 1 },
      { value: 'B', label: 'Grade B', description: '', typicalRange: '', isPass: true, order: 2 },
      { value: 'C', label: 'Grade C', description: '', typicalRange: '', isPass: true, order: 3 },
      { value: 'D', label: 'Grade D', description: '', typicalRange: '', isPass: true, order: 4 },
      { value: 'F', label: 'Grade F', description: '', typicalRange: '', isPass: false, order: 5 }
    ]
  }
]
import sweetAlert from '@/utils/ui/sweetAlert'

// Router
const router = useRouter()

// Stores
const gradingStore = useGradingStore()
const gradingApiStore = useGradingApiStore()

// Load exam types from API using composable
const { examTypesLocal: examTypes } = useExamTypesAutoLoad()

// Load subjects from API using composable
const { activeSubjects: subjectsRaw } = useSubjectsAutoLoad()

// Deduplicated and filtered subjects computed property
const subjects = computed(() => {
  const subjectMap = new Map()
  const nameMap = new Map() // Track by name to catch name duplicates too

  subjectsRaw.value.forEach(subject => {
    // Skip if we already have this subject by ID
    if (subjectMap.has(subject.id)) {
      return
    }

    // Filter by exam type if selected
    if (formData.examType && subject.examTypeId && subject.examTypeId !== formData.examType) {
      return
    }

    // Skip if we already have a subject with the same name (case-insensitive)
    const normalizedName = subject.name?.toLowerCase().trim()
    if (normalizedName && nameMap.has(normalizedName)) {
      console.log(`🔄 Skipping duplicate subject by name: ${subject.name} (ID: ${subject.id})`)
      return
    }

    // Add to both maps
    subjectMap.set(subject.id, subject)
    if (normalizedName) {
      nameMap.set(normalizedName, subject.id)
    }
  })

  const uniqueSubjects = Array.from(subjectMap.values()).sort((a, b) => a.name.localeCompare(b.name))
  console.log(`📚 Loaded ${uniqueSubjects.length} unique subjects for grade boundaries (exam type: ${formData.examType || 'all'})`)

  return uniqueSubjects
})

// Dynamic grade levels based on selected exam type
const availableGradeLevels = computed(() => {
  if (!formData.examType) {
    return []
  }

  const gradingSystem = EXAM_GRADING_SYSTEMS.find(system => system.examType === formData.examType)
  if (!gradingSystem) {
    console.warn(`No grading system found for exam type: ${formData.examType}`)
    // Fallback to default letter grades
    return GRADE_LEVELS.map(grade => ({
      value: grade.level,
      label: `Grade ${grade.level}`,
      description: grade.name,
      typicalRange: grade.typicalRange || '',
      isPass: grade.isPass,
      order: grade.level.charCodeAt(0)
    }))
  }

  console.log(`📊 Using ${gradingSystem.name} for ${formData.examType}:`, gradingSystem.grades.length, 'grades')
  return gradingSystem.grades.sort((a, b) => a.order - b.order)
})

// Current grading system info
const currentGradingSystem = computed(() => {
  if (!formData.examType) return null
  return EXAM_GRADING_SYSTEMS.find(system => system.examType === formData.examType)
})

// Reactive state
const formData = reactive<{
  examType: string
  subjectCode: string
  year: number
  gradeLevel: GradeLevel | ''
  minScore: number | null
  maxScore: number | null
  description: string
  isActive: boolean
  award: string
}>({
  examType: '',
  subjectCode: '',
  year: new Date().getFullYear(),
  gradeLevel: '',
  minScore: null,
  maxScore: null,
  description: '',
  isActive: true,
  award: ''
})

const errors = reactive<Record<string, string>>({})
const submitError = ref<string | null>(null)
const isSubmitting = ref(false)

// Methods
const validateForm = (): boolean => {
  // Clear previous errors
  Object.keys(errors).forEach(key => delete errors[key])

  let isValid = true

  // Required field validation
  if (!formData.examType.trim()) {
    errors.examType = 'Exam type is required'
    isValid = false
  }

  // Validate exam type is from available options
  const validExamTypes = examTypes.value.map(et => et.value)
  if (formData.examType && !validExamTypes.includes(formData.examType)) {
    errors.examType = 'Please select a valid exam type'
    isValid = false
  }

  if (!formData.subjectCode.trim()) {
    errors.subjectCode = 'Subject is required'
    isValid = false
  }

  // Validate subject is from available options
  const validSubjects = subjects.value.map(s => s.id)
  if (formData.subjectCode && !validSubjects.includes(formData.subjectCode)) {
    errors.subjectCode = 'Please select a valid subject'
    isValid = false
  }

  if (!formData.year) {
    errors.year = 'Year is required'
    isValid = false
  } else if (formData.year < 2020 || formData.year > 2030) {
    errors.year = 'Year must be between 2020 and 2030'
    isValid = false
  }

  if (!formData.gradeLevel) {
    errors.gradeLevel = 'Grade level is required'
    isValid = false
  }

  // Validate grade level is valid for selected exam type
  if (formData.gradeLevel && formData.examType) {
    const validGrades = availableGradeLevels.value.map(g => g.value)
    if (!validGrades.includes(formData.gradeLevel)) {
      errors.gradeLevel = `Grade level "${formData.gradeLevel}" is not valid for ${formData.examType}`
      isValid = false
    }
  }

  if (formData.minScore === null || formData.minScore === undefined) {
    errors.minScore = 'Minimum score is required'
    isValid = false
  } else if (formData.minScore < 0 || formData.minScore > 100) {
    errors.minScore = 'Minimum score must be between 0 and 100'
    isValid = false
  }

  if (formData.maxScore === null || formData.maxScore === undefined) {
    errors.maxScore = 'Maximum score is required'
    isValid = false
  } else if (formData.maxScore < 0 || formData.maxScore > 100) {
    errors.maxScore = 'Maximum score must be between 0 and 100'
    isValid = false
  }

  // Validate score range
  if (formData.minScore !== null && formData.maxScore !== null && formData.minScore >= formData.maxScore) {
    errors.maxScore = 'Maximum score must be greater than minimum score'
    isValid = false
  }

  // Validate exam type + subject + grade level combination for uniqueness
  if (formData.examType && formData.subjectCode && formData.gradeLevel) {
    console.log('🔍 Validating unique combination:', {
      examType: formData.examType,
      subject: formData.subjectCode,
      grade: formData.gradeLevel,
      year: formData.year
    })

    // Note: In a real implementation, you would check against existing boundaries
    // For now, we just log the combination to ensure it's being tracked
  }

  return isValid
}

// Helper function to get award based on grade level
const getAwardForGrade = (grade: string): string => {
  const gradeAwards: Record<string, string> = {
    // Letter grades (PLCE, TTC, JCE)
    'A': 'Distinction',
    'B': 'Credit',
    'C': 'Pass',
    'D': 'Pass',
    'F': 'Fail',
    // Numeric grades (MSCE)
    '1': 'Distinction',
    '2': 'Credit',
    '3': 'Credit',
    '4': 'Pass',
    '5': 'Pass',
    '6': 'Pass',
    '7': 'Pass',
    '8': 'Fail',
    '9': 'Fail'
  }
  return gradeAwards[grade] || 'Pass'
}

const handleSubmit = async () => {
  if (!validateForm()) {
    return
  }

  try {
    isSubmitting.value = true
    submitError.value = null

    // Find the subject from the selected subject ID
    const subject = subjects.value.find(s => s.id === formData.subjectCode)
    if (!subject) {
      throw new Error('Selected subject not found')
    }



    // Helper function to get numeric value for grades
    const getGradeNumericValue = (grade: string): number => {
      const gradeValues: Record<string, number> = {
        'A': 1, '1': 1,
        'B': 2, '2': 2,
        'C': 3, '3': 3,
        'D': 4, '4': 4,
        'F': 9, '5': 5, '6': 6, '7': 7, '8': 8, '9': 9
      }
      return gradeValues[grade] || 9
    }

    // Check if this is MSCE (uses numeric grades) or other exam types (uses letter grades)
    const isMSCE = formData.examType === 'MSCE'
    const isNumericGrade = /^\d+$/.test(formData.gradeLevel as string) // Check if grade is numeric

    // Prepare data for submission with correct API field names
    const submitData: CreateGradeBoundaryRequest = {
      examLevel: formData.examType, // API expects 'examLevel' not 'examType'
      subjectCode: subject.id, // Use subject ID as subjectCode value
      grade: (isMSCE || isNumericGrade) ? null : formData.gradeLevel as string, // null for MSCE/numeric grades
      lowerBound: formData.minScore!, // API expects 'lowerBound' not 'minScore'
      upperBound: formData.maxScore!, // API expects 'upperBound' not 'maxScore'
      isActive: true,
      approvalStatus: 'Unapproved', // Default approval status
      examYear: new Date().getFullYear().toString(),
      cohort: new Date().getFullYear().toString(),
      award: formData.award || getAwardForGrade(formData.gradeLevel as string), // Use manual award or auto-generated
      gradeValue: (isMSCE || isNumericGrade) ? parseInt(formData.gradeLevel as string) : null // null for letter grades
    }

    // Log the submission data for debugging
    console.log('🚀 Creating grade boundary with data:', {
      formData: formData,
      selectedSubject: subject,
      apiPayload: submitData
    })

    // Verify exam type specificity
    console.log('📋 Grade boundary will be created for:', {
      examType: submitData.examLevel,
      subject: submitData.subjectCode,
      grade: submitData.grade,
      scoreRange: `${submitData.lowerBound}-${submitData.upperBound}`,
      award: submitData.award
    })

    // Submit to grading API
    await gradingApiStore.createGradeBoundary(submitData)

    // Show success message with exam type specificity
    await sweetAlert.success(
      'Grade boundary created successfully!',
      `Grade ${submitData.grade || submitData.gradeValue} boundary for ${subject.name} (${submitData.examLevel}) has been set to ${submitData.lowerBound}-${submitData.upperBound} points with award: ${submitData.award}.`
    )

    // Navigate back to grading system
    router.push({ name: 'admin.grading-system' })

  } catch (error: any) {
    submitError.value = error.message || 'Failed to create grade boundary'
    console.error('Error creating grade boundary:', error)
  } finally {
    isSubmitting.value = false
  }
}

// Watch for exam type changes and clear dependent selections
watch(() => formData.examType, (newExamType, oldExamType) => {
  if (newExamType !== oldExamType) {
    console.log('🔄 Exam type changed from', oldExamType, 'to', newExamType)

    // Clear subject selection
    if (formData.subjectCode) {
      console.log('🔄 Clearing subject selection')
      formData.subjectCode = ''
    }

    // Clear grade level selection since grading system may have changed
    if (formData.gradeLevel) {
      console.log('🔄 Clearing grade level selection due to grading system change')
      formData.gradeLevel = ''
    }

    // Clear award when exam type changes
    if (formData.award) {
      formData.award = ''
    }

    // Log the new grading system
    const newGradingSystem = EXAM_GRADING_SYSTEMS.find(system => system.examType === newExamType)
    if (newGradingSystem) {
      console.log('📊 Switched to grading system:', newGradingSystem.name)
    }
  }
})

// Auto-populate award when grade level changes (only if award is empty)
watch(() => formData.gradeLevel, (newGrade) => {
  if (newGrade && !formData.award) {
    formData.award = getAwardForGrade(newGrade)
  }
})

// Lifecycle - subjects and exam types are auto-loaded by composables
onMounted(() => {
  console.log('Grade boundary creation form mounted')
})
</script>

<style scoped>
/* MANEB Theme Colors */
.bg-maneb-primary {
  background-color: #a12c2c;
}

.text-maneb-primary {
  color: #a12c2c;
}

.bg-maneb-primary-dark {
  background-color: #8b2424;
}

.border-maneb-primary {
  border-color: #a12c2c;
}

.focus\:ring-maneb-primary:focus {
  --tw-ring-color: #a12c2c;
}

.focus\:border-maneb-primary:focus {
  border-color: #a12c2c;
}


</style>

/**
 * @fileoverview Composable for managing papers from API
 * @description Provides reactive papers data and searching functionality
 */

import { ref, computed, onMounted } from 'vue'
import { paperService, type PaperDto, type PaperOption, type PaperFilters } from '@/services/data/paper.service'

/**
 * Composable for papers management
 */
export function usePapers() {
  // Reactive state
  const papers = ref<PaperDto[]>([])
  const isLoading = ref(false)
  const error = ref<string | null>(null)
  const searchQuery = ref('')
  const filters = ref<PaperFilters>({})

  /**
   * Load papers from API
   */
  const loadPapers = async (forceRefresh: boolean = false) => {
    isLoading.value = true
    error.value = null
    
    try {
      papers.value = await paperService.fetchPapers(forceRefresh)
      console.log('✅ usePapers: Papers loaded from API:', papers.value.length, 'papers')
    } catch (err) {
      error.value = 'Failed to load papers'
      console.error('❌ usePapers: Error loading papers from API:', err)
      papers.value = []
    } finally {
      isLoading.value = false
    }
  }

  /**
   * Get active papers only
   */
  const activePapers = computed(() => 
    papers.value.filter(paper => !paper.isDeleted)
  )

  /**
   * Get filtered papers based on current filters and search
   */
  const filteredPapers = computed(() => {
    let result = activePapers.value

    // Apply exam level filter
    if (filters.value.examLevel) {
      result = result.filter(paper => paper.examLevel === filters.value.examLevel)
    }

    // Apply subject code filter
    if (filters.value.subjectCode) {
      result = result.filter(paper => paper.subjectCode === filters.value.subjectCode)
    }

    // Apply search query
    if (searchQuery.value) {
      const query = searchQuery.value.toLowerCase()
      result = result.filter(paper =>
        paper.paperName.toLowerCase().includes(query) ||
        paper.paperId.toLowerCase().includes(query) ||
        paper.subjectName.toLowerCase().includes(query) ||
        paper.subjectCode.toLowerCase().includes(query)
      )
    }

    return result
  })

  /**
   * Get papers as dropdown options
   */
  const paperOptions = computed((): PaperOption[] => 
    filteredPapers.value.map(paper => ({
      id: paper.id,
      name: paper.paperName,
      code: paper.paperId,
      examLevel: paper.examLevel,
      subjectCode: paper.subjectCode,
      subjectName: paper.subjectName,
      paperMark: paper.paperMark
    }))
  )

  /**
   * Get unique exam levels from papers
   */
  const examLevels = computed(() => {
    const levels = [...new Set(activePapers.value.map(paper => paper.examLevel))]
    return levels.sort()
  })

  /**
   * Get unique subjects from papers
   */
  const subjects = computed(() => {
    const subjectsMap = new Map<string, string>()
    
    activePapers.value.forEach(paper => {
      subjectsMap.set(paper.subjectCode, paper.subjectName)
    })
    
    return Array.from(subjectsMap.entries()).map(([code, name]) => ({ code, name }))
  })

  /**
   * Search papers
   */
  const searchPapers = (query: string) => {
    searchQuery.value = query
  }

  /**
   * Set filters
   */
  const setFilters = (newFilters: Partial<PaperFilters>) => {
    filters.value = { ...filters.value, ...newFilters }
  }

  /**
   * Clear filters
   */
  const clearFilters = () => {
    filters.value = {}
    searchQuery.value = ''
  }

  /**
   * Get paper by ID
   */
  const getPaperById = (id: string): PaperDto | undefined => {
    return papers.value.find(paper => paper.id === id)
  }

  /**
   * Get papers by exam level
   */
  const getPapersByExamLevel = (examLevel: string): PaperDto[] => {
    return activePapers.value.filter(paper => paper.examLevel === examLevel)
  }

  /**
   * Get papers by subject code
   */
  const getPapersBySubjectCode = (subjectCode: string): PaperDto[] => {
    return activePapers.value.filter(paper => paper.subjectCode === subjectCode)
  }

  /**
   * Refresh papers data
   */
  const refresh = () => loadPapers(true)

  /**
   * Clear error state
   */
  const clearError = () => {
    error.value = null
  }

  return {
    // State
    papers,
    activePapers,
    filteredPapers,
    paperOptions,
    examLevels,
    subjects,
    isLoading,
    error,
    searchQuery,
    filters,
    
    // Methods
    loadPapers,
    searchPapers,
    setFilters,
    clearFilters,
    getPaperById,
    getPapersByExamLevel,
    getPapersBySubjectCode,
    refresh,
    clearError
  }
}

/**
 * Composable that auto-loads papers on mount
 */
export function usePapersAutoLoad() {
  const papersComposable = usePapers()
  
  onMounted(async () => {
    await papersComposable.loadPapers()
  })
  
  return papersComposable
}

/**
 * Composable for searchable paper dropdown
 */
export function useSearchablePapers(initialFilters?: PaperFilters) {
  const papersComposable = usePapersAutoLoad()
  
  // Apply initial filters if provided
  if (initialFilters) {
    papersComposable.setFilters(initialFilters)
  }
  
  return {
    ...papersComposable,
    // Additional methods for searchable dropdown
    searchOptions: papersComposable.paperOptions,
    search: papersComposable.searchPapers,
    isSearching: papersComposable.isLoading
  }
}

<template>
  <div class="bg-white rounded-lg border border-gray-200 p-3">
    <div class="flex items-center justify-between">
      <h3 class="text-sm font-medium text-gray-700">Sorting Options</h3>
      
      <div class="flex items-center space-x-4">
        <div class="flex items-center space-x-2">
          <label for="sortBy" class="text-xs font-medium text-gray-700">Sort by:</label>
          <select
            id="sortBy"
            v-model="sortBy"
            @change="onSortChange"
            :disabled="isLoading"
            class="bg-gray-50 border border-gray-300 text-gray-900 text-xs rounded focus:ring-maneb-primary focus:border-maneb-primary p-1 disabled:bg-gray-100 disabled:cursor-not-allowed"
          >
            <option value="sequenceNo">Sequence Number</option>
            <option value="candidateNumber">Candidate Number</option>
            <option value="examinationNumber">Examination Number</option>
            <option value="centerNumber">Center Number</option>
          </select>
        </div>

        <div class="flex items-center space-x-2">
          <label for="sortOrder" class="text-xs font-medium text-gray-700">Order:</label>
          <select
            id="sortOrder"
            v-model="sortOrder"
            @change="onSortChange"
            :disabled="isLoading"
            class="bg-gray-50 border border-gray-300 text-gray-900 text-xs rounded focus:ring-maneb-primary focus:border-maneb-primary p-1 disabled:bg-gray-100 disabled:cursor-not-allowed"
          >
            <option value="asc">Ascending</option>
            <option value="desc">Descending</option>
          </select>
        </div>

        <button
          @click="onResetSort"
          :disabled="isLoading || (sortBy === 'sequenceNo' && sortOrder === 'asc')"
          class="px-3 py-1 bg-gray-500 text-white text-xs font-medium rounded hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors"
        >
          Reset
        </button>
      </div>
    </div>

    <div v-if="totalCandidates > 0" class="mt-2 text-xs text-gray-500">
      Total candidates: {{ totalCandidates.toLocaleString() }}
      <span v-if="currentPosition > 0">
        | Current position: {{ currentPosition.toLocaleString() }}
      </span>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  sortBy: string
  sortOrder: 'asc' | 'desc'
  isLoading: boolean
  totalCandidates: number
  currentPosition: number
}

interface Emits {
  (e: 'sortChange', sortBy: string, sortOrder: 'asc' | 'desc'): void
  (e: 'resetSort'): void
}

defineProps<Props>()
const emit = defineEmits<Emits>()

const onSortChange = () => {
  emit('sortChange', sortBy.value, sortOrder.value)
}

const onResetSort = () => {
  emit('resetSort')
}
</script>

<template>
  <div class="landing-page">
    <div class="hero">
      <div class="overlay">
        <h1>Travel around<br />Follow your heart</h1>
        <p>
          Lorem ipsum dolor sit amet, consectetur adipiscing elit. Cursus imperdiet sed id elementum.
          Quam vel aliquam sit vulputate. Lorem ipsum dolor sit amet, consectetur adipiscing elit.
          Cursus imperdiet sed id elementum. Quam vel aliquam sit vulputate.
        </p>
        <button class="get-started">Get started</button>
      </div>
    </div>

    <div class="categories">
      <div class="category" v-for="(item, index) in categories" :key="index">
        <img :src="item.image" :alt="item.name" />
        <div class="label">{{ item.name }}</div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'

const categories = ref([
  { name: 'Stays', image: 'https://picsum.photos/244/200/' },
  { name: 'Flights', image: 'https://picsum.photos/732/200/' },
  { name: 'Cruises', image: 'https://picsum.photos/542/200/' },
  { name: 'Cars', image: 'https://picsum.photos/421/200/' },
  { name: 'Fun', image: 'https://picsum.photos/423/200/' },
  { name: 'Restaurants', image: 'https://picsum.photos/135/200/' },
])

</script>

<style scoped>
.landing-page {
  font-family: sans-serif;
  color: #fff;
  position: relative;

}

.hero {
  background-image: url('https://picsum.photos/1600/900/');
  background-size: cover;
  background-position: center;
  padding: 80px 20px;
  position: relative;
  border-radius: 0 0 20px 20px;
  z-index: 1;
}

.overlay {
  max-width: 600px;
  background-color: rgba(0, 0, 0, 0.4);
  padding: 40px;
  border-radius: 12px;
}

.hero h1 {
  font-size: 2.8rem;
  margin-bottom: 20px;
}

.hero p {
  font-size: 1.1rem;
  margin-bottom: 30px;
}

.get-started {
  padding: 12px 24px;
  font-size: 1rem;
  background-color: white;
  color: black;
  border: none;
  border-radius: 30px;
  cursor: pointer;
}

.categories {
  display: flex;
  justify-content: space-around;
  flex-wrap: wrap;
  padding: 20px;
  border-radius: 20px;
  margin-top: -60px; /* Pull up into the hero */
  /* box-shadow: 0 -5px 15px rgba(0, 0, 0, 0.1); */
  position: relative;
  z-index: 2;
}


.category {
  width: 240px;
  text-align: center;
  /* margin: 10px; */
  position: relative;
}

.category img {
  width: 100%;
  border-radius: 10px;
  height: 150px;
  object-fit: cover;
}

.label {
  margin-top: 8px;
  font-weight: bold;
  color: #000;
}
</style>

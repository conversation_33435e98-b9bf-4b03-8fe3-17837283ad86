<template>
  <div class="min-h-screen bg-gray-50 p-8">
    <div class="max-w-7xl mx-auto">
      <!-- Welcome Section -->
      <div class="bg-white rounded-lg shadow-sm p-8 mb-6">
        <div class="flex items-center justify-between">
          <div>
            <h1 class="text-3xl font-bold text-gray-900 mb-2">
              {{ authStore.isDataClerk ? 'Data Clerk Dashboard' : 'Admin Dashboard' }}
            </h1>
            <p class="text-gray-600">Welcome to the MANEB Results Management System</p>
            <div class="mt-3 flex items-center">
              <span class="text-sm text-gray-500 mr-2">Logged in as:</span>
              <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium"
                    :class="authStore.isDataClerk ? 'bg-blue-100 text-blue-800' : 'bg-red-100 text-red-800'">
                {{ authStore.userFullName }} ({{ authStore.isDataClerk ? 'Data Clerk' : 'Administrator' }})
              </span>
            </div>
          </div>
          <div class="text-right">
            <div class="text-sm text-gray-500">Access Level</div>
            <div class="text-lg font-semibold text-maneb-primary">
              {{ authStore.isDataClerk ? 'Limited Access' : 'Full Access' }}
            </div>
          </div>
        </div>
      </div>

      <!-- Available Modules -->
      <div class="bg-white rounded-lg shadow-sm p-8">
        <h2 class="text-xl font-semibold text-gray-900 mb-6">Available Modules</h2>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <!-- Dashboard Module (Always Available) -->
          <router-link to="/admin/dashboard"
                       class="bg-gray-50 p-6 rounded-lg border border-gray-200 hover:bg-gray-100 transition-colors duration-200 block">
            <div class="flex items-center mb-3">
              <div class="bg-green-100 p-2 rounded-lg mr-3">
                <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z" />
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5a2 2 0 012-2h4a2 2 0 012 2v0M8 5a2 2 0 012-2h4a2 2 0 012 2v0" />
                </svg>
              </div>
              <h3 class="text-lg font-semibold text-maneb-primary">Dashboard</h3>
            </div>
            <p class="text-black text-sm">System overview and statistics</p>
          </router-link>

          <!-- Score Entry Module -->
          <router-link to="/admin/score-entry"
                       class="bg-gray-50 p-6 rounded-lg border border-gray-200 hover:bg-gray-100 transition-colors duration-200 block">
            <div class="flex items-center mb-3">
              <div class="bg-blue-100 p-2 rounded-lg mr-3">
                <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                </svg>
              </div>
              <h3 class="text-lg font-semibold text-maneb-primary">Score Entry</h3>
            </div>
            <p class="text-black text-sm">Enter and manage examination scores</p>
          </router-link>

          <!-- Grading System (Admin Only) -->
          <router-link v-if="authStore.hasAdminPrivileges"
                       to="/admin/grading-system"
                       class="bg-gray-50 p-6 rounded-lg border border-gray-200 hover:bg-gray-100 transition-colors duration-200 block">
            <div class="flex items-center mb-3">
              <div class="bg-purple-100 p-2 rounded-lg mr-3">
                <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                </svg>
              </div>
              <h3 class="text-lg font-semibold text-maneb-primary">Grading System</h3>
            </div>
            <p class="text-black text-sm">Configure grading boundaries and schemes</p>
          </router-link>

          <!-- Results Management (Admin Only) -->
          <router-link v-if="authStore.hasAdminPrivileges"
                       to="/admin/results"
                       class="bg-gray-50 p-6 rounded-lg border border-gray-200 hover:bg-gray-100 transition-colors duration-200 block">
            <div class="flex items-center mb-3">
              <div class="bg-orange-100 p-2 rounded-lg mr-3">
                <svg class="w-6 h-6 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              </div>
              <h3 class="text-lg font-semibold text-maneb-primary">Results Management</h3>
            </div>
            <p class="text-black text-sm">View and manage examination results</p>
          </router-link>

          <!-- User Management (Admin Only) -->
          <router-link v-if="authStore.hasAdminPrivileges"
                       to="/admin/user-management"
                       class="bg-gray-50 p-6 rounded-lg border border-gray-200 hover:bg-gray-100 transition-colors duration-200 block">
            <div class="flex items-center mb-3">
              <div class="bg-red-100 p-2 rounded-lg mr-3">
                <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                </svg>
              </div>
              <h3 class="text-lg font-semibold text-maneb-primary">User Management</h3>
            </div>
            <p class="text-black text-sm">Manage users, roles, and permissions</p>
          </router-link>
        </div>

        <!-- Role Information -->
        <div class="mt-8 p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <h3 class="text-sm font-medium text-blue-900 mb-2">Access Information</h3>
          <div class="text-sm text-blue-700">
            <p v-if="authStore.isDataClerk">
              <strong>Data Clerk Access:</strong> You can see and access Dashboard and Score Entry modules.
              Additional modules are available to administrators only.
            </p>
            <p v-else-if="authStore.hasAdminPrivileges">
              <strong>Administrator Access:</strong> You have full access to all system modules and features.
            </p>
            <p v-else>
              <strong>Limited Access:</strong> Your access level is determined by your assigned role.
              Only modules you have permission to access are displayed above.
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useAuthStore } from '@/store/auth.store'

// Stores and router
const authStore = useAuthStore()

// Debug logging removed
</script>

<style scoped>
/* MANEB Theme Colors */
.text-maneb-primary {
  color: #a12c2c;
}
</style>

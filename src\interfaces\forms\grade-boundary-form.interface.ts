/**
 * @fileoverview Grade boundary form interfaces for MANEB Results Management System
 * @description Standardized interfaces for grade boundary form data and validation
 */

import type { ExamLevel, GradeLevel } from '../common/exam-types.interface';
import type { ValidationResult } from '../common/base.interface';

/**
 * Grade boundary form data (Internal EMS)
 * @description Form data structure for creating/editing grade boundaries
 */
export interface GradeBoundaryFormData {
  /** Examination level */
  examLevel: ExamLevel | '';
  /** Subject ID */
  subjectId: string;
  /** Subject code (for display/validation) */
  subjectCode?: string;
  /** Academic year */
  year: number | '';
  /** Grade level */
  gradeLevel: GradeLevel | '';
  /** Lower score boundary */
  lowerBound: number;
  /** Upper score boundary */
  upperBound: number;
  /** Optional description */
  description?: string;
  /** Whether this boundary is active */
  isActive: boolean;
}

/**
 * External grade boundary form data (External Grading API)
 * @description Form data structure for external grading system
 */
export interface ExternalGradeBoundaryFormData {
  /** Examination level */
  examLevel: string;
  /** Subject code */
  subjectCode: string;
  /** Grade letter */
  gradeLetter: string;
  /** Lower score boundary */
  lowerBound: number;
  /** Upper score boundary */
  upperBound: number;
  /** Approval status */
  approvalStatus: string;
  /** ID of user setting the boundary */
  setById: string;
}

/**
 * Comprehensive grade boundary form data
 * @description Form data for creating complete grade boundary sets
 */
export interface ComprehensiveGradeBoundaryFormData {
  /** Examination level */
  examLevel: ExamLevel | '';
  /** Subject ID */
  subjectId: string;
  /** Academic year */
  year: number | '';
  /** Grade boundary sets for all grades */
  gradeBoundaries: {
    A: GradeBoundarySetFormData;
    B: GradeBoundarySetFormData;
    C: GradeBoundarySetFormData;
    D: GradeBoundarySetFormData;
    F: GradeBoundarySetFormData;
  };
  /** Whether boundaries are active */
  isActive?: boolean;
}

/**
 * Grade boundary set form data
 * @description Individual grade boundary within a comprehensive set
 */
export interface GradeBoundarySetFormData {
  /** Grade level */
  gradeLevel: GradeLevel;
  /** Lower score boundary */
  lowerBound: number;
  /** Upper score boundary */
  upperBound: number;
  /** Optional description */
  description?: string;
}

/**
 * Grade boundary form validation rules
 * @description Validation configuration for grade boundary forms
 */
export interface GradeBoundaryFormValidation {
  /** Required fields */
  required: (keyof GradeBoundaryFormData)[];
  /** Score range validation */
  scoreRange: {
    min: number;
    max: number;
  };
  /** Year range validation */
  yearRange: {
    min: number;
    max: number;
  };
  /** Custom validation rules */
  customRules?: {
    /** Rule name */
    name: string;
    /** Validation function */
    validator: (data: GradeBoundaryFormData) => boolean;
    /** Error message */
    message: string;
  }[];
}

/**
 * Grade boundary form errors
 * @description Error state for grade boundary forms
 */
export interface GradeBoundaryFormErrors {
  /** Examination level error */
  examLevel?: string;
  /** Subject error */
  subjectId?: string;
  /** Subject code error */
  subjectCode?: string;
  /** Year error */
  year?: string;
  /** Grade level error */
  gradeLevel?: string;
  /** Lower bound error */
  lowerBound?: string;
  /** Upper bound error */
  upperBound?: string;
  /** Description error */
  description?: string;
  /** General form error */
  general?: string;
}

/**
 * Grade boundary form state
 * @description Complete form state including data, errors, and status
 */
export interface GradeBoundaryFormState {
  /** Form data */
  data: GradeBoundaryFormData;
  /** Form errors */
  errors: GradeBoundaryFormErrors;
  /** Whether form is being submitted */
  isSubmitting: boolean;
  /** Whether form has been touched */
  isTouched: boolean;
  /** Whether form is valid */
  isValid: boolean;
  /** Validation warnings */
  warnings: string[];
}

/**
 * Grade boundary form props
 * @description Props interface for grade boundary form components
 */
export interface GradeBoundaryFormProps {
  /** Whether modal/form is open */
  isOpen: boolean;
  /** Existing boundary data for editing */
  boundary?: any | null;
  /** Pre-selected subject */
  preSelectedSubject?: string | null;
  /** Pre-selected year */
  preSelectedYear?: number | null;
  /** Whether form is in edit mode */
  isEditing?: boolean;
  /** Form validation rules */
  validationRules?: GradeBoundaryFormValidation;
}

/**
 * Grade boundary form events
 * @description Event interface for grade boundary form components
 */
export interface GradeBoundaryFormEvents {
  /** Form close event */
  close: [];
  /** Form success event */
  success: [];
  /** Form error event */
  error: [error: string];
  /** Form validation event */
  validate: [result: ValidationResult];
  /** Form data change event */
  change: [data: GradeBoundaryFormData];
}

/**
 * Grade boundary bulk form data
 * @description Form data for bulk grade boundary operations
 */
export interface GradeBoundaryBulkFormData {
  /** Operation type */
  operation: 'create' | 'update' | 'delete';
  /** Examination level filter */
  examLevel?: ExamLevel | 'All';
  /** Subject filter */
  subjectId?: string | 'All';
  /** Year filter */
  year?: number | 'All';
  /** Grade boundaries to process */
  boundaries: GradeBoundaryFormData[];
  /** Additional options */
  options?: {
    /** Whether to validate before processing */
    validateFirst?: boolean;
    /** Whether to stop on first error */
    stopOnError?: boolean;
    /** Whether to create backup */
    createBackup?: boolean;
  };
}

/**
 * Grade boundary import form data
 * @description Form data for importing grade boundaries from file
 */
export interface GradeBoundaryImportFormData {
  /** File to import */
  file: File | null;
  /** File format */
  format: 'csv' | 'excel' | 'json';
  /** Whether to overwrite existing boundaries */
  overwriteExisting: boolean;
  /** Whether to validate before import */
  validateFirst: boolean;
  /** Column mapping for CSV/Excel */
  columnMapping?: Record<string, string>;
  /** Import options */
  options?: {
    /** Skip header row */
    skipHeader?: boolean;
    /** Delimiter for CSV */
    delimiter?: string;
    /** Sheet name for Excel */
    sheetName?: string;
  };
}

/**
 * Grade boundary export form data
 * @description Form data for exporting grade boundaries
 */
export interface GradeBoundaryExportFormData {
  /** Export format */
  format: 'csv' | 'excel' | 'json' | 'pdf';
  /** Filters for export */
  filters: {
    examLevel?: ExamLevel | 'All';
    subjectId?: string | 'All';
    year?: number | 'All';
    gradeLevel?: GradeLevel | 'All';
    isActive?: boolean | 'All';
  };
  /** Export options */
  options?: {
    /** Include headers */
    includeHeaders?: boolean;
    /** Include metadata */
    includeMetadata?: boolean;
    /** File name */
    fileName?: string;
  };
}

<template>
  <div class="certificate-container bg-white shadow-2xl rounded-lg overflow-hidden max-w-4xl mx-auto">
    <!-- Certificate Header -->
    <div class="certificate-header bg-gradient-to-r from-red-50 to-red-100 p-8 text-center border-b-4 border-maneb-primary">
      <!-- MANEB Logo and Title -->
      <div class="flex items-center justify-center mb-6">
        <div class="w-16 h-16 bg-white rounded-full flex items-center justify-center mr-4 shadow-md">
          <svg class="w-10 h-10 text-maneb-primary" fill="currentColor" viewBox="0 0 24 24">
            <path d="M12 2L2 7v10c0 5.55 3.84 9.74 9 11 5.16-1.26 9-5.45 9-11V7l-10-5z"/>
          </svg>
        </div>
        <div class="text-left">
          <h1 class="text-2xl font-bold text-gray-900">MALAWI NATIONAL EXAMINATIONS BOARD</h1>
          <p class="text-lg text-maneb-primary font-semibold">MANEB</p>
        </div>
      </div>
      
      <!-- Certificate Title -->
      <div class="mb-4">
        <h2 class="text-3xl font-bold text-maneb-primary mb-2">CERTIFICATE OF EXAMINATION</h2>
        <p class="text-lg text-gray-700">{{ certificate.examSession }}</p>
      </div>
      
      <!-- Certificate Number -->
      <div class="text-sm text-gray-600">
        Certificate No: <span class="font-mono font-semibold">{{ certificate.certificateNumber }}</span>
      </div>
    </div>

    <!-- Student Information -->
    <div class="p-8 border-b border-gray-200">
      <div class="text-center mb-6">
        <p class="text-lg text-gray-700 mb-2">This is to certify that</p>
        <h3 class="text-2xl font-bold text-gray-900 mb-2">{{ certificate.studentName || certificate.studentId }}</h3>
        <div class="flex justify-center space-x-8 text-sm text-gray-600">
          <div>Student ID: <span class="font-semibold">{{ certificate.studentId }}</span></div>
          <div v-if="certificate.examNumber">Exam Number: <span class="font-semibold">{{ certificate.examNumber }}</span></div>
        </div>
      </div>
      
      <p class="text-center text-lg text-gray-700">
        has successfully completed the examination in the following subjects:
      </p>
    </div>

    <!-- Subjects and Results -->
    <div class="p-8">
      <div class="space-y-6">
        <div 
          v-for="subject in certificate.subjects" 
          :key="subject.subjectId"
          class="bg-gray-50 rounded-lg p-6 border border-gray-200"
        >
          <!-- Subject Header -->
          <div class="flex items-center justify-between mb-4">
            <div>
              <h4 class="text-xl font-semibold text-gray-900">{{ subject.subjectName }}</h4>
              <p class="text-sm text-gray-600">Subject Code: {{ subject.subjectCode }}</p>
            </div>
            <div class="text-right">
              <div class="flex items-center">
                <span class="text-lg font-bold mr-2">Overall Grade:</span>
                <span 
                  class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium"
                  :class="getGradeBadgeClass(subject.overallGrade)"
                >
                  {{ subject.overallGrade }}
                </span>
              </div>
              <div class="text-sm mt-1" :class="subject.overallPassStatus ? 'text-green-600' : 'text-red-600'">
                {{ subject.overallPassStatus ? 'PASS' : 'FAIL' }}
              </div>
            </div>
          </div>

          <!-- Papers -->
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div 
              v-for="paper in subject.papers" 
              :key="paper.paperId"
              class="bg-white rounded-md p-4 border border-gray-100"
            >
              <div class="flex justify-between items-center">
                <div>
                  <p class="font-medium text-gray-900">{{ paper.paperName }}</p>
                  <p class="text-xs text-gray-500">{{ paper.paperId }}</p>
                </div>
                <div class="text-right">
                  <div class="text-lg font-bold text-gray-900">{{ paper.score }}%</div>
                  <div class="flex items-center space-x-2">
                    <span 
                      class="inline-flex items-center px-2 py-1 rounded text-xs font-medium"
                      :class="getGradeBadgeClass(paper.grade)"
                    >
                      {{ paper.grade }}
                    </span>
                    <span 
                      class="text-xs font-medium"
                      :class="paper.passStatus ? 'text-green-600' : 'text-red-600'"
                    >
                      {{ paper.passStatus ? 'PASS' : 'FAIL' }}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Overall Performance Summary -->
    <div class="bg-gray-50 p-8 border-t border-gray-200">
      <h4 class="text-xl font-semibold text-gray-900 mb-4 text-center">Overall Performance Summary</h4>
      <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
        <div class="bg-white rounded-lg p-4 border border-gray-200">
          <div class="text-2xl font-bold text-maneb-primary">{{ certificate.overallPerformance.totalSubjects }}</div>
          <div class="text-sm text-gray-600">Total Subjects</div>
        </div>
        <div class="bg-white rounded-lg p-4 border border-gray-200">
          <div class="text-2xl font-bold text-green-600">{{ certificate.overallPerformance.passedSubjects }}</div>
          <div class="text-sm text-gray-600">Passed</div>
        </div>
        <div class="bg-white rounded-lg p-4 border border-gray-200">
          <div class="text-2xl font-bold text-red-600">{{ certificate.overallPerformance.failedSubjects }}</div>
          <div class="text-sm text-gray-600">Failed</div>
        </div>
        <div class="bg-white rounded-lg p-4 border border-gray-200">
          <div class="text-2xl font-bold text-blue-600">{{ certificate.overallPerformance.passRate.toFixed(1) }}%</div>
          <div class="text-sm text-gray-600">Pass Rate</div>
        </div>
      </div>
      
      <!-- Overall Grade -->
      <div class="text-center mt-6">
        <div class="inline-flex items-center space-x-3">
          <span class="text-lg font-semibold text-gray-900">Overall Grade:</span>
          <span 
            class="inline-flex items-center px-4 py-2 rounded-full text-lg font-bold"
            :class="getGradeBadgeClass(certificate.overallPerformance.overallGrade)"
          >
            {{ certificate.overallPerformance.overallGrade }}
          </span>
        </div>
      </div>
    </div>

    <!-- Certificate Footer -->
    <div class="bg-maneb-primary text-white p-6 text-center">
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
        <div>
          <p class="font-semibold">Issue Date:</p>
          <p>{{ formatDate(certificate.issueDate) }}</p>
        </div>
        <div>
          <p class="font-semibold">Issued By:</p>
          <p>{{ certificate.issuedBy }}</p>
        </div>
      </div>
      <div class="mt-4 pt-4 border-t border-red-400">
        <p class="text-xs opacity-90">
          This certificate is issued by the Malawi National Examinations Board and is valid for official purposes.
        </p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { StudentCertificateDto } from '@/interfaces'

// Local type definition for grade levels
type GradeLevel = 'A' | 'B' | 'C' | 'D' | 'F'

// Props
interface Props {
  certificate: StudentCertificateDto
}

const props = defineProps<Props>()

// Helper methods
const getGradeBadgeClass = (grade: string | GradeLevel): string => {
  const classes = {
    'A': 'bg-green-100 text-green-800',
    'B': 'bg-blue-100 text-blue-800',
    'C': 'bg-yellow-100 text-yellow-800',
    'D': 'bg-orange-100 text-orange-800',
    'F': 'bg-red-100 text-red-800'
  }
  return classes[grade as keyof typeof classes] || 'bg-gray-100 text-gray-800'
}

const formatDate = (date: Date): string => {
  return new Date(date).toLocaleDateString('en-GB', {
    day: 'numeric',
    month: 'long', 
    year: 'numeric'
  })
}
</script>

<style scoped>
/* MANEB Theme Colors */
.bg-maneb-primary {
  background-color: #a12c2c;
}

.text-maneb-primary {
  color: #a12c2c;
}

.border-maneb-primary {
  border-color: #a12c2c;
}

/* Print styles */
@media print {
  .certificate-container {
    box-shadow: none;
    border-radius: 0;
    max-width: none;
    margin: 0;
  }
  
  .certificate-header {
    background: #f9fafb !important;
    -webkit-print-color-adjust: exact;
    color-adjust: exact;
  }
  
  .bg-maneb-primary {
    background-color: #a12c2c !important;
    -webkit-print-color-adjust: exact;
    color-adjust: exact;
  }
}
</style>

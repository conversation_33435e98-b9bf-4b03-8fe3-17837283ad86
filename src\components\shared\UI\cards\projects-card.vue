<template>
  <div class="bg-white p-6 mt-6 rounded-2xl shadow-md w-full">
    <!-- Header -->
    <div class="flex items-center justify-between mb-6">
      <div>
        <h2 class="text-xl font-bold text-gray-800">Projects</h2>
        <p class="text-gray-500 text-sm">Architects design houses</p>
      </div>
    </div>

    <!-- Projects Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <div v-for="(project, index) in props.projects" :key="index" class="bg-white rounded-2xl shadow-sm overflow-hidden border border-gray-200">
        <img :src="project.image" :alt="project.title" class="w-full h-40 object-cover">
        <div class="p-4">
          <h3 class="text-lg font-semibold text-gray-800 mb-1">{{ project.title }}</h3>
          <p class="text-gray-500 text-sm mb-4">{{ project.description }}</p>
          <div class="flex items-center justify-between">
            <button class="text-teal-500 font-semibold text-xs px-3 py-1 rounded-lg border border-teal-500 hover:bg-teal-500 hover:text-white transition-colors duration-200">
              VIEW ALL
            </button>
            <!-- Placeholder for icons, if needed. You can expand this with props for specific icons. -->
            <div class="flex space-x-1">
              <span class="w-5 h-5 bg-purple-200 rounded-full"></span>
              <span class="w-5 h-5 bg-purple-300 rounded-full"></span>
              <span class="w-5 h-5 bg-purple-400 rounded-full"></span>
            </div>
          </div>
        </div>
      </div>

      <!-- Create New Project Card -->
      <div class="bg-gray-50 rounded-2xl border-2 border-dashed border-gray-300 flex flex-col items-center justify-center p-6 text-center cursor-pointer hover:border-teal-500 transition-colors duration-200">
        <svg class="w-12 h-12 text-gray-400 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
        </svg>
        <span class="text-gray-600 font-medium">Create a New Project</span>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
interface Project {
  image: string;
  title: string;
  description: string;
}

interface Props {
  projects: Project[];
}

const props = withDefaults(defineProps<Props>(), {
  projects: () => []
})

</script>

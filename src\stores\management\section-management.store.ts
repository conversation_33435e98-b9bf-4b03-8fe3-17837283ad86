/**
 * @fileoverview Section Management Store for MANEB Results Management System
 * @description Pinia store for managing section state with full CRUD operations
 */

import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import { sectionService } from '@/services';
import type { 
  SectionDto, 
  CreateSectionRequest, 
  UpdateSectionRequest,
  SectionFilterDto,
  RecordStatus
} from '@/interfaces';

export const useSectionManagementStore = defineStore('sectionManagement', () => {
  // State
  const sections = ref<SectionDto[]>([]);
  const currentSection = ref<SectionDto | null>(null);
  const isLoading = ref(false);
  const error = ref<string | null>(null);
  const searchQuery = ref('');
  const filters = ref<SectionFilterDto>({
    searchQuery: '',
    controller: 'All',
    area: 'All',
    status: 'All',
    parentSectionId: 'All',
    level: 'All',
    isActive: 'All',
    hasUsers: 'All'
  });
  const pagination = ref({
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 0
  });
  const sectionHierarchy = ref<SectionDto[]>([]);

  // Getters
  const filteredSections = computed(() => {
    let result = sections.value;

    if (searchQuery.value) {
      const query = searchQuery.value.toLowerCase();
      result = result.filter(section =>
        section.name?.toLowerCase().includes(query) ||
        section.controller?.toLowerCase().includes(query) ||
        section.area?.toLowerCase().includes(query) ||
        section.description?.toLowerCase().includes(query)
      );
    }

    if (filters.value.controller && filters.value.controller !== 'All') {
      result = result.filter(section => section.controller === filters.value.controller);
    }

    if (filters.value.area && filters.value.area !== 'All') {
      result = result.filter(section => section.area === filters.value.area);
    }

    if (filters.value.status && filters.value.status !== 'All') {
      result = result.filter(section => section.status === filters.value.status);
    }

    if (filters.value.parentSectionId && filters.value.parentSectionId !== 'All') {
      result = result.filter(section => section.parentSectionId === filters.value.parentSectionId);
    }

    if (filters.value.level !== undefined && filters.value.level !== 'All') {
      result = result.filter(section => section.level === filters.value.level);
    }

    if (filters.value.isActive !== undefined && filters.value.isActive !== 'All') {
      result = result.filter(section => section.isActive === filters.value.isActive);
    }

    return result;
  });

  const sectionsByLevel = computed(() => {
    return (level: number) => sections.value.filter(section => section.level === level);
  });

  const rootSections = computed(() => 
    sections.value.filter(section => section.level === 0 || !section.parentSectionId)
  );

  const sectionsByStatus = computed(() => {
    return (status: RecordStatus) => sections.value.filter(section => section.status === status);
  });

  const totalSections = computed(() => sections.value.length);

  const activeSections = computed(() => 
    sections.value.filter(section => section.status === 'Approved' || section.status === 'SecondApproved')
  );

  // Actions
  const fetchSections = async (): Promise<void> => {
    try {
      isLoading.value = true;
      error.value = null;
      sections.value = await sectionService.getAllSections();
    } catch (err: any) {
      error.value = err.message || 'Failed to fetch sections';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const fetchSectionsPaginated = async (page: number = 1, limit: number = 10): Promise<void> => {
    try {
      isLoading.value = true;
      error.value = null;
      
      const response = await sectionService.getSectionsPaginated(page, limit, {
        Search: searchQuery.value || undefined,
        status: filters.value.status !== 'All' ? filters.value.status as RecordStatus : undefined
      });
      
      sections.value = response;
      pagination.value = {
        page,
        limit,
        total: response.length,
        totalPages: Math.ceil(response.length / limit)
      };
    } catch (err: any) {
      error.value = err.message || 'Failed to fetch sections';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const fetchSectionById = async (id: string): Promise<SectionDto> => {
    try {
      isLoading.value = true;
      error.value = null;
      const section = await sectionService.getSectionById(id);
      currentSection.value = section;
      return section;
    } catch (err: any) {
      error.value = err.message || 'Failed to fetch section';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const createSection = async (sectionData: CreateSectionRequest): Promise<SectionDto> => {
    try {
      isLoading.value = true;
      error.value = null;
      const newSection = await sectionService.createSection(sectionData);
      sections.value.push(newSection);
      return newSection;
    } catch (err: any) {
      error.value = err.message || 'Failed to create section';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const updateSection = async (id: string, sectionData: UpdateSectionRequest): Promise<void> => {
    try {
      isLoading.value = true;
      error.value = null;
      await sectionService.updateSection(id, sectionData);
      
      // Update local state
      const index = sections.value.findIndex(section => section.id === id);
      if (index !== -1) {
        sections.value[index] = { ...sections.value[index], ...sectionData };
      }
      
      if (currentSection.value?.id === id) {
        currentSection.value = { ...currentSection.value, ...sectionData };
      }
    } catch (err: any) {
      error.value = err.message || 'Failed to update section';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const deleteSection = async (id: string): Promise<void> => {
    try {
      isLoading.value = true;
      error.value = null;
      await sectionService.deleteSection(id);
      
      // Remove from local state
      sections.value = sections.value.filter(section => section.id !== id);
      if (currentSection.value?.id === id) {
        currentSection.value = null;
      }
    } catch (err: any) {
      error.value = err.message || 'Failed to delete section';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const approveSection = async (id: string): Promise<void> => {
    try {
      isLoading.value = true;
      error.value = null;
      await sectionService.approveSection(id);
      
      // Update local state
      const index = sections.value.findIndex(section => section.id === id);
      if (index !== -1) {
        sections.value[index].status = 'Approved';
      }
    } catch (err: any) {
      error.value = err.message || 'Failed to approve section';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const fetchSectionHierarchy = async (): Promise<void> => {
    try {
      isLoading.value = true;
      error.value = null;
      
      // Build hierarchy from flat sections list
      const allSections = await sectionService.getAllSections();
      sectionHierarchy.value = buildHierarchy(allSections);
    } catch (err: any) {
      error.value = err.message || 'Failed to fetch section hierarchy';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const fetchRootSections = async (): Promise<SectionDto[]> => {
    try {
      return await sectionService.getRootSections();
    } catch (err: any) {
      error.value = err.message || 'Failed to fetch root sections';
      throw err;
    }
  };

  const fetchChildSections = async (parentId: string): Promise<SectionDto[]> => {
    try {
      return await sectionService.getChildSections(parentId);
    } catch (err: any) {
      error.value = err.message || 'Failed to fetch child sections';
      throw err;
    }
  };

  const searchSections = async (query: string): Promise<void> => {
    searchQuery.value = query;
    await fetchSectionsPaginated(1, pagination.value.limit);
  };

  const filterSections = async (newFilters: Partial<SectionFilterDto>): Promise<void> => {
    filters.value = { ...filters.value, ...newFilters };
    await fetchSectionsPaginated(1, pagination.value.limit);
  };

  const toggleSectionStatus = async (id: string): Promise<void> => {
    try {
      isLoading.value = true;
      error.value = null;
      await sectionService.toggleSectionStatus(id);
      
      // Update local state
      const index = sections.value.findIndex(section => section.id === id);
      if (index !== -1) {
        sections.value[index].isActive = !sections.value[index].isActive;
      }
    } catch (err: any) {
      error.value = err.message || 'Failed to toggle section status';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const moveSection = async (sectionId: string, newParentId: string | null): Promise<void> => {
    try {
      isLoading.value = true;
      error.value = null;
      await sectionService.moveSection(sectionId, newParentId);
      
      // Refresh sections to get updated hierarchy
      await fetchSections();
    } catch (err: any) {
      error.value = err.message || 'Failed to move section';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  // Helper function to build hierarchy
  const buildHierarchy = (flatSections: SectionDto[]): SectionDto[] => {
    const sectionMap = new Map<string, SectionDto>();
    const rootSections: SectionDto[] = [];

    // Create a map of all sections
    flatSections.forEach(section => {
      sectionMap.set(section.id!, { ...section, childSections: [] });
    });

    // Build the hierarchy
    flatSections.forEach(section => {
      const sectionWithChildren = sectionMap.get(section.id!)!;
      
      if (section.parentSectionId) {
        const parent = sectionMap.get(section.parentSectionId);
        if (parent) {
          parent.childSections = parent.childSections || [];
          parent.childSections.push(sectionWithChildren);
        }
      } else {
        rootSections.push(sectionWithChildren);
      }
    });

    return rootSections;
  };

  const clearError = (): void => {
    error.value = null;
  };

  const clearCurrentSection = (): void => {
    currentSection.value = null;
  };

  const resetFilters = (): void => {
    filters.value = {
      searchQuery: '',
      controller: 'All',
      area: 'All',
      status: 'All',
      parentSectionId: 'All',
      level: 'All',
      isActive: 'All',
      hasUsers: 'All'
    };
    searchQuery.value = '';
  };

  return {
    // State
    sections,
    currentSection,
    isLoading,
    error,
    searchQuery,
    filters,
    pagination,
    sectionHierarchy,
    
    // Getters
    filteredSections,
    sectionsByLevel,
    rootSections,
    sectionsByStatus,
    totalSections,
    activeSections,
    
    // Actions
    fetchSections,
    fetchSectionsPaginated,
    fetchSectionById,
    createSection,
    updateSection,
    deleteSection,
    approveSection,
    fetchSectionHierarchy,
    fetchRootSections,
    fetchChildSections,
    searchSections,
    filterSections,
    toggleSectionStatus,
    moveSection,
    clearError,
    clearCurrentSection,
    resetFilters,
  };
});

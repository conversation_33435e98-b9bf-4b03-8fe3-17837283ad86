// Environment Configuration Utilities

export interface EnvironmentConfig {
  // Application
  appId: string
  appName: string
  appVersion: string
  appSecret: string
  
  // API URLs
  apiBaseUrl: string
  gradingApiBaseUrl: string
  
  // Developer Info (Optional)
  developerName?: string
  developerWebsite?: string
  developerGithub?: string
  developerEmail?: string
  developerPhone?: string
  developerAddress?: string
}

export interface ConfigValidation {
  isValid: boolean
  errors: string[]
  warnings: string[]
  config: EnvironmentConfig
}

/**
 * Get environment configuration with validation
 */
export function getEnvironmentConfig(): EnvironmentConfig {
  return {
    // Application
    appId: import.meta.env.VITE_APP_ID || 'EMSFRONTEND',
    appName: import.meta.env.VITE_APP_NAME || 'EMS Frontend',
    appVersion: import.meta.env.VITE_APP_VERSION || '1.0.0',
    appSecret: import.meta.env.VITE_APP_SECRET || '',
    
    // API URLs
    apiBaseUrl: import.meta.env.VITE_API_BASE_URL || 'https://localhost:7266/',
    gradingApiBaseUrl: import.meta.env.VITE_GRADING_API_BASE_URL || 'http://************:5000/api',
    
    // Developer Info (Optional)
    developerName: import.meta.env.VITE_APP_DEVELOPER_NAME,
    developerWebsite: import.meta.env.VITE_APP_DEVELOPER_WEBSITE,
    developerGithub: import.meta.env.VITE_APP_DEVELOPER_GITHUB,
    developerEmail: import.meta.env.VITE_APP_DEVELOPER_EMAIL,
    developerPhone: import.meta.env.VITE_APP_DEVELOPER_PHONE,
    developerAddress: import.meta.env.VITE_APP_DEVELOPER_ADDRESS,
  }
}

/**
 * Validate environment configuration
 */
export function validateEnvironmentConfig(): ConfigValidation {
  const config = getEnvironmentConfig()
  const errors: string[] = []
  const warnings: string[] = []

  // Required fields validation
  if (!config.appSecret) {
    errors.push('VITE_APP_SECRET is required but not set')
  }

  if (!config.apiBaseUrl) {
    errors.push('VITE_API_BASE_URL is required but not set')
  } else if (!config.apiBaseUrl.startsWith('http')) {
    errors.push('VITE_API_BASE_URL must start with http:// or https://')
  }

  if (!config.gradingApiBaseUrl) {
    errors.push('VITE_GRADING_API_BASE_URL is required but not set')
  } else if (!config.gradingApiBaseUrl.startsWith('http')) {
    errors.push('VITE_GRADING_API_BASE_URL must start with http:// or https://')
  }

  // Warnings for missing optional fields
  if (!import.meta.env.VITE_GRADING_API_BASE_URL) {
    warnings.push('VITE_GRADING_API_BASE_URL not set, using fallback URL')
  }

  if (!import.meta.env.VITE_API_BASE_URL) {
    warnings.push('VITE_API_BASE_URL not set, using fallback URL')
  }

  // Development environment warnings
  if (import.meta.env.DEV) {
    if (config.gradingApiBaseUrl.startsWith('http://')) {
      warnings.push('Using HTTP for grading API in development - consider HTTPS for production')
    }
    
    if (config.appSecret === '6ad4e9aa-d009-423d-ad65-c8a1a1f48efb') {
      warnings.push('Using default app secret - change this for production')
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
    config
  }
}

export function logEnvironmentConfig(): void {
  if (!import.meta.env.DEV) return
  validateEnvironmentConfig()
}

/**
 * Get API endpoints summary
 */
export function getApiEndpoints(): { name: string; url: string; purpose: string }[] {
  const config = getEnvironmentConfig()
  
  return [
    {
      name: 'Main EMS API',
      url: config.apiBaseUrl,
      purpose: 'User management, roles, permissions, sections'
    },
    {
      name: 'Grading & Audit API',
      url: config.gradingApiBaseUrl,
      purpose: 'Grade boundaries, score entries, audit logs'
    }
  ]
}

/**
 * Check if running in development mode
 */
export function isDevelopment(): boolean {
  return import.meta.env.DEV
}

/**
 * Check if running in production mode
 */
export function isProduction(): boolean {
  return import.meta.env.PROD
}

/**
 * Get build mode
 */
export function getBuildMode(): 'development' | 'production' {
  return import.meta.env.DEV ? 'development' : 'production'
}

<template>
  <div class="bg-white rounded-lg border border-gray-200 p-3">
    <div class="grid grid-cols-1 md:grid-cols-3 gap-3 items-end">
      
      <div>
        <label for="candidateSearch" class="block text-xs font-medium text-gray-700 mb-1">
          Search by Candidate/Examination/Center Number
        </label>
        <input
          id="candidateSearch"
          type="text"
          v-model="searchQuery"
          @input="onSearchInput"
          @keydown.enter="onSearchEnter"
          :placeholder="searchPlaceholder"
          :disabled="isLoading"
          class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-maneb-primary focus:border-maneb-primary block w-full p-2 disabled:bg-gray-100 disabled:cursor-not-allowed"
        />
        <div v-if="searchError" class="text-red-500 text-xs mt-1">
          {{ searchError }}
        </div>
      </div>

      <div>
        <label for="sequenceStart" class="block text-xs font-medium text-gray-700 mb-1">
          Start from Sequence Number
        </label>
        <input
          id="sequenceStart"
          type="number"
          v-model.number="startSequence"
          @change="onSequenceChange"
          :min="1"
          :max="totalCandidates"
          :placeholder="`1 to ${totalCandidates}`"
          :disabled="isLoading || totalCandidates === 0"
          class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-maneb-primary focus:border-maneb-primary block w-full p-2 disabled:bg-gray-100 disabled:cursor-not-allowed"
        />
      </div>

      <div class="flex space-x-2">
        <button
          @click="onSearch"
          :disabled="!searchQuery.trim() || isLoading"
          class="px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors"
        >
          {{ isLoading ? 'Searching...' : 'Search' }}
        </button>

        <button
          @click="onClearSearch"
          :disabled="isLoading"
          class="px-4 py-2 bg-gray-500 text-white text-sm font-medium rounded-lg hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors"
        >
          Clear
        </button>
      </div>

    </div>

    <div v-if="searchResults.length > 0" class="mt-4">
      <h4 class="text-sm font-medium text-gray-700 mb-2">Search Results:</h4>
      <div class="space-y-2 max-h-32 overflow-y-auto">
        <div
          v-for="result in searchResults"
          :key="result.id"
          @click="onSelectResult(result)"
          class="p-2 border border-gray-200 rounded cursor-pointer hover:bg-gray-50 transition-colors"
        >
          <div class="text-sm font-medium">{{ result.candidateNumber }}</div>
          <div class="text-xs text-gray-500">
            Exam: {{ result.examinationNumber }} | Center: {{ result.centerNumber }}
          </div>
        </div>
      </div>
    </div>

    <div v-if="showNoResults" class="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
      <p class="text-sm text-yellow-800">
        No candidates found matching "{{ searchQuery }}". Please check the number and try again.
      </p>
    </div>
  </div>
</template>

<script setup lang="ts">
interface SearchResult {
  id: string
  candidateNumber: string
  examinationNumber: string
  centerNumber: string
}

interface Props {
  searchQuery: string
  startSequence: number
  totalCandidates: number
  isLoading: boolean
  searchError: string | null
  searchResults: SearchResult[]
  showNoResults: boolean
  searchPlaceholder: string
}

interface Emits {
  (e: 'searchInput', value: string): void
  (e: 'searchEnter'): void
  (e: 'search'): void
  (e: 'clearSearch'): void
  (e: 'sequenceChange', value: number): void
  (e: 'selectResult', result: SearchResult): void
}

defineProps<Props>()
const emit = defineEmits<Emits>()

const onSearchInput = (event: Event) => {
  const target = event.target as HTMLInputElement
  emit('searchInput', target.value)
}

const onSearchEnter = () => emit('searchEnter')
const onSearch = () => emit('search')
const onClearSearch = () => emit('clearSearch')
const onSequenceChange = (event: Event) => {
  const target = event.target as HTMLInputElement
  emit('sequenceChange', parseInt(target.value) || 1)
}
const onSelectResult = (result: SearchResult) => emit('selectResult', result)
</script>

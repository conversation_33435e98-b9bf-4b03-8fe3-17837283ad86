<template>
  <div class="mx-auto max-w-4xl px-4 sm:px-6 lg:px-8 py-6">
    <!-- Enhanced <PERSON><PERSON> with MANEB Branding -->
    <div class="mb-8">
      <nav class="flex mb-5" aria-label="Breadcrumb">
        <ol class="inline-flex items-center space-x-1 text-sm font-medium md:space-x-2">
          <li class="inline-flex items-center">
            <router-link
              to="/admin/dashboard"
              class="inline-flex items-center text-gray-700 hover:text-maneb-primary transition-colors duration-200"
            >
              <svg class="w-5 h-5 mr-2.5" fill="currentColor" viewBox="0 0 20 20">
                <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"/>
              </svg>
              Admin Dashboard
            </router-link>
          </li>
          <li>
            <div class="flex items-center">
              <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
              </svg>
              <router-link
                to="/admin/user-management/users"
                class="ml-1 text-gray-700 hover:text-maneb-primary md:ml-2 transition-colors duration-200"
              >
                User Management
              </router-link>
            </div>
          </li>
          <li>
            <div class="flex items-center">
              <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
              </svg>
              <span class="ml-1 text-gray-400 md:ml-2" aria-current="page">Edit User</span>
            </div>
          </li>
        </ol>
      </nav>
    </div>

    <!-- Enhanced Loading State -->
    <div v-if="isLoading" class="flex flex-col justify-center items-center py-16">
      <div class="animate-spin rounded-full h-16 w-16 border-4 border-gray-200 border-t-maneb-primary"></div>
      <p class="mt-4 text-sm text-gray-600">Loading user information...</p>
    </div>

    <!-- Enhanced Error State -->
    <div v-else-if="loadError" class="bg-red-50 border border-red-200 rounded-lg p-6 mb-6">
      <div class="flex">
        <div class="flex-shrink-0">
          <svg class="h-6 w-6 text-red-400" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
          </svg>
        </div>
        <div class="ml-3 flex-1">
          <h3 class="text-sm font-medium text-red-800">Error loading user</h3>
          <div class="mt-2 text-sm text-red-700">{{ loadError }}</div>
          <div class="mt-4">
            <button
              @click="fetchUserData"
              class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-red-700 bg-red-100 hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors duration-200"
            >
              Try Again
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Enhanced Edit User Form -->
    <div v-else-if="user" class="space-y-8">
      <!-- Enhanced Header -->
      <div class="bg-white shadow-sm rounded-lg border border-gray-200">
        <div class="px-6 py-6">
          <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
            <!-- User Info -->
            <div class="flex items-center space-x-4">
              <div class="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center">
                <span class="text-xl font-bold text-maneb-primary">
                  {{ getUserInitials(user) }}
                </span>
              </div>
              <div>
                <h1 class="text-3xl font-bold text-gray-900">Edit User</h1>
                <p class="mt-1 text-sm text-gray-600">
                  Update information for {{ user.fullName || `${user.firstName} ${user.lastName}` }}
                </p>
                <div class="mt-2 flex items-center space-x-4">
                  <span :class="getStatusBadgeClass(user.status)" class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium">
                    {{ getStatusDisplayText(user.status) }}
                  </span>
                  <span v-if="user.role" class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                    {{ user.role.name }}
                  </span>
                </div>
              </div>
            </div>

            <!-- Action Buttons -->
            <div class="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-3">
              <router-link
                :to="{ name: 'admin.user-management.users.detail', params: { id: user.id } }"
                class="inline-flex items-center justify-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-maneb-primary transition-colors duration-200"
              >
                <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 616 0z" />
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                </svg>
                View Details
              </router-link>
              <router-link
                :to="{ name: 'admin.user-management.users' }"
                class="inline-flex items-center justify-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-maneb-primary transition-colors duration-200"
              >
                <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                </svg>
                Back to Users
              </router-link>
            </div>
          </div>
        </div>
      </div>

      <!-- Enhanced Edit Form -->
      <div class="bg-white shadow-sm rounded-lg border border-gray-200">
        <div class="px-6 py-5 border-b border-gray-200">
          <div class="flex items-center space-x-3">
            <div class="w-10 h-10 bg-blue-50 rounded-lg flex items-center justify-center">
              <svg class="w-6 h-6 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z"/>
              </svg>
            </div>
            <div>
              <h3 class="text-lg font-semibold text-gray-900">Update User Information</h3>
              <p class="mt-1 text-sm text-gray-600">Modify the user details below and save changes.</p>
            </div>
          </div>
        </div>

        <form @submit.prevent="handleSubmit" class="px-6 py-6 space-y-8">
          <!-- Personal Information Section -->
          <div class="space-y-6">
            <div class="border-l-4 border-maneb-primary pl-4">
              <h4 class="text-lg font-semibold text-gray-900">Personal Information</h4>
              <p class="text-sm text-gray-600 mt-1">Update basic personal details</p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <!-- First Name -->
              <div class="space-y-2">
                <label for="firstName" class="block text-sm font-medium text-gray-700">
                  First Name <span class="text-red-500">*</span>
                </label>
                <div class="relative">
                  <input
                    id="firstName"
                    v-model="formData.firstName"
                    type="text"
                    required
                    :class="[
                      'block w-full rounded-lg border px-3 py-2.5 text-sm transition-colors duration-200',
                      'focus:ring-2 focus:ring-maneb-primary focus:border-maneb-primary',
                      errors.firstName
                        ? 'border-red-300 bg-red-50 text-red-900 placeholder-red-300 focus:border-red-500 focus:ring-red-500'
                        : 'border-gray-300 bg-white text-gray-900 placeholder-gray-500'
                    ]"
                    placeholder="Enter first name"
                  />
                  <div v-if="errors.firstName" class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                    <svg class="h-5 w-5 text-red-500" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
                    </svg>
                  </div>
                </div>
                <p v-if="errors.firstName" class="text-sm text-red-600">{{ errors.firstName }}</p>
              </div>

              <!-- Last Name -->
              <div class="space-y-2">
                <label for="lastName" class="block text-sm font-medium text-gray-700">
                  Last Name <span class="text-red-500">*</span>
                </label>
                <div class="relative">
                  <input
                    id="lastName"
                    v-model="formData.lastName"
                    type="text"
                    required
                    :class="[
                      'block w-full rounded-lg border px-3 py-2.5 text-sm transition-colors duration-200',
                      'focus:ring-2 focus:ring-maneb-primary focus:border-maneb-primary',
                      errors.lastName
                        ? 'border-red-300 bg-red-50 text-red-900 placeholder-red-300 focus:border-red-500 focus:ring-red-500'
                        : 'border-gray-300 bg-white text-gray-900 placeholder-gray-500'
                    ]"
                    placeholder="Enter last name"
                  />
                  <div v-if="errors.lastName" class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                    <svg class="h-5 w-5 text-red-500" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
                    </svg>
                  </div>
                </div>
                <p v-if="errors.lastName" class="text-sm text-red-600">{{ errors.lastName }}</p>
              </div>

              <!-- Gender -->
              <div>
                <label for="gender" class="block text-sm font-medium text-gray-700">Gender</label>
                <select
                  id="gender"
                  v-model="formData.gender"
                  class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-maneb-primary focus:ring-maneb-primary sm:text-sm"
                >
                  <option value="">Select gender</option>
                  <option value="Male">Male</option>
                  <option value="Female">Female</option>
                  <option value="Other">Other</option>
                </select>
              </div>

              <!-- Date of Birth -->
              <div>
                <label for="dateOfBirth" class="block text-sm font-medium text-gray-700">Date of Birth</label>
                <input
                  id="dateOfBirth"
                  v-model="formData.dateOfBirth"
                  type="date"
                  class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-maneb-primary focus:ring-maneb-primary sm:text-sm"
                />
              </div>

              <!-- ID Type -->
              <div>
                <label for="idType" class="block text-sm font-medium text-gray-700">ID Type</label>
                <select
                  id="idType"
                  v-model="formData.idType"
                  class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-maneb-primary focus:ring-maneb-primary sm:text-sm"
                >
                  <option value="">Select ID type</option>
                  <option value="National ID">National ID</option>
                  <option value="Passport">Passport</option>
                  <option value="Driver's License">Driver's License</option>
                  <option value="Other">Other</option>
                </select>
              </div>

              <!-- ID Number -->
              <div>
                <label for="idNumber" class="block text-sm font-medium text-gray-700">ID Number</label>
                <input
                  id="idNumber"
                  v-model="formData.idNumber"
                  type="text"
                  class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-maneb-primary focus:ring-maneb-primary sm:text-sm"
                  placeholder="Enter ID number"
                />
              </div>
            </div>
          </div>

          <!-- Account Information Section -->
          <div>
            <h4 class="text-md font-medium text-gray-900 mb-4">Account Information</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <!-- Email -->
              <div>
                <label for="email" class="block text-sm font-medium text-gray-700">
                  Email Address <span class="text-red-500">*</span>
                </label>
                <input
                  id="email"
                  v-model="formData.email"
                  type="email"
                  required
                  class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-maneb-primary focus:ring-maneb-primary sm:text-sm"
                  placeholder="Enter email address"
                />
                <p v-if="errors.email" class="mt-1 text-sm text-red-600">{{ errors.email }}</p>
              </div>

              <!-- Username -->
              <div>
                <label for="userName" class="block text-sm font-medium text-gray-700">
                  Username <span class="text-red-500">*</span>
                </label>
                <input
                  id="userName"
                  v-model="formData.userName"
                  type="text"
                  required
                  class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-maneb-primary focus:ring-maneb-primary sm:text-sm"
                  placeholder="Enter username"
                />
                <p v-if="errors.userName" class="mt-1 text-sm text-red-600">{{ errors.userName }}</p>
              </div>

              <!-- Current Roles Display -->
              <div class="md:col-span-2">
                <label class="block text-sm font-medium text-gray-700">Current Roles</label>
                <div class="mt-1 flex items-center justify-between p-3 bg-gray-50 rounded-md">
                  <div class="flex-1">
                    <template v-if="user.roles && user.roles.length > 0">
                      <div class="flex flex-wrap gap-2">
                        <span v-for="role in user.roles" :key="role.id" class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-blue-100 text-blue-800">
                          {{ role.name }}
                        </span>
                      </div>
                    </template>
                    <span v-else class="text-sm text-gray-900">No roles assigned</span>
                  </div>
                  <router-link
                    :to="{ name: 'admin.user-management.users.roles', params: { id: user.id } }"
                    class="text-sm text-maneb-primary hover:text-maneb-primary-dark font-medium ml-3"
                  >
                    Manage Roles →
                  </router-link>
                </div>
                <p class="mt-1 text-sm text-gray-500">Use the "Manage Roles" link to assign or change user roles.</p>
              </div>
            </div>
          </div>

          <!-- Error Display -->
          <div v-if="submitError" class="bg-red-50 border border-red-200 rounded-lg p-4">
            <div class="flex">
              <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                </svg>
              </div>
              <div class="ml-3">
                <h3 class="text-sm font-medium text-red-800">Error updating user</h3>
                <div class="mt-2 text-sm text-red-700">{{ submitError }}</div>
              </div>
            </div>
          </div>

          <!-- Form Actions -->
          <div class="flex justify-end space-x-3 pt-6 border-t border-gray-200">
            <router-link 
              :to="{ name: 'admin.user-management.users.detail', params: { id: user.id } }"
              class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-maneb-primary"
            >
              Cancel
            </router-link>
            <button
              type="submit"
              :disabled="isSubmitting"
              class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-maneb-primary hover:bg-maneb-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-maneb-primary disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <svg v-if="isSubmitting" class="animate-spin -ml-1 mr-3 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              {{ isSubmitting ? 'Updating User...' : 'Update User' }}
            </button>
          </div>
        </form>
      </div>
    </div>

    <!-- User Not Found -->
    <div v-else class="text-center py-12">
      <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
      </svg>
      <h3 class="mt-2 text-sm font-medium text-gray-900">User not found</h3>
      <p class="mt-1 text-sm text-gray-500">The requested user could not be found.</p>
      <div class="mt-6">
        <router-link 
          :to="{ name: 'admin.user-management.users' }"
          class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-maneb-primary hover:bg-maneb-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-maneb-primary"
        >
          Back to Users
        </router-link>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useUserStore } from '@/stores'
import type { UserDto, UpdateUserRequest, RecordStatus } from '@/interfaces'
import sweetAlert from '@/utils/ui/sweetAlert'

// Router
const route = useRoute()
const router = useRouter()

// Store
const userStore = useUserStore()

// Reactive state
const user = ref<UserDto | null>(null)
const isLoading = ref(false)
const loadError = ref<string | null>(null)
const isSubmitting = ref(false)
const submitError = ref<string | null>(null)

const formData = reactive<UpdateUserRequest & {
  dateOfBirth?: string
}>({
  firstName: '',
  lastName: '',
  email: '',
  userName: '',
  gender: undefined,
  dateOfBirth: undefined,
  idType: '',
  idNumber: ''
})

const errors = reactive<Record<string, string>>({})

// Computed
const userId = computed(() => route.params.id as string)

const userEditBreadcrumbs = computed(() => [
  { name: 'Admin', href: '/admin', current: false },
  { name: 'User Management', href: '/admin/user-management', current: false },
  { name: 'Users', href: '/admin/user-management/users', current: false },
  { name: user.value?.fullName || `${user.value?.firstName} ${user.value?.lastName}` || 'Edit User', href: '', current: true },
])

// Methods
const validateForm = (): boolean => {
  // Clear previous errors
  Object.keys(errors).forEach(key => delete errors[key])

  let isValid = true

  // Required field validation
  if (!formData.firstName?.trim()) {
    errors.firstName = 'First name is required'
    isValid = false
  }

  if (!formData.lastName?.trim()) {
    errors.lastName = 'Last name is required'
    isValid = false
  }

  if (!formData.email?.trim()) {
    errors.email = 'Email address is required'
    isValid = false
  } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
    errors.email = 'Please enter a valid email address'
    isValid = false
  }

  if (!formData.userName?.trim()) {
    errors.userName = 'Username is required'
    isValid = false
  } else if (formData.userName.length < 3) {
    errors.userName = 'Username must be at least 3 characters long'
    isValid = false
  }

  return isValid
}

const populateForm = (userData: UserDto) => {
  formData.firstName = userData.firstName || ''
  formData.lastName = userData.lastName || ''
  formData.email = userData.email || ''
  formData.userName = userData.userName || ''
  formData.gender = userData.gender || undefined
  formData.dateOfBirth = userData.dateOfBirth ?
    new Date(userData.dateOfBirth).toISOString().split('T')[0] : undefined
  formData.idType = userData.idType || ''
  formData.idNumber = userData.idNumber || ''
}

const handleSubmit = async () => {
  if (!validateForm() || !user.value?.id) {
    return
  }

  try {
    isSubmitting.value = true
    submitError.value = null

    // Prepare data for submission
    const submitData: UpdateUserRequest = {
      firstName: formData.firstName?.trim(),
      lastName: formData.lastName?.trim(),
      email: formData.email?.trim(),
      userName: formData.userName?.trim(),
      gender: formData.gender || undefined,
      dateOfBirth: formData.dateOfBirth ? new Date(formData.dateOfBirth) : undefined,
      idType: formData.idType || undefined,
      idNumber: formData.idNumber || undefined,
    }

    // Update the user
    await userStore.updateUser(user.value.id, submitData)

    // Update local user data
    user.value = { ...user.value, ...submitData }

    // Show success message
    await sweetAlert.success('Success', 'User updated successfully!')

    // Navigate to user detail page
    router.push({
      name: 'admin.user-management.users.detail',
      params: { id: user.value.id }
    })

  } catch (error: any) {
    submitError.value = error.message || 'Failed to update user'
    console.error('Error updating user:', error)
  } finally {
    isSubmitting.value = false
  }
}

const getUserInitials = (user: UserDto): string => {
  const first = user.firstName?.charAt(0) || ''
  const last = user.lastName?.charAt(0) || ''
  return (first + last).toUpperCase()
}

const getStatusBadgeClass = (status?: RecordStatus): string => {
  switch (status) {
    case 'Approved':
      return 'bg-green-100 text-green-800'
    case 'SecondApproved':
      return 'bg-blue-100 text-blue-800'
    case 'Unapproved':
      return 'bg-yellow-100 text-yellow-800'
    case 'Rejected':
      return 'bg-red-100 text-red-800'
    default:
      return 'bg-gray-100 text-gray-800'
  }
}

const getStatusDisplayText = (status?: RecordStatus): string => {
  switch (status) {
    case 'Approved':
      return 'Approved'
    case 'SecondApproved':
      return 'Second Approved'
    case 'Unapproved':
      return 'Pending Approval'
    case 'Rejected':
      return 'Rejected'
    default:
      return 'Unknown'
  }
}

const fetchUserDetails = async () => {
  if (!userId.value) {
    loadError.value = 'No user ID provided'
    return
  }

  try {
    isLoading.value = true
    loadError.value = null
    user.value = await userStore.fetchUserById(userId.value)
    populateForm(user.value)
  } catch (err: any) {
    loadError.value = err.message || 'Failed to load user details'
    console.error('Error fetching user details:', err)
  } finally {
    isLoading.value = false
  }
}

const fetchUserData = fetchUserDetails

// Lifecycle
onMounted(() => {
  fetchUserDetails()
})

// Watch for route changes
watch(() => route.params.id, (newId) => {
  if (newId && newId !== userId.value) {
    fetchUserDetails()
  }
})
</script>

<style scoped>
/* MANEB Theme Colors */
.bg-maneb-primary {
  background-color: #a12c2c;
}

.text-maneb-primary {
  color: #a12c2c;
}

.bg-maneb-primary-dark {
  background-color: #8b2424;
}

.border-maneb-primary {
  border-color: #a12c2c;
}

.focus\:ring-maneb-primary:focus {
  --tw-ring-color: #a12c2c;
}

.focus\:border-maneb-primary:focus {
  border-color: #a12c2c;
}

.hover\:bg-maneb-primary-dark:hover {
  background-color: #8b2424;
}

.hover\:text-maneb-primary-dark:hover {
  color: #8b2424;
}
</style>

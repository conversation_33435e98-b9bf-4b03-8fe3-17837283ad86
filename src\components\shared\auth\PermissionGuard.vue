<!--
  @fileoverview Permission Guard Component for MANEB RBAC
  @description Conditionally renders content based on user permissions
-->

<template>
  <div v-if="hasAccess">
    <slot />
  </div>
  <div v-else-if="showFallback">
    <slot name="fallback">
      <div class="text-center py-8 text-gray-500">
        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
        </svg>
        <h3 class="mt-2 text-sm font-medium text-gray-900">Access Restricted</h3>
        <p class="mt-1 text-sm text-gray-500">You don't have permission to access this content.</p>
      </div>
    </slot>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
// @ts-ignore
import { usePermissions } from '@/composables/usePermissions'
// @ts-ignore
import { PermissionModule, PermissionAction } from '@/utils/permissions'

interface Props {
  /** Module to check access for */
  module?: PermissionModule
  /** Specific action to check */
  action?: PermissionAction
  /** Multiple actions - user needs ANY of these */
  anyActions?: PermissionAction[]
  /** Multiple actions - user needs ALL of these */
  allActions?: PermissionAction[]
  /** Role name to check directly */
  role?: string
  /** Show fallback content when access is denied */
  showFallback?: boolean
  /** Invert the permission check */
  not?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  showFallback: false,
  not: false
})

const { can, canAccess, canAny, canAll, currentRole } = usePermissions()

/**
 * Determine if user has access based on props
 */
const hasAccess = computed(() => {
  let access = false

  // Direct role check
  if (props.role) {
    access = currentRole.value.toLowerCase() === props.role.toLowerCase()
  }
  // Module + specific action check
  else if (props.module && props.action) {
    // Use the enhanced permission checking from usePermissions
    access = can(props.module, props.action).value
  }
  // Module + any actions check
  else if (props.module && props.anyActions && props.anyActions.length > 0) {
    access = canAny(props.module, props.anyActions).value
  }
  // Module + all actions check
  else if (props.module && props.allActions && props.allActions.length > 0) {
    access = canAll(props.module, props.allActions).value
  }
  // Module access only
  else if (props.module) {
    // Use the enhanced module access checking from usePermissions
    access = canAccess(props.module).value
  }

  // Invert if requested
  return props.not ? !access : access
})
</script>

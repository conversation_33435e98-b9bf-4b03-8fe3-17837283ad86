<template>
  <!-- Flowbite Pro Modal Base -->
  <div
    v-if="isOpen"
    :id="modalId"
    tabindex="-1"
    aria-hidden="true"
    class="fixed inset-0 z-50 flex items-center justify-center p-4 bg-gray-900 bg-opacity-50"
    @click.self="handleBackdropClick"
  >
    <div 
      class="relative w-full max-h-full"
      :class="modalSizeClasses"
    >
      <!-- Modal content -->
      <div class="relative bg-white rounded-lg shadow-xl border border-gray-200">
        <!-- Modal header -->
        <div class="flex items-start justify-between p-4 border-b rounded-t border-gray-200">
          <div class="flex items-center space-x-3">
            <!-- Icon (optional) -->
            <div 
              v-if="showIcon"
              class="w-12 h-12 rounded-lg flex items-center justify-center"
              :class="iconClasses"
            >
              <slot name="icon">
                <svg class="w-6 h-6" :class="iconColorClasses" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" :d="defaultIconPath" />
                </svg>
              </slot>
            </div>
            
            <!-- Title and description -->
            <div>
              <h3 class="text-xl font-semibold text-gray-900">
                <slot name="title">{{ title }}</slot>
              </h3>
              <p v-if="description || $slots.description" class="text-sm text-gray-600">
                <slot name="description">{{ description }}</slot>
              </p>
            </div>
          </div>
          
          <!-- Close button -->
          <button 
            @click="handleClose"
            type="button" 
            class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ml-auto inline-flex justify-center items-center transition-colors duration-200"
            :disabled="isLoading"
          >
            <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
              <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"/>
            </svg>
            <span class="sr-only">Close modal</span>
          </button>
        </div>
        
        <!-- Modal body -->
        <div class="p-6">
          <slot name="body">
            <slot></slot>
          </slot>
        </div>
        
        <!-- Modal footer (optional) -->
        <div 
          v-if="$slots.footer || showDefaultFooter"
          class="flex items-center justify-end p-4 border-t border-gray-200 space-x-3"
        >
          <slot name="footer">
            <button
              v-if="showCancelButton"
              @click="handleClose"
              type="button"
              class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:ring-4 focus:ring-gray-200 transition-colors duration-200"
              :disabled="isLoading"
            >
              {{ cancelButtonText }}
            </button>
            <button
              v-if="showConfirmButton"
              @click="handleConfirm"
              type="button"
              class="px-4 py-2 text-sm font-medium text-white rounded-lg focus:ring-4 focus:ring-blue-300 transition-colors duration-200"
              :class="confirmButtonClasses"
              :disabled="isLoading || confirmDisabled"
            >
              <svg v-if="isLoading" class="w-4 h-4 mr-2 animate-spin" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              {{ isLoading ? loadingText : confirmButtonText }}
            </button>
          </slot>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, watch } from 'vue';

// Define component name for better debugging
defineOptions({
  name: 'BaseModal'
});

/**
 * Modal size options
 */
export type ModalSize = 'sm' | 'md' | 'lg' | 'xl' | '2xl' | '3xl' | '4xl' | '5xl' | '6xl' | 'full';

/**
 * Modal variant for styling
 */
export type ModalVariant = 'default' | 'danger' | 'warning' | 'success' | 'info';

/**
 * Props interface
 */
interface Props {
  /** Whether the modal is open */
  isOpen: boolean;
  /** Modal title */
  title?: string;
  /** Modal description */
  description?: string;
  /** Modal size */
  size?: ModalSize;
  /** Modal variant for styling */
  variant?: ModalVariant;
  /** Whether to show the icon */
  showIcon?: boolean;
  /** Whether to close on backdrop click */
  closeOnBackdrop?: boolean;
  /** Whether the modal is in loading state */
  isLoading?: boolean;
  /** Whether to show default footer */
  showDefaultFooter?: boolean;
  /** Whether to show cancel button */
  showCancelButton?: boolean;
  /** Whether to show confirm button */
  showConfirmButton?: boolean;
  /** Cancel button text */
  cancelButtonText?: string;
  /** Confirm button text */
  confirmButtonText?: string;
  /** Loading text */
  loadingText?: string;
  /** Whether confirm button is disabled */
  confirmDisabled?: boolean;
  /** Custom modal ID */
  modalId?: string;
}

const props = withDefaults(defineProps<Props>(), {
  isOpen: false,
  title: '',
  description: '',
  size: 'lg',
  variant: 'default',
  showIcon: false,
  closeOnBackdrop: true,
  isLoading: false,
  showDefaultFooter: false,
  showCancelButton: true,
  showConfirmButton: true,
  cancelButtonText: 'Cancel',
  confirmButtonText: 'Confirm',
  loadingText: 'Loading...',
  confirmDisabled: false,
  modalId: 'base-modal'
});

/**
 * Emits
 */
const emit = defineEmits<{
  close: [];
  confirm: [];
}>();

/**
 * Computed properties
 */
const modalSizeClasses = computed(() => {
  const sizeMap: Record<ModalSize, string> = {
    'sm': 'max-w-sm',
    'md': 'max-w-md',
    'lg': 'max-w-lg',
    'xl': 'max-w-xl',
    '2xl': 'max-w-2xl',
    '3xl': 'max-w-3xl',
    '4xl': 'max-w-4xl',
    '5xl': 'max-w-5xl',
    '6xl': 'max-w-6xl',
    'full': 'max-w-full'
  };
  return sizeMap[props.size];
});

const iconClasses = computed(() => {
  const variantMap: Record<ModalVariant, string> = {
    'default': 'bg-gray-50',
    'danger': 'bg-red-50',
    'warning': 'bg-yellow-50',
    'success': 'bg-green-50',
    'info': 'bg-blue-50'
  };
  return variantMap[props.variant];
});

const iconColorClasses = computed(() => {
  const variantMap: Record<ModalVariant, string> = {
    'default': 'text-gray-600',
    'danger': 'text-red-600',
    'warning': 'text-yellow-600',
    'success': 'text-green-600',
    'info': 'text-blue-600'
  };
  return variantMap[props.variant];
});

const confirmButtonClasses = computed(() => {
  const variantMap: Record<ModalVariant, string> = {
    'default': 'bg-blue-600 hover:bg-blue-700',
    'danger': 'bg-red-600 hover:bg-red-700',
    'warning': 'bg-yellow-600 hover:bg-yellow-700',
    'success': 'bg-green-600 hover:bg-green-700',
    'info': 'bg-blue-600 hover:bg-blue-700'
  };
  return variantMap[props.variant];
});

const defaultIconPath = computed(() => {
  const variantMap: Record<ModalVariant, string> = {
    'default': 'M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z',
    'danger': 'M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.464 0L4.35 16.5c-.77.833.192 2.5 1.732 2.5z',
    'warning': 'M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.464 0L4.35 16.5c-.77.833.192 2.5 1.732 2.5z',
    'success': 'M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z',
    'info': 'M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z'
  };
  return variantMap[props.variant];
});

/**
 * Methods
 */
const handleClose = () => {
  if (!props.isLoading) {
    emit('close');
  }
};

const handleConfirm = () => {
  if (!props.isLoading && !props.confirmDisabled) {
    emit('confirm');
  }
};

const handleBackdropClick = () => {
  if (props.closeOnBackdrop) {
    handleClose();
  }
};

/**
 * Watch for modal open/close to handle body scroll
 */
watch(() => props.isOpen, (isOpen) => {
  if (typeof document !== 'undefined') {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'auto';
    }
  }
});
</script>

<style scoped>
/* MANEB Theme Colors */
.text-maneb-primary {
  color: #a12c2c;
}

.bg-maneb-primary {
  background-color: #a12c2c;
}

.border-maneb-primary {
  border-color: #a12c2c;
}
</style>

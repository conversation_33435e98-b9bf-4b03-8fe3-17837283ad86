import './assets/styles/base.css'

import { createApp } from 'vue'
import { createPinia } from 'pinia'
import { initFlowbite } from 'flowbite'

import App from './App.vue'
import router from './router'
import { useAuthStore } from '@/stores/auth/auth.store'
import apiClient from '@/services/core/api-client'

const app = createApp(App)
const pinia = createPinia()

app.use(pinia)
app.use(router)

const authStore = useAuthStore()
authStore.initializeAuth()

if (authStore.token) {
  apiClient.setToken(authStore.token)
}

app.mount('#app')

initFlowbite()


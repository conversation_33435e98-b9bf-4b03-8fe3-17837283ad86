/**
 * @fileoverview User table interfaces for MANEB Results Management System
 * @description Standardized interfaces for user management data models
 */

import type { BaseEntity, RecordStatus, Gender, PermissionAction } from '../common/base.interface';

/**
 * User entity - Updated to match API schema exactly
 * @description Core user information matching UserDto from API
 */
export interface UserEntity extends BaseEntity {
  /** First name (required) */
  firstName: string;
  /** Last name (required) */
  lastName: string;
  /** Gender - matching API enum values */
  gender?: 'Male' | 'Female' | 'Other';
  /** ID number */
  idNumber?: string;
  /** ID type */
  idType?: string;
  /** Date of birth */
  dateOfBirth?: Date | string;
  /** Email address */
  email?: string;
  /** Username */
  userName?: string;
  /** Full name (computed field - read-only) */
  fullName?: string;
  /** Record status */
  status?: RecordStatus;
}

/**
 * User DTO for API responses - Updated to match API schema exactly
 * @description Data transfer object for user data with populated references
 */
export interface UserDto extends UserEntity {
  /** User's assigned roles array - matching API schema */
  roles?: RoleDto[];
}

/**
 * Role entity
 * @description Role definition for access control
 */
export interface RoleEntity extends BaseEntity {
  /** Role name */
  name: string;
  /** Role description */
  description?: string;
  /** Record status */
  status?: RecordStatus;
  /** Whether the role is active */
  isActive?: boolean;
}

/**
 * Role DTO for API responses
 * @description Data transfer object for role data with populated references
 */
export interface RoleDto extends RoleEntity {
  /** Role permissions (populated) */
  rolePermissions?: RolePermissionDto[];
  /** Legacy permissions property for backward compatibility */
  permissions?: RolePermissionDto[];
}

/**
 * Role filter DTO
 * @description Filter criteria for role queries
 */
export interface RoleFilterDto {
  /** Search query */
  searchQuery?: string;
  /** Status filter */
  status?: RecordStatus | 'All';
  /** Has permissions filter */
  hasPermissions?: boolean | 'All';
  /** Active status filter */
  isActive?: boolean | 'All';
}

/**
 * Role permission entity (Junction table)
 * @description Permissions assigned to roles
 */
export interface RolePermissionEntity extends BaseEntity {
  /** Section ID reference (required) */
  sectionID: string;
  /** Role ID reference (required) */
  roleId: string;
  /** Permission action */
  action: PermissionAction;
  /** Whether access is granted */
  canAccess: boolean;
  /** Record status */
  status?: RecordStatus;
}

/**
 * Role permission DTO for API responses
 * @description Data transfer object for role permission data with populated references
 */
export interface RolePermissionDto extends RolePermissionEntity {
  /** Section information (populated, can be null) */
  section?: SectionDto | null;
}

/**
 * Section entity (Organizational structure) - Updated to match API schema
 * @description System sections for permission management
 */
export interface SectionEntity extends BaseEntity {
  /** Section name */
  name: string;
  /** API controller name */
  controller?: string;
  /** API area name */
  area?: string;
  /** Section description */
  description?: string;
  /** Section code */
  code?: string;
  /** Parent section ID for hierarchy */
  parentSectionId?: string;
  /** Hierarchy level */
  level?: number;
  /** Whether section is active */
  isActive?: boolean;
  /** Number of users in this section */
  userCount?: number;
  /** Record status */
  status?: RecordStatus;
}

/**
 * Section DTO for API responses - Updated to match API schema
 * @description Data transfer object for section data with hierarchical structure
 */
export interface SectionDto extends SectionEntity {
  /** Child sections for hierarchy */
  childSections?: SectionDto[];
}

/**
 * Section filter DTO
 * @description Filter criteria for section queries
 */
export interface SectionFilterDto {
  /** Search query */
  searchQuery?: string;
  /** Controller filter */
  controller?: string | 'All';
  /** Area filter */
  area?: string | 'All';
  /** Status filter */
  status?: RecordStatus | 'All';
  /** Parent section ID filter */
  parentSectionId?: string | 'All';
  /** Level filter */
  level?: number | 'All';
  /** Active status filter */
  isActive?: boolean | 'All';
  /** Has users filter */
  hasUsers?: boolean | 'All';
}

/**
 * Workspace entity
 * @description Workspace for organizing users and projects
 */
export interface WorkspaceEntity extends BaseEntity {
  /** Workspace name */
  name: string;
  /** Description */
  description?: string;
  /** Owner user ID */
  ownerId: string;
  /** Whether workspace is active */
  isActive: boolean;
  /** Member count */
  memberCount?: number;
  /** Workspace settings */
  settings?: WorkspaceSettings;
  /** Record status */
  status?: RecordStatus;
}

/**
 * Workspace DTO for API responses
 * @description Data transfer object for workspace data with populated references
 */
export interface WorkspaceDto extends WorkspaceEntity {
  /** Owner information (populated) */
  owner?: UserDto;
}

/**
 * Workspace settings
 * @description Configuration options for workspaces
 */
export interface WorkspaceSettings {
  /** Allow public access */
  allowPublicAccess?: boolean;
  /** Require approval for joining */
  requireApprovalForJoin?: boolean;
  /** Maximum number of members */
  maxMembers?: number;
  /** Default role for new members */
  defaultRole?: string;
}

/**
 * Audit log entity
 * @description System audit trail
 */
export interface AuditEntity extends BaseEntity {
  /** User who performed the action */
  userId: string;
  /** Action performed */
  action: string;
  /** Type of entity affected */
  entityType: string;
  /** ID of affected entity */
  entityId?: string;
  /** Name/description of affected entity */
  entityName?: string;
  /** Detailed description */
  description: string;
  /** Action status */
  status: string;
  /** IP address */
  ipAddress?: string;
  /** User agent */
  userAgent?: string;
  /** Session ID */
  sessionId?: string;
  /** Additional context data */
  additionalData?: Record<string, any>;
}

/**
 * Audit DTO for API responses
 * @description Data transfer object for audit data with populated references
 */
export interface AuditDto extends AuditEntity {
  /** User information (populated) */
  user?: UserDto;
}

/**
 * User filter options
 * @description Filtering options for user queries
 */
export interface UserFilter {
  /** Search query */
  searchQuery?: string;
  /** Filter by role ID */
  roleId?: string | 'All';
  /** Filter by status */
  status?: RecordStatus | 'All';
  /** Filter by gender */
  gender?: Gender | 'All';
  /** Filter by active status */
  isActive?: boolean | 'All';
}

/**
 * Role filter options
 * @description Filtering options for role queries
 */
export interface RoleFilter {
  /** Search query */
  searchQuery?: string;
  /** Filter by status */
  status?: RecordStatus | 'All';
  /** Filter by whether role has permissions */
  hasPermissions?: boolean | 'All';
  /** Filter by active status */
  isActive?: boolean | 'All';
}

/**
 * Legacy role filter DTO for backward compatibility
 * @description Legacy filtering options for role queries
 */
export interface RoleFilterDto extends RoleFilter {}

/**
 * Role permission filter options
 * @description Filtering options for role permission queries
 */
export interface RolePermissionFilter {
  /** Search query */
  searchQuery?: string;
  /** Filter by role ID */
  roleId?: string | 'All';
  /** Filter by section ID */
  sectionId?: string | 'All';
  /** Filter by action */
  action?: PermissionAction | 'All';
  /** Filter by access granted */
  canAccess?: boolean | 'All';
  /** Filter by status */
  status?: RecordStatus | 'All';
}

/**
 * Section filter options
 * @description Filtering options for section queries
 */
export interface SectionFilter {
  /** Search query */
  searchQuery?: string;
  /** Filter by controller */
  controller?: string | 'All';
  /** Filter by area */
  area?: string | 'All';
  /** Filter by status */
  status?: RecordStatus | 'All';
}

/**
 * Workspace filter options
 * @description Filtering options for workspace queries
 */
export interface WorkspaceFilter {
  /** Search query */
  searchQuery?: string;
  /** Filter by owner ID */
  ownerId?: string | 'All';
  /** Filter by active status */
  isActive?: boolean | 'All';
  /** Filter by status */
  status?: RecordStatus | 'All';
  /** Filter by member count */
  memberCount?: 'None' | 'Some' | 'Many' | 'All';
}

/**
 * Audit filter options
 * @description Filtering options for audit queries
 */
export interface AuditFilter {
  /** Search query */
  searchQuery?: string;
  /** Filter by user ID */
  userId?: string | 'All';
  /** Filter by action */
  action?: string | 'All';
  /** Filter by entity type */
  entityType?: string | 'All';
  /** Filter by status */
  status?: string | 'All';
  /** Date range filter */
  dateFrom?: Date;
  /** Date range filter */
  dateTo?: Date;
  /** Filter by IP address */
  ipAddress?: string;
}

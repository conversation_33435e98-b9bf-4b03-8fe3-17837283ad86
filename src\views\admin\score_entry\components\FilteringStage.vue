<template>
  <div class="bg-white rounded-lg shadow-sm p-8">
    <div class="mb-8">
      <h2 class="text-2xl font-bold text-gray-900 mb-2">Select Examination Criteria</h2>
      <p class="text-gray-600">Please complete all required filters to proceed to score entry</p>
    </div>

    <!-- Progress Indicator -->
    <div class="mb-8">
      <div class="flex items-center justify-between text-sm text-gray-500 mb-2">
        <span>Progress</span>
        <span>{{ completedFilters }}/{{ totalRequiredFilters }} completed</span>
      </div>
      <div class="w-full bg-gray-200 rounded-full h-2">
        <div
          class="bg-maneb-primary h-2 rounded-full transition-all duration-300"
          :style="{ width: `${(completedFilters / totalRequiredFilters) * 100}%` }"
        ></div>
      </div>
    </div>

    <!-- Unified Filters -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
      <!-- Exam Type Filter (First) - Traditional Dropdown - Hidden when route params provided -->
      <div v-if="!hasRouteParams">
        <label for="examType" class="block text-sm font-medium text-gray-700 mb-2">
          Exam Type <span class="text-red-500">*</span>
        </label>
        <select
          id="examType"
          v-model="filters.examType"
          @change="onExamTypeChange"
          :disabled="isLoading || isLoadingExamTypes"
          class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-maneb-primary focus:border-maneb-primary block w-full p-2.5 disabled:bg-gray-100 disabled:cursor-not-allowed"
        >
          <option value="">Select Exam Type</option>
          <option v-for="examType in examTypes" :key="examType.id" :value="examType.id">
            {{ examType.name }}
          </option>
        </select>
        <div v-if="isLoadingExamTypes" class="mt-1 text-xs text-gray-500 flex items-center">
          <svg class="animate-spin -ml-1 mr-1 h-3 w-3 text-gray-500" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          Loading exam types...
        </div>
      </div>

      <!-- Route Parameters Display (when exam type and year are set via submenu) -->
      <div v-if="hasRouteParams" class="col-span-full">
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h3 class="text-sm font-medium text-blue-900 mb-2">Selected via Navigation:</h3>
          <div class="flex items-center gap-4 text-sm text-blue-800">
            <span><strong>Exam Type:</strong> {{ getExamTypeName(filters.examType) }}</span>
            <span><strong>Year:</strong> {{ filters.examYear }}</span>
          </div>
        </div>
      </div>

      <!-- District Filter (Shown for Primary School exam types, Hidden for others) - Searchable -->
      <SearchableDropdown
        v-if="isPrimarySchoolExamType"
        id="district"
        label="District"
        placeholder="Search districts..."
        required
        :disabled="!filters.examType || isLoading"
        :options="districtOptions"
        :is-loading="isLoadingDistricts"
        v-model="filters.district"
        @change="onDistrictChange"
      />

      <!-- School Filter (Shown for Primary School exam types, Hidden for others) - Searchable -->
      <SearchableDropdown
        v-if="isPrimarySchoolExamType"
        id="school"
        label="School"
        :placeholder="!filters.district ? 'Select a district first...' : 'Search schools...'"
        required
        :disabled="!filters.district || isLoading || isLoadingSchools"
        :options="schoolOptions"
        :is-loading="isLoadingSchools"
        v-model="filters.school"
        @change="onSchoolChange"
      />

      <!-- Center Filter - Searchable with Live Search -->
      <SearchableDropdown
        id="center"
        label="Center"
        placeholder="Search centers by number..."
        required
        :disabled="!filters.examType || isLoading"
        :options="centerOptions"
        :is-loading="isLoadingCenters"
        v-model="filters.center"
        @change="onCenterChange"
        @search="onCenterSearch"
      />

      <!-- Subject Filter -->
      <div>
        <label for="subject" class="block text-sm font-medium text-gray-700 mb-2">
          Subject <span class="text-red-500">*</span>
        </label>
        <SearchableDropdown
          id="subject"
          placeholder="Select Subject"
          required
          :disabled="!filters.center || isLoadingSubjects"
          :options="subjectOptions"
          :is-loading="isLoadingSubjects"
          v-model="filters.subject"
          @change="onSubjectChange"
          @search="onSubjectSearch"
        />
      </div>

      <!-- Paper Filter -->
      <div v-if="filters.subject && availablePapers.length > 0">
        <label for="paper" class="block text-sm font-medium text-gray-700 mb-2">
          Paper <span class="text-red-500">*</span>
        </label>
        <select
          id="paper"
          v-model="filters.paper"
          @change="onPaperChange"
          :disabled="!filters.subject || isLoadingPapers"
          class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-maneb-primary focus:border-maneb-primary block w-full p-2.5 disabled:bg-gray-100 disabled:cursor-not-allowed"
        >
          <option value="">Select Paper</option>
          <option v-for="paper in availablePapers" :key="paper.id" :value="paper.id">
            {{ paper.name }}
          </option>
        </select>
      </div>

      <!-- Exam Year Filter - Hidden when route params provided -->
      <div v-if="!hasRouteParams">
        <label for="examYear" class="block text-sm font-medium text-gray-700 mb-2">
          Exam Year <span class="text-red-500">*</span>
        </label>
        <select
          id="examYear"
          v-model="filters.examYear"
          @change="onExamYearChange"
          class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-maneb-primary focus:border-maneb-primary block w-full p-2.5"
        >
          <option v-for="year in availableYears" :key="year" :value="year">
            {{ year }}
          </option>
        </select>
      </div>
    </div>

    <!-- Filter Summary -->
    <div v-if="completedFilters > 0" class="mb-8 p-4 bg-blue-50 border border-blue-200 rounded-lg">
      <h3 class="text-sm font-medium text-blue-900 mb-2">Current Selection:</h3>
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2 text-sm">
        <div v-if="filters.examType" class="text-blue-800">
          <span class="font-medium">Exam Type:</span> {{ getExamTypeName(filters.examType) }}
        </div>
        <div v-if="filters.district && isPrimarySchoolExamType" class="text-blue-800">
          <span class="font-medium">District:</span> {{ getDistrictName(filters.district) }}
        </div>
        <div v-if="filters.school && isPrimarySchoolExamType" class="text-blue-800">
          <span class="font-medium">School:</span> {{ getSchoolName(filters.school) }}
        </div>
        <div v-if="filters.center" class="text-blue-800">
          <span class="font-medium">Center:</span> {{ getCenterName(filters.center) }}
        </div>
        <div v-if="filters.subject" class="text-blue-800">
          <span class="font-medium">Subject:</span> {{ getSubjectName(filters.subject) }}
        </div>
        <div v-if="filters.paper" class="text-blue-800">
          <span class="font-medium">Paper:</span> {{ getPaperName(filters.paper) }}
        </div>
        <div class="text-blue-800">
          <span class="font-medium">Year:</span> {{ filters.examYear }}
        </div>
      </div>
    </div>

    <!-- Action Buttons -->
    <div class="flex items-center justify-between pt-6 border-t border-gray-200">
      <button
        @click="resetFilters"
        type="button"
        class="text-gray-500 bg-white hover:bg-gray-100 focus:ring-4 focus:outline-none focus:ring-gray-200 rounded-lg border border-gray-200 text-sm font-medium px-5 py-2.5 hover:text-gray-900 focus:z-10"
        :disabled="isLoading || completedFilters === 0"
      >
        Reset Filters
      </button>
      
      <button
        @click="proceedToScoreEntry"
        type="button"
        class="text-white bg-maneb-primary hover:bg-maneb-primary-dark focus:ring-4 focus:outline-none focus:ring-maneb-primary/50 font-medium rounded-lg text-sm px-5 py-2.5 text-center"
        :disabled="!allFiltersSelected || isLoading"
      >
        <span v-if="isLoading" class="flex items-center">
          <svg class="animate-spin -ml-1 mr-3 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          Loading...
        </span>
        <span v-else>Continue to Score Entry</span>
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import SearchableDropdown from '@/components/shared/SearchableDropdown.vue'
import { districtService, examTypeService } from '@/services'
import { useCenterStore, useSchoolStore, useSubjectStore, usePaperStore } from '@/stores'
import sweetAlert from '@/utils/ui/sweetAlert'

// Props
interface Props {
  isLoading?: boolean
  currentFilters?: any
  routeExamType?: string
  routeExamYear?: number
  hasRouteParams?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  isLoading: false,
  currentFilters: null,
  routeExamType: '',
  routeExamYear: new Date().getFullYear(),
  hasRouteParams: false
})

// Emits
const emit = defineEmits<{
  'filters-selected': [filters: any]
}>()

// Stores
const centerStore = useCenterStore()
const schoolStore = useSchoolStore()
const subjectStore = useSubjectStore()
const paperStore = usePaperStore()

// Types
interface FilterOption {
  id: string
  name: string
  parentId?: string
}

// Reactive state
const filters = ref({
  district: '',
  center: '',
  examType: props.routeExamType || '',
  school: '',
  subject: '',
  paper: '',
  examYear: props.routeExamYear || new Date().getFullYear() // Default to current year or route year
})

// API data
const examTypes = ref<any[]>([])
const districts = ref<any[]>([])
const schools = ref<any[]>([])
const centers = ref<any[]>([])
const availableSubjects = ref<any[]>([])
const availablePapers = ref<any[]>([])

// Loading states
const isLoadingExamTypes = ref(false)
const isLoadingDistricts = ref(false)
const isLoadingSchools = ref(false)
const isLoadingCenters = ref(false)
const isLoadingSubjects = ref(false)
const isLoadingPapers = ref(false)

// Center search state
const centerSearchQuery = ref('')
const centerSearchTimeout = ref<any>(null)

// Available years for the dropdown (current year and previous 5 years)
const availableYears = computed(() => {
  const currentYear = new Date().getFullYear()
  const years = []
  for (let i = 0; i <= 5; i++) {
    years.push(currentYear - i)
  }
  return years
})

// Subject search state
const subjectSearchQuery = ref('')

// School caching for lazy loading
const schoolsCache = ref<Map<string, any[]>>(new Map())
const schoolsLoaded = ref(false)

// Computed options for searchable dropdowns
const districtOptions = computed(() => {
  return districts.value.map(district => ({
    id: district.id,
    name: district.name,
    description: district.emisdistrictCode || ''
  }))
})

const schoolOptions = computed(() => {
  // Return empty array if no district is selected or schools not loaded for this district
  if (!filters.value.district || !schoolsLoaded.value) {
    console.log('🔍 schoolOptions: returning empty array', {
      hasDistrict: !!filters.value.district,
      schoolsLoaded: schoolsLoaded.value,
      schoolsCount: schools.value.length
    })
    return []
  }

  const filteredSchools = schools.value
    .filter(school => {
      // Filter by examDistrictPartitionId matching selected district
      // Only show schools that have examDistrictPartitionId populated
      return school.examDistrictPartitionId &&
             school.examDistrictPartitionId === filters.value.district
    })
    .map(school => ({
      id: school.id,
      name: school.fullname || school.shortName,
      description: school.shortName || ''
    }))

  console.log('🔍 schoolOptions: computed', {
    district: filters.value.district,
    totalSchools: schools.value.length,
    filteredSchools: filteredSchools.length,
    schoolsLoaded: schoolsLoaded.value
  })

  return filteredSchools
})

const centerOptions = computed(() => {
  // For all exam types, filter by exam type first
  if (!filters.value.examType) return []

  let filteredCenters = centers.value.filter(center => center.examTypeId === filters.value.examType)

  // For Primary School exam types, be more flexible with school filtering
  if (isPrimarySchoolExamType.value && filters.value.school) {
    // Try to filter by school, but if no results, show all centers for the exam type
    const schoolFilteredCenters = filteredCenters.filter(center => center.schoolId === filters.value.school)

    if (schoolFilteredCenters.length > 0) {
      filteredCenters = schoolFilteredCenters
    } else {
      console.log('⚠️ No centers found for selected school, showing all centers for exam type')
      // Keep all centers for the exam type if school filtering returns no results
    }
  }

  console.log('🔍 Center options computed:', {
    examType: filters.value.examType,
    school: filters.value.school,
    isPrimarySchool: isPrimarySchoolExamType.value,
    totalCenters: centers.value.length,
    filteredCenters: filteredCenters.length
  })

  return filteredCenters.map(center => {
    if (isPrimarySchoolExamType.value) {
      // For Primary School exam types, show format: {CENTER_NUMBER} | {SCHOOL_NAME}
      const school = schools.value.find(s => s.id === center.schoolId)
      const schoolName = school ? (school.fullname || school.shortName || 'Unknown School') : 'Unknown School'
      return {
        id: center.id,
        name: `${center.manebcentreNo} | ${schoolName}`,
        description: center.name || ''
      }
    } else {
      // For other exam types, show standard format
      return {
        id: center.id,
        name: `Center ${center.manebcentreNo}`,
        description: center.name || ''
      }
    }
  })
})

const subjectOptions = computed(() => {
  if (!subjectSearchQuery.value) {
    return availableSubjects.value.map(subject => ({
      id: subject.id,
      name: subject.name,
      parentId: subject.examTypeId
    }))
  }

  // Filter subjects based on search query
  const query = subjectSearchQuery.value.toLowerCase()
  return availableSubjects.value
    .filter(subject =>
      subject.name.toLowerCase().includes(query) ||
      subject.id.toLowerCase().includes(query) ||
      (subject.shortName && subject.shortName.toLowerCase().includes(query))
    )
    .map(subject => ({
      id: subject.id,
      name: subject.name,
      parentId: subject.examTypeId
    }))
})

// API Methods
const loadExamTypes = async () => {
  isLoadingExamTypes.value = true
  try {
    const data = await examTypeService.getActiveExamTypes()
    examTypes.value = data
    console.log('✅ Exam types loaded:', data.length, 'items')
  } catch (error) {
    console.error('❌ Error loading exam types:', error)
    sweetAlert.error('Error', 'Failed to load exam types')
  } finally {
    isLoadingExamTypes.value = false
  }
}

const loadDistricts = async () => {
  isLoadingDistricts.value = true
  try {
    // Set page size to 40 items per request for districts
    const data = await districtService.fetchDistrictsWithPagination(40, 1)
    districts.value = data
    console.log('✅ Districts loaded with pagination:', data.length, 'items (page size: 40)')
  } catch (error) {
    console.error('Error loading districts:', error)
    sweetAlert.error('Error', 'Failed to load districts')
  } finally {
    isLoadingDistricts.value = false
  }
}

// Lazy loading for schools based on district selection
const loadSchoolsForDistrict = async (districtId: string) => {
  if (!districtId) {
    schools.value = []
    schoolsLoaded.value = false
    return
  }

  // Check cache first
  if (schoolsCache.value.has(districtId)) {
    schools.value = schoolsCache.value.get(districtId) || []
    schoolsLoaded.value = true
    console.log(`✅ Schools loaded from cache for district ${districtId}:`, schools.value.length, 'items')
    return
  }

  isLoadingSchools.value = true
  schoolsLoaded.value = false

  try {
    // Get district name for search parameter
    const districtName = getDistrictNameById(districtId)

    console.log(`🔍 Loading schools for district: ${districtName} (ID: ${districtId})`)

    // Use the store to load schools for the district
    const filteredSchools = await schoolStore.loadSchoolsForDistrict(districtId)

    // Cache the results
    schoolsCache.value.set(districtId, filteredSchools)
    schools.value = filteredSchools
    schoolsLoaded.value = true

    console.log(`✅ Schools loaded for district ${districtName}:`, filteredSchools.length, 'items')

    if (filteredSchools.length === 0) {
      console.warn(`⚠️ No schools found for district ${districtName}`)
    }

  } catch (error) {
    console.error('Error loading schools for district:', error)
    sweetAlert.error('Error', 'Failed to load schools for selected district')
    schools.value = []
    schoolsLoaded.value = false
  } finally {
    isLoadingSchools.value = false
  }
}

// Helper function to get district name by ID (for lazy loading)
const getDistrictNameById = (districtId: string): string => {
  const district = districts.value.find(d => d.id === districtId)
  return district?.name || ''
}

// Clear school cache (useful for refreshing data)
const clearSchoolCache = () => {
  schoolsCache.value.clear()
  schools.value = []
  schoolsLoaded.value = false
  console.log('🧹 School cache cleared')
}

const loadCenters = async () => {
  isLoadingCenters.value = true
  try {
    // Use larger page size to get more centers for filtering
    const data = await centerStore.fetchCenters({
      pageSize: 200 // Increase page size to get more centers
    })
    centers.value = data

    // Sync with store state
    centerStore.centers = data
    console.log('✅ Centers loaded:', data.length, 'items')
  } catch (error) {
    console.error('Error loading centers:', error)
    sweetAlert.error('Error', 'Failed to load centers')
  } finally {
    isLoadingCenters.value = false
  }
}

// Load subjects based on selected exam type
const loadSubjects = async () => {
  if (!filters.value.examType) {
    availableSubjects.value = []
    return
  }

  isLoadingSubjects.value = true
  try {
    // Get the exam type name to match with paper's examLevel
    const selectedExamType = examTypes.value.find(et => et.id === filters.value.examType)

    // Try multiple strategies to match exam level
    let examLevel = selectedExamType?.id || filters.value.examType // Try ID first
    if (selectedExamType?.name) {
      examLevel = selectedExamType.name // Then try name
    }

    console.log('🔍 Loading subjects from papers for exam level:', filters.value.examType)
    console.log('📋 Selected exam type object:', selectedExamType)
    console.log('📋 Available exam types:', examTypes.value)
    console.log('📋 Current filter exam type:', filters.value.examType)

    // Fetch papers from the API and extract unique subjects
    const papers = await paperStore.fetchPapers({ examTypeId: filters.value.examType })

    console.log('📥 Raw papers from API:', papers)

    if (!Array.isArray(papers)) {
      console.error('Papers is not an array:', papers)
      availableSubjects.value = []
      return
    }

    // Debug: Log all unique exam levels in papers
    const allExamLevels = [...new Set(papers.map(paper => paper.examLevel))]
    console.log('🔍 All exam levels found in papers:', allExamLevels)
    console.log('🔍 Looking for exam level:', examLevel)

    // Filter papers by exam level and extract unique subjects
    let filteredPapers = papers.filter(paper => paper.examLevel === filters.value.examType)

    // If no exact match, try flexible matching
    if (filteredPapers.length === 0) {
      console.log('🔍 No exact match found, trying flexible matching...')
      filteredPapers = papers.filter(paper =>
        paper.examLevel.toLowerCase().includes(examLevel.toLowerCase()) ||
        examLevel.toLowerCase().includes(paper.examLevel.toLowerCase())
      )
      console.log('🔍 Flexible match found:', filteredPapers.length, 'papers')
    }

    console.log('🔍 Filtered papers count:', filteredPapers.length)
    console.log('🔍 Sample filtered papers:', filteredPapers.slice(0, 3))

    const subjectsMap = new Map<string, any>()

    filteredPapers.forEach(paper => {
      if (!subjectsMap.has(paper.subjectCode)) {
        subjectsMap.set(paper.subjectCode, {
          id: paper.subjectCode,
          name: paper.subjectName,
          shortName: paper.subjectCode,
          examTypeId: examLevel
        })
      }
    })

    // Transform to the format expected by the dropdown
    availableSubjects.value = Array.from(subjectsMap.values())

    console.log('✅ Subjects extracted from papers for exam level:', examLevel, availableSubjects.value.length, 'subjects')
    console.log('📋 Extracted subjects:', availableSubjects.value)
  } catch (error) {
    console.error('Error loading subjects from papers:', error)
    sweetAlert.error('Error', 'Failed to load subjects')
    availableSubjects.value = []
  } finally {
    isLoadingSubjects.value = false
  }
}

// Load papers based on selected subject
const loadPapers = async (subjectId: string) => {
  if (!subjectId) {
    availablePapers.value = []
    return
  }

  isLoadingPapers.value = true
  try {
    // Get the selected subject details
    const selectedSubject = availableSubjects.value.find(s => s.id === subjectId)
    const subjectCode = selectedSubject?.id // Use subject ID as subject cod

    if (!subjectCode) {
      console.warn('Subject code not found for ID:', subjectId)
      availablePapers.value = []
      return
    }

    // Fetch papers filtered by subject and exam type using the store
    console.log('🔍 About to fetch papers with subjectCode:', subjectCode, 'examType:', filters.value.examType);
    let papers: any[] = []
    if (filters.value.examType) {
      console.log('📞 Calling getPapersBySubjectAndExamType with:', subjectCode, filters.value.examType);
      papers = await paperStore.getPapersBySubjectAndExamType(subjectCode, filters.value.examType)
    } else {
      console.log('📞 Calling fetchPapers with subjectId:', subjectCode);
      papers = await paperStore.fetchPapers({ subjectId: subjectCode })
    }
    console.log('📥 Received papers from store:', papers.length, 'papers');

    // Ensure papers is an array before mapping
    if (!Array.isArray(papers)) {
      console.error('Papers is not an array:', papers)
      availablePapers.value = []
      return
    }

    // Transform to the format expected by the dropdown
    availablePapers.value = papers.map((paper: any) => ({
      id: paper.id,
      name: paper.paperName,
      code: paper.paperId,
      subjectName: paper.subjectName,
      examLevel: paper.examLevel,
      mark: paper.paperMark
    }))

    console.log('✅ Papers loaded for subject code:', subjectCode, availablePapers.value.length, 'papers')
    console.log('📋 Available papers:', availablePapers.value)
  } catch (error) {
    console.error('Error loading papers:', error)
    sweetAlert.error('Error', 'Failed to load papers')
    availablePapers.value = []
  } finally {
    isLoadingPapers.value = false
  }
}

// Load centers with API filtering using query parameters
const loadFilteredCenters = async () => {
  if (!filters.value.examType) {
    console.log('⚠️ No exam type selected, skipping center filtering')
    return
  }

  isLoadingCenters.value = true
  try {
    // Build search term based on exam type and available filters
    let searchTerm = `examTypeId:${filters.value.examType}`

    // For Primary School exam types, add school filter if available
    if (isPrimarySchoolExamType.value && filters.value.school) {
      searchTerm += ` schoolId:${filters.value.school}`
    }

    const fetchOptions: any = {
      pageSize: 100 // Get more results for filtering
    }

    // Only add search parameter if we have a meaningful search term
    if (searchTerm && searchTerm.trim()) {
      fetchOptions.search = searchTerm
    }

    const filteredCenters = await centerStore.fetchCenters(fetchOptions)

    // Apply client-side filtering as backup since API doesn't support direct field filtering
    let finalCenters = filteredCenters

    if (isPrimarySchoolExamType.value && filters.value.school) {
      // For Primary School exam types (PLSCE, PSLCE, etc.), filter by both school and exam type
      finalCenters = filteredCenters.filter((center: any) =>
        center.schoolId === filters.value.school &&
        center.examTypeId === filters.value.examType &&
        center.isActive
      )
      console.log(`✅ Primary School exam type centers loaded for school ${filters.value.school} and exam type ${filters.value.examType}:`, finalCenters.length, 'items')
    } else if (!isPrimarySchoolExamType.value) {
      // For other exam types (MSCE, JCE, TTC, etc.), filter by exam type only
      finalCenters = filteredCenters.filter((center: any) =>
        center.examTypeId === filters.value.examType && center.isActive
      )
      console.log(`✅ Centers loaded for exam type ${filters.value.examType}:`, finalCenters.length, 'items')
    }

    centers.value = finalCenters
  } catch (error) {
    console.error('Error loading filtered centers:', error)
    sweetAlert.error('Error', 'Failed to load centers')
  } finally {
    isLoadingCenters.value = false
  }
}







// Check if selected exam type is Primary School type (PLSCE, PSLCE, or any exam type starting with "Primary S...")
const isPrimarySchoolExamType = computed(() => {
  const selectedExamType = examTypes.value.find(et => et.id === filters.value.examType)
  if (!selectedExamType?.name) return false

  const examTypeName = selectedExamType.name.toUpperCase()
  const examTypeId = selectedExamType.id.toUpperCase()

  console.log('🔍 Checking if Primary School exam type:', {
    id: examTypeId,
    name: examTypeName,
    selectedId: filters.value.examType
  })

  // Check for Primary School exam types:
  // 1. PLSCE (Primary Leaving Certificate Examination)
  // 2. PSLCE (Primary School Leaving Certificate Examination)
  // 3. PLCE (Primary Leaving Certificate Examination)
  // 4. Any exam type that contains "PRIMARY" in name or id
  const isPrimary = examTypeName.includes('PLSCE') ||
         examTypeName.includes('PSLCE') ||
         examTypeName.includes('PLCE') ||
         examTypeName.includes('PRIMARY') ||
         examTypeId.includes('PLSCE') ||
         examTypeId.includes('PSLCE') ||
         examTypeId.includes('PLCE') ||
         examTypeId.includes('PRIMARY')

  console.log('🎯 Is Primary School exam type:', isPrimary)
  return isPrimary
})

// Legacy computed property for backward compatibility
const isPLSCE = computed(() => isPrimarySchoolExamType.value)

// Calculate total required filters based on exam type
const totalRequiredFilters = computed(() => {
  // If subject is selected but has no papers, don't require paper selection
  const paperRequired = !filters.value.subject || availablePapers.value.length > 0

  // Adjust counts based on whether exam type and year are provided via route
  const examTypeRequired = !props.hasRouteParams
  const yearRequired = !props.hasRouteParams

  if (isPrimarySchoolExamType.value) {
    // Primary School types: ExamType + District + School + Center + Subject + (Paper if available) + Year
    let count = 0
    if (examTypeRequired) count++ // ExamType
    count++ // District (always required for Primary School)
    count++ // School
    count++ // Center
    count++ // Subject
    if (paperRequired) count++ // Paper (if available)
    if (yearRequired) count++ // Year
    return count
  } else {
    // Others: ExamType + Center + Subject + (Paper if available) + Year
    let count = 0
    if (examTypeRequired) count++ // ExamType
    count++ // Center
    count++ // Subject
    if (paperRequired) count++ // Paper (if available)
    if (yearRequired) count++ // Year
    return count
  }
})

const completedFilters = computed(() => {
  // Check if paper is required (only if subject is selected and has papers)
  const paperRequired = !filters.value.subject || availablePapers.value.length > 0

  if (isPrimarySchoolExamType.value) {
    // For Primary School exam types: count examType, district, school, center, subject, paper (if required), and year
    let count = 0
    if (filters.value.examType) count++ // Count if provided (either manually or via route)
    if (filters.value.district) count++
    if (filters.value.school) count++
    if (filters.value.center) count++
    if (filters.value.subject) count++
    if (paperRequired && filters.value.paper) count++
    if (!paperRequired && filters.value.subject) count++ // Count subject as final step if no papers
    if (filters.value.examYear) count++ // Count if provided (either manually or via route)
    return count
  } else {
    // For other exam types: count examType, center, subject, paper (if required), and year
    let count = 0
    if (filters.value.examType) count++ // Count if provided (either manually or via route)
    if (filters.value.center) count++
    if (filters.value.subject) count++
    if (paperRequired && filters.value.paper) count++
    if (!paperRequired && filters.value.subject) count++ // Count subject as final step if no papers
    if (filters.value.examYear) count++ // Count if provided (either manually or via route)
    return count
  }
})

const allFiltersSelected = computed(() => {
  return completedFilters.value === totalRequiredFilters.value
})

// Helper methods
const getDistrictName = (id: string) => {
  const district = districts.value.find(d => d.id === id)
  return district ? district.name : ''
}

const getCenterName = (id: string) => {
  const center = centers.value.find(c => c.id === id)
  return center ? `Center ${center.manebcentreNo}` : ''
}

const getSchoolName = (id: string) => {
  const school = schools.value.find(s => s.id === id)
  return school ? school.name : ''
}

const getSubjectName = (id: string) => {
  const subject = availableSubjects.value.find((s: any) => s.id === id)
  return subject ? subject.name : ''
}

const getPaperName = (id: string) => {
  const paper = availablePapers.value.find((p: any) => p.id === id)
  return paper ? paper.name : ''
}

const getExamTypeName = (id: string) => examTypes.value.find(e => e.id === id)?.name || ''

// Event handlers

const onExamTypeChange = async () => {
  console.log('🔄 Exam type changed to:', filters.value.examType)

  // Reset dependent filters
  filters.value.district = ''
  filters.value.school = ''
  filters.value.center = ''
  filters.value.subject = ''
  filters.value.paper = ''

  // Load centers for all exam types (Primary School and others)
  if (filters.value.examType) {
    console.log('📋 Loading centers for exam type:', filters.value.examType)
    await loadFilteredCenters()

    // Load subjects for the selected exam type
    loadSubjects()
  }
}

const onDistrictChange = async (option: any) => {
  console.log('🔄 District changed to:', filters.value.district)

  // Reset dependent filters (school and center)
  filters.value.school = ''
  filters.value.center = ''

  // Clear schools cache if district is cleared
  if (!filters.value.district) {
    schools.value = []
    schoolsLoaded.value = false
    console.log('🧹 District cleared, schools reset')
    return
  }

  // Trigger lazy loading of schools for the selected district
  await loadSchoolsForDistrict(filters.value.district)
}

const onSchoolChange = async (option: any) => {
  console.log('🔄 School changed to:', filters.value.school)

  // Reset center when school changes
  filters.value.center = ''

  // Load centers filtered by school and exam type (only for Primary School exam types)
  if (isPrimarySchoolExamType.value && filters.value.school && filters.value.examType) {
    console.log('🔍 Loading centers for school ID:', filters.value.school)
    await loadFilteredCenters()
  }
}

// Helper method to get center numbers by school ID
const getCenterNumbersBySchoolId = async (schoolId: string): Promise<string[]> => {
  try {
    const centers = await centerStore.fetchCenters({ schoolId })
    return centers.map((center: any) => center.manebcentreNo)
  } catch (error) {
    console.error('Error getting center numbers for school:', error)
    return []
  }
}

// Live search for centers by center number
const searchCentersLive = async (searchQuery: string) => {
  if (!filters.value.examType) {
    console.log('⚠️ No exam type selected, skipping center search')
    return
  }

  console.log('🔍 Starting live center search:', {
    searchQuery,
    examType: filters.value.examType,
    school: filters.value.school,
    isPrimarySchool: isPrimarySchoolExamType.value
  })

  isLoadingCenters.value = true
  try {
    // For live search, we'll search by center number specifically
    // The API search parameter will look for the query in center numbers
    const fetchOptions: any = {
      pageSize: 100, // Increase page size to get more results
      search: searchQuery // Search directly by center number
    }

    console.log('🔍 Fetching centers with options:', fetchOptions)
    const searchResults = await centerStore.fetchCenters(fetchOptions)
    console.log('🔍 Raw search results from API:', searchResults.length, 'centers')

    // Apply client-side filtering to ensure we get the right centers
    let filteredResults = searchResults.filter((center: any) => {
      // First, check if center number matches the search query
      const centerNumberMatches = center.manebcentreNo.toLowerCase().includes(searchQuery.toLowerCase())

      if (!centerNumberMatches || !center.isActive) {
        return false
      }

      // Then apply exam type and school filters based on current selection
      if (isPrimarySchoolExamType.value) {
        // For Primary School exam types (PLSCE), be more flexible with filtering
        const examTypeMatches = center.examTypeId === filters.value.examType

        // For PLSCE, let's be more flexible with school matching
        // If no school is selected, or if school data is inconsistent, just match by exam type
        let schoolMatches = true
        if (filters.value.school && center.schoolId) {
          schoolMatches = center.schoolId === filters.value.school
        }

        // For PLSCE, prioritize exam type match over school match
        // This allows centers to show up even if school data is inconsistent
        const result = examTypeMatches // Remove strict school requirement for now

        console.log('🔍 PLSCE filter check:', {
          centerNumber: center.manebcentreNo,
          centerName: center.name,
          centerSchoolId: center.schoolId,
          centerExamTypeId: center.examTypeId,
          selectedSchoolId: filters.value.school,
          selectedExamTypeId: filters.value.examType,
          examTypeMatches,
          schoolMatches,
          finalResult: result
        })
        return result
      } else {
        // For other exam types, only require exam type match
        const result = center.examTypeId === filters.value.examType
        console.log('🔍 Non-Primary School filter check:', {
          centerNumber: center.manebcentreNo,
          examTypeMatches: result
        })
        return result
      }
    })

    centers.value = filteredResults
    console.log(`✅ Live search results for center number "${searchQuery}":`, filteredResults.length, 'centers found')
    console.log('✅ Filtered centers:', filteredResults.map(c => ({
      id: c.id,
      number: c.manebcentreNo,
      name: c.name,
      schoolId: c.schoolId,
      examTypeId: c.examTypeId
    })))

    // Show helpful message if no results found
    if (filteredResults.length === 0 && searchQuery.length > 0) {
      console.log(`ℹ️ No centers found matching "${searchQuery}" with current filters`)
      console.log('ℹ️ Current filter state:', {
        examType: filters.value.examType,
        school: filters.value.school,
        isPrimarySchool: isPrimarySchoolExamType.value
      })
    }
  } catch (error) {
    console.error('Error in live center search:', error)
    sweetAlert.error('Error', 'Failed to search centers')
  } finally {
    isLoadingCenters.value = false
  }
}

const onCenterChange = (option: any) => {
  console.log('🔄 Center changed to:', filters.value.center)

  // Reset subject and paper when center changes
  filters.value.subject = ''
  filters.value.paper = ''

  // Load subjects for the selected exam type
  if (filters.value.examType) {
    loadSubjects()
  }
}

const onSubjectChange = () => {
  console.log('🔄 Subject changed to:', filters.value.subject)

  // Reset paper when subject changes
  filters.value.paper = ''

  // Load papers for the selected subject
  if (filters.value.subject) {
    loadPapers(filters.value.subject)
  }
}

const onPaperChange = () => {
  console.log('🔄 Paper changed to:', filters.value.paper)
  // Paper is the final filter in the chain
}

const onExamYearChange = () => {
  console.log('🔄 Exam Year changed to:', filters.value.examYear)
  // Year change doesn't affect other filters
}

// Center search handler with debouncing
const onCenterSearch = (query: string) => {
  centerSearchQuery.value = query

  // Clear existing timeout
  if (centerSearchTimeout.value) {
    clearTimeout(centerSearchTimeout.value)
  }

  // Debounce the search request (300ms delay)
  centerSearchTimeout.value = setTimeout(async () => {
    if (query.trim().length > 0) {
      console.log('🔍 Searching centers by number for:', query.trim())
      await searchCentersLive(query.trim())
    } else {
      // If search is empty, reload centers based on current filters
      console.log('🔄 Search cleared, reloading filtered centers')
      await loadFilteredCenters()
    }
  }, 300)
}

// Subject search handler
const onSubjectSearch = (query: string) => {
  subjectSearchQuery.value = query
}

const resetFilters = () => {
  filters.value = {
    district: '',
    center: '',
    examType: '',
    school: '',
    subject: '',
    paper: '',
    examYear: new Date().getFullYear() // Reset to current year
  }

  // Reset lazy loading state
  schools.value = []
  schoolsLoaded.value = false
}

const proceedToScoreEntry = async () => {
  if (!allFiltersSelected.value) {
    sweetAlert.error('Incomplete Selection', 'Please select all required filters before proceeding.')
    return
  }

  // Validate filter combination
  try {
    // Get the selected center details to extract the center number and school ID
    const selectedCenter = centers.value.find(c => c.id === filters.value.center)
    const centerNumber = selectedCenter?.manebcentreNo
    const schoolId = selectedCenter?.schoolId

    if (!centerNumber) {
      console.error('❌ Center number not found for selected center ID:', filters.value.center)
      sweetAlert.error('Error', 'Center number not found. Please select a valid center.')
      return
    }

    const filterData: any = {
      examType: filters.value.examType,
      examTypeId: filters.value.examType, // Add exam type ID for API calls
      center: filters.value.center, // Keep the center ID for internal use
      centerNumber: centerNumber, // Add the actual center number for API calls
      manebcentreNo: centerNumber, // Alternative property name for backward compatibility
      schoolId: schoolId, // Add school ID for API calls
      subject: filters.value.subject,
      paper: filters.value.paper,
      // Add readable names for display
      examTypeName: getExamTypeName(filters.value.examType),
      centerName: getCenterName(filters.value.center),
      subjectName: getSubjectName(filters.value.subject),
      paperName: getPaperName(filters.value.paper),
      // Use selected exam year
      examYear: filters.value.examYear
    }

    // Always include district and school for filter persistence
    filterData.district = filters.value.district
    filterData.school = filters.value.school
    filterData.districtName = getDistrictName(filters.value.district)
    filterData.schoolName = getSchoolName(filters.value.school)

    console.log('🔄 Proceeding to score entry with filters:', filterData)
    emit('filters-selected', filterData)
  } catch (error) {
    console.error('Error processing filters:', error)
    sweetAlert.error('Error', 'Failed to process filter selection. Please try again.')
  }
}

// Populate filters from props when editing
const populateFiltersFromProps = async () => {
  if (props.currentFilters) {
    console.log('🔄 Populating filters from props:', props.currentFilters)

    filters.value = {
      district: props.currentFilters.district || '',
      center: props.currentFilters.center || '',
      examType: props.currentFilters.examType || props.currentFilters.examTypeId || '',
      school: props.currentFilters.school || props.currentFilters.schoolId || '',
      subject: props.currentFilters.subject || '',
      paper: props.currentFilters.paper || '',
      examYear: props.currentFilters.examYear || new Date().getFullYear()
    }

    console.log('🔄 Populated filters:', filters.value)

    // Load dependent data based on populated filters in the correct order
    try {
      // Step 1: If district is set, load schools first (required for school selection)
      if (filters.value.district) {
        console.log('🔄 Step 1: Loading schools for district:', filters.value.district)
        await loadSchoolsForDistrict(filters.value.district)
      }

      // Step 2: If exam type is set, load centers and subjects
      if (filters.value.examType) {
        console.log('🔄 Step 2: Loading centers and subjects for exam type:', filters.value.examType)
        await Promise.all([
          loadFilteredCenters(),
          loadSubjects()
        ])
      }

      // Step 3: If subject is set, load papers
      if (filters.value.subject) {
        console.log('🔄 Step 3: Loading papers for subject:', filters.value.subject)
        await loadPapers(filters.value.subject)
      }

      // Step 4: Allow time for reactive updates to propagate
      await new Promise(resolve => setTimeout(resolve, 100))

      // Step 5: Verify that all options are available
      console.log('🔄 Step 5: Verifying loaded data and computed options:', {
        schoolsLoaded: schoolsLoaded.value,
        schoolsCount: schools.value.length,
        schoolOptionsCount: schoolOptions.value.length,
        centersCount: centers.value.length,
        centerOptionsCount: centerOptions.value.length,
        subjectsCount: availableSubjects.value.length,
        subjectOptionsCount: subjectOptions.value.length,
        papersCount: availablePapers.value.length
      })
    } catch (error) {
      console.error('Error loading dependent data:', error)
    }
  }
}

// Watch for changes in currentFilters prop
watch(() => props.currentFilters, async (newFilters, oldFilters) => {
  console.log('👀 currentFilters prop changed:', { newFilters, oldFilters })
  if (newFilters) {
    await populateFiltersFromProps()
  }
}, { immediate: true, deep: true })

// Watchers for reactive filtering
watch(() => filters.value.examType, async (newExamType, oldExamType) => {
  if (newExamType !== oldExamType && newExamType) {
    console.log('👀 Watcher: Exam type changed, triggering center reload for all exam types')
    // Load centers for all exam types (Primary School and others)
    await loadFilteredCenters()
  }
})

watch(() => [filters.value.school, filters.value.examType], async ([newSchool, newExamType], [oldSchool, oldExamType]) => {
  if ((newSchool !== oldSchool || newExamType !== oldExamType) && newSchool && newExamType && isPrimarySchoolExamType.value) {
    console.log('👀 Watcher: School or exam type changed, triggering center reload for Primary School exam type')
    await loadFilteredCenters()
  }
})

// Populate filters on mount and fetch API data
onMounted(async () => {
  // Load initial data from APIs first
  await Promise.all([
    loadExamTypes(),
    loadDistricts(),
    loadCenters()
  ])

  // If we have route parameters, initialize the exam type and year
  if (props.hasRouteParams && props.routeExamType) {
    // Find the exam type ID from the name
    const examType = examTypes.value.find(et => et.name === props.routeExamType)
    if (examType) {
      filters.value.examType = examType.id
      filters.value.examYear = props.routeExamYear || new Date().getFullYear()

      // Trigger the exam type change to load dependent data
      await onExamTypeChange()
    }
  }

  // Then populate filters from props (this may trigger school and subject loading)
  await populateFiltersFromProps()
})

// Cleanup on component unmount
onUnmounted(() => {
  if (centerSearchTimeout.value) {
    clearTimeout(centerSearchTimeout.value)
  }
})
</script>

<style scoped>
.bg-maneb-primary {
  background-color: #a12c2c;
}

.bg-maneb-primary-dark {
  background-color: #8b2424;
}

.focus\:ring-maneb-primary:focus {
  --tw-ring-color: rgba(161, 44, 44, 0.5);
}

.focus\:border-maneb-primary:focus {
  border-color: #a12c2c;
}

.focus\:ring-maneb-primary\/50:focus {
  --tw-ring-color: rgba(161, 44, 44, 0.5);
}
</style>

/**
 * @fileoverview Role Management Service for MANEB Results Management System
 * @description Comprehensive service for managing roles with full CRUD operations and API alignment
 */

import apiClient from '../core/api-client';
import type { 
  RoleDto, 
  CreateRoleRequest, 
  UpdateRoleRequest,
  RoleFilterDto,
  RecordStatus
} from '@/interfaces';

export class RoleManagementService {
  /**
   * Get all roles
   */
  async getAllRoles(): Promise<RoleDto[]> {
    try {
      return await apiClient.get<RoleDto[]>('/api/Role');
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to fetch roles');
    }
  }

  /**
   * Get roles with pagination and filters - matching API schema
   */
  async getRolesPaginated(
    pageNumber: number = 1, 
    pageSize: number = 10,
    filters?: {
      Search?: string;
      SortBy?: string;
      SortDescending?: boolean;
      status?: RecordStatus;
    }
  ): Promise<RoleDto[]> {
    try {
      const params = new URLSearchParams({
        PageNumber: pageNumber.toString(),
        PageSize: pageSize.toString()
      });

      // Add filters if provided
      if (filters?.Search) params.append('Search', filters.Search);
      if (filters?.SortBy) params.append('SortBy', filters.SortBy);
      if (filters?.SortDescending !== undefined) params.append('SortDescending', filters.SortDescending.toString());
      if (filters?.status) params.append('status', filters.status);

      return await apiClient.get<RoleDto[]>(`/api/Role/paginated?${params}`);
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to fetch roles');
    }
  }

  /**
   * Get role by ID
   */
  async getRoleById(id: string): Promise<RoleDto> {
    try {
      return await apiClient.get<RoleDto>(`/api/Role/${id}`);
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to fetch role');
    }
  }

  /**
   * Create new role
   */
  async createRole(roleData: CreateRoleRequest): Promise<RoleDto> {
    try {
      return await apiClient.post<RoleDto>('/api/Role', roleData);
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to create role');
    }
  }

  /**
   * Update role
   */
  async updateRole(id: string, roleData: UpdateRoleRequest): Promise<void> {
    try {
      await apiClient.put(`/api/Role/${id}`, roleData);
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to update role');
    }
  }

  /**
   * Delete role
   */
  async deleteRole(id: string): Promise<void> {
    try {
      await apiClient.delete(`/api/Role/${id}`);
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to delete role');
    }
  }

  /**
   * Approve role - matching API endpoint
   */
  async approveRole(id: string): Promise<void> {
    try {
      await apiClient.put(`/api/Role/${id}/approve`);
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to approve role');
    }
  }

  /**
   * Filter roles using local filtering (for backward compatibility)
   */
  async filterRoles(filters: RoleFilterDto): Promise<RoleDto[]> {
    try {
      let roles = await this.getAllRoles();

      if (filters.searchQuery) {
        const searchTerm = filters.searchQuery.toLowerCase();
        roles = roles.filter(role => 
          role.name?.toLowerCase().includes(searchTerm)
        );
      }

      if (filters.status && filters.status !== 'All') {
        roles = roles.filter(role => role.status === filters.status);
      }

      if (filters.hasPermissions !== undefined && filters.hasPermissions !== 'All') {
        roles = roles.filter(role => {
          const hasPerms = role.rolePermissions && role.rolePermissions.length > 0;
          return filters.hasPermissions === true ? hasPerms : !hasPerms;
        });
      }

      return roles;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to filter roles');
    }
  }

  /**
   * Search roles by query
   */
  async searchRoles(query: string): Promise<RoleDto[]> {
    try {
      return await this.getRolesPaginated(1, 100, { Search: query });
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to search roles');
    }
  }

  /**
   * Get roles by status
   */
  async getRolesByStatus(status: RecordStatus): Promise<RoleDto[]> {
    try {
      return await this.getRolesPaginated(1, 100, { status });
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to fetch roles by status');
    }
  }

  /**
   * Check if a role is a protected system role
   */
  isSystemAdminRole(role: RoleDto | string): boolean {
    const roleName = typeof role === 'string' ? role : role.name;
    if (!roleName) return false;

    const roleNameLower = roleName.toLowerCase();
    const systemAdminVariations = [
      'system_administrator',
      'systemadministrator', 
      'sysadmin',
      'system admin',
      'sys_admin',
      'sys admin'
    ];

    return systemAdminVariations.some(variation =>
      roleNameLower.includes(variation.toLowerCase())
    );
  }

  /**
   * Get non-system roles (excludes system administrator roles)
   */
  async getNonSystemRoles(): Promise<RoleDto[]> {
    try {
      const allRoles = await this.getAllRoles();
      return allRoles.filter(role => !this.isSystemAdminRole(role));
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to fetch non-system roles');
    }
  }
}

export const roleManagementService = new RoleManagementService();

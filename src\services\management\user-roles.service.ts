/**
 * @fileoverview User Roles Service for MANEB Results Management System
 * @description Service for managing user-role assignments with full API alignment
 */

import apiClient from '../core/api-client';
import type { 
  UserRolesDto, 
  AssignRoleDto
} from '@/interfaces';

export class UserRolesService {
  /**
   * Get all user-role assignments
   */
  async getAllUserRoles(): Promise<UserRolesDto[]> {
    try {
      return await apiClient.get<UserRolesDto[]>('/api/UserRoles');
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to fetch user roles');
    }
  }

  /**
   * Get roles for specific user
   */
  async getUserRoles(userId: string): Promise<UserRolesDto[]> {
    try {
      return await apiClient.get<UserRolesDto[]>(`/api/UserRoles/user/${userId}`);
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to fetch user roles');
    }
  }

  /**
   * Get users with specific role
   */
  async getUsersWithRole(roleId: string): Promise<UserRolesDto[]> {
    try {
      return await apiClient.get<UserRolesDto[]>(`/api/UserRoles/role/${roleId}`);
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to fetch users with role');
    }
  }

  /**
   * Assign role to user
   */
  async assignRole(assignRoleData: AssignRoleDto): Promise<UserRolesDto> {
    try {
      return await apiClient.post<UserRolesDto>('/api/UserRoles/assign', assignRoleData);
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to assign role to user');
    }
  }

  /**
   * Remove role from user
   */
  async removeRole(userId: string, roleId: string): Promise<void> {
    try {
      await apiClient.delete(`/api/UserRoles/remove/${userId}/${roleId}`);
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to remove role from user');
    }
  }

  /**
   * Check if user has specific role
   */
  async checkUserRole(userId: string, roleId: string): Promise<boolean> {
    try {
      return await apiClient.get<boolean>(`/api/UserRoles/check/${userId}/${roleId}`);
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to check user role');
    }
  }

  /**
   * Bulk assign roles to user
   */
  async bulkAssignRoles(userId: string, roleIds: string[]): Promise<UserRolesDto[]> {
    try {
      const assignments = roleIds.map(roleId => ({ userId, roleId }));
      const results: UserRolesDto[] = [];
      
      for (const assignment of assignments) {
        const result = await this.assignRole(assignment);
        results.push(result);
      }
      
      return results;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to bulk assign roles');
    }
  }

  /**
   * Bulk remove roles from user
   */
  async bulkRemoveRoles(userId: string, roleIds: string[]): Promise<void> {
    try {
      for (const roleId of roleIds) {
        await this.removeRole(userId, roleId);
      }
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to bulk remove roles');
    }
  }

  /**
   * Replace user roles (remove all existing and assign new ones)
   */
  async replaceUserRoles(userId: string, newRoleIds: string[]): Promise<UserRolesDto[]> {
    try {
      // Get current roles
      const currentRoles = await this.getUserRoles(userId);
      
      // Remove all current roles
      for (const userRole of currentRoles) {
        await this.removeRole(userId, userRole.roleId);
      }
      
      // Assign new roles
      return await this.bulkAssignRoles(userId, newRoleIds);
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to replace user roles');
    }
  }
}

export const userRolesService = new UserRolesService();

<template>
  <div class="min-h-screen bg-gray-50">
    <!-- MANEB Professional Responsive Header -->
    <nav class="bg-maneb-primary-light border-gray-200 dark:bg-gray-900">
      <div class="max-w-screen-xl flex flex-wrap items-center justify-between mx-auto px-4 py-1">
        <!-- Lo<PERSON> and Brand -->
        <a href="#" class="flex items-center space-x-2 rtl:space-x-reverse">
          <div class="logo-container">
            <img src="@/assets/logo.png" class="h-16 maneb-logo" alt="MANEB Logo" />
          </div>
          <div class="text-white">
            <span class="self-center text-xl font-semibold whitespace-nowrap">Student Portal</span>
            <div class="text-sm text-white text-opacity-90">MANEB Results Management System</div>
          </div>
        </a>

        <!-- Desktop Navigation and User Menu -->
        <div class="hidden md:flex items-center space-x-6">
          <!-- Desktop Navigation Links -->
          <nav class="flex space-x-6">
            <a href="#" @click="navigateToResults" class="text-white hover:bg-white hover:bg-opacity-10 hover:text-white px-3 py-2 rounded transition-all duration-200">Results</a>
            <a href="#" @click="navigateToCertificates" class="text-white hover:bg-white hover:bg-opacity-10 hover:text-white px-3 py-2 rounded transition-all duration-200">Certificates</a>
            <a href="#" @click="navigateToProfile" class="text-white hover:bg-white hover:bg-opacity-10 hover:text-white px-3 py-2 rounded transition-all duration-200">Profile</a>
            <a href="#" @click="navigateToSchedule" class="text-white hover:bg-white hover:bg-opacity-10 hover:text-white px-3 py-2 rounded transition-all duration-200">Schedule</a>
          </nav>

          <!-- Desktop User Info -->
          <div class="flex items-center space-x-3 text-white border-l border-white border-opacity-30 pl-6">
            <div class="flex items-center space-x-2">
              <div class="w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center relative">
                <span class="text-white font-semibold text-sm">{{ getUserInitials() }}</span>
                <div class="absolute -bottom-0.5 -right-0.5 w-3 h-3 rounded-full border-2 border-white" :class="statusIndicatorClass"></div>
              </div>
              <div class="hidden lg:block">
                <p class="font-medium text-sm">{{ authStore.userFullName || 'Student' }}</p>
                <p class="text-xs text-white text-opacity-75">{{ getStatusText() }}</p>
              </div>
            </div>

            <!-- Desktop Logout Button -->
            <button
              @click="handleLogout"
              class="bg-red-600 hover:bg-red-700 text-white border border-red-500 hover:border-red-600 px-3 py-1.5 rounded text-sm font-medium transition-all duration-200 flex items-center space-x-1 shadow-sm"
            >
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
              </svg>
              <span>Logout</span>
            </button>
          </div>
        </div>

        <!-- Mobile Menu Button -->
        <button
          @click="toggleMobileMenu"
          type="button"
          class="inline-flex items-center p-2 w-10 h-10 justify-center text-sm text-white rounded-lg md:hidden hover:bg-white hover:bg-opacity-20 focus:outline-none focus:ring-2 focus:ring-white focus:ring-opacity-30"
          :aria-expanded="isMobileMenuOpen"
        >
          <span class="sr-only">Open main menu</span>
          <svg v-if="!isMobileMenuOpen" class="w-5 h-5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 17 14">
            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M1 1h15M1 7h15M1 13h15"/>
          </svg>
          <svg v-else class="w-5 h-5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"/>
          </svg>
        </button>

      </div>

      <!-- Mobile Navigation Menu (Full Width Below Header) -->
      <div v-if="isMobileMenuOpen" class="md:hidden bg-maneb-primary-light border-t border-white border-opacity-20">
        <div class="max-w-screen-xl mx-auto px-4 py-4">
          <!-- Mobile User Info -->
          <div class="flex items-center space-x-3 mb-4 pb-4 border-b border-white border-opacity-20">
            <div class="w-10 h-10 bg-white bg-opacity-20 rounded-full flex items-center justify-center relative">
              <span class="text-white font-semibold">{{ getUserInitials() }}</span>
              <div class="absolute -bottom-0.5 -right-0.5 w-3 h-3 rounded-full border-2 border-white" :class="statusIndicatorClass"></div>
            </div>
            <div class="text-white">
              <p class="font-medium">{{ authStore.userFullName || 'Student User' }}</p>
              <p class="text-sm text-white text-opacity-75">{{ getStatusText() }}</p>
            </div>
          </div>

          <!-- Mobile Navigation Links -->
          <nav class="space-y-2">
            <a href="#" @click="navigateToResults; toggleMobileMenu()" class="flex items-center space-x-3 py-3 px-4 text-white rounded-lg hover:bg-maneb-red-dark hover:text-white transition-all duration-200">
              <svg class="w-5 h-5" fill="none" stroke="white" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
              </svg>
              <span>View Results</span>
            </a>
            <a href="#" @click="navigateToCertificates; toggleMobileMenu()" class="flex items-center space-x-3 py-3 px-4 text-white rounded-lg hover:bg-maneb-red-dark hover:text-white transition-all duration-200">
              <svg class="w-5 h-5" fill="none" stroke="white" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
              </svg>
              <span>Download Certificates</span>
            </a>
            <a href="#" @click="navigateToProfile; toggleMobileMenu()" class="flex items-center space-x-3 py-3 px-4 text-white rounded-lg hover:bg-maneb-red-dark hover:text-white transition-all duration-200">
              <svg class="w-5 h-5" fill="none" stroke="white" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
              </svg>
              <span>My Profile</span>
            </a>
            <a href="#" @click="navigateToSchedule; toggleMobileMenu()" class="flex items-center space-x-3 py-3 px-4 text-white rounded-lg hover:bg-maneb-red-dark hover:text-white transition-all duration-200">
              <svg class="w-5 h-5" fill="none" stroke="white" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
              </svg>
              <span>Exam Schedule</span>
            </a>
            <a href="#" @click="handleLogout" class="flex items-center space-x-3 py-3 px-4 text-white rounded-lg hover:bg-red-600 hover:text-white transition-all duration-200 border-t border-white border-opacity-20 mt-4 pt-4">
              <svg class="w-5 h-5" fill="none" stroke="white" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
              </svg>
              <span>Sign Out</span>
            </a>
          </nav>
        </div>
      </div>
    </nav>

    <!-- Main Content -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <!-- Welcome Section -->
      <div class="bg-white rounded-lg shadow-sm p-6 mb-8">
        <div class="flex items-center justify-between">
          <div>
            <h2 class="text-xl font-semibold text-gray-900 mb-2">
              Welcome back, {{ authStore.user?.firstName }}!
            </h2>
            <p class="text-gray-600">
              Access your examination results, certificates, and profile information.
            </p>
          </div>
          <div class="text-right">
            <div class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium"
                 :class="statusBadgeClass">
              <span class="w-2 h-2 rounded-full mr-2" :class="statusDotClass"></span>
              {{ authStore.userStatus }}
            </div>
          </div>
        </div>
      </div>

      <!-- Quick Actions Grid -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <!-- View Results -->
        <div class="bg-white rounded-lg shadow-sm p-6 hover:shadow-md transition-shadow cursor-pointer"
             @click="navigateToResults">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-10 h-10 bg-red-50 rounded-lg flex items-center justify-center">
                <svg class="w-6 h-6" fill="none" stroke="#a12c2c" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
              </div>
            </div>
            <div class="ml-4">
              <h3 class="text-lg font-medium text-gray-900">View Results</h3>
              <p class="text-sm text-gray-500">Check your examination results</p>
            </div>
          </div>
        </div>

        <!-- Download Certificates -->
        <div class="bg-white rounded-lg shadow-sm p-6 hover:shadow-md transition-shadow cursor-pointer"
             @click="navigateToCertificates">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-10 h-10 bg-orange-50 rounded-lg flex items-center justify-center">
                <svg class="w-6 h-6" fill="none" stroke="#D97706" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
              </div>
            </div>
            <div class="ml-4">
              <h3 class="text-lg font-medium text-gray-900">Certificates</h3>
              <p class="text-sm text-gray-500">Download your certificates</p>
            </div>
          </div>
        </div>

        <!-- Profile -->
        <div class="bg-white rounded-lg shadow-sm p-6 hover:shadow-md transition-shadow cursor-pointer"
             @click="navigateToProfile">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-10 h-10 bg-red-50 rounded-lg flex items-center justify-center">
                <svg class="w-6 h-6" fill="none" stroke="#a12c2c" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                </svg>
              </div>
            </div>
            <div class="ml-4">
              <h3 class="text-lg font-medium text-gray-900">My Profile</h3>
              <p class="text-sm text-gray-500">View and edit your profile</p>
            </div>
          </div>
        </div>

        <!-- Exam Schedule -->
        <div class="bg-white rounded-lg shadow-sm p-6 hover:shadow-md transition-shadow cursor-pointer"
             @click="navigateToSchedule">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-10 h-10 bg-red-50 rounded-lg flex items-center justify-center">
                <svg class="w-6 h-6" fill="none" stroke="#a12c2c" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                </svg>
              </div>
            </div>
            <div class="ml-4">
              <h3 class="text-lg font-medium text-gray-900">Exam Schedule</h3>
              <p class="text-sm text-gray-500">View upcoming examinations</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Recent Activity / Announcements -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <!-- Recent Results -->
        <div class="bg-white rounded-lg shadow-sm">
          <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Recent Results</h3>
          </div>
          <div class="p-6">
            <div class="text-center py-8">
              <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
              </svg>
              <p class="mt-2 text-sm text-gray-500">No recent results available</p>
              <button class="mt-4 text-maneb-primary hover-text-maneb-primary-dark text-sm font-medium">
                View All Results
              </button>
            </div>
          </div>
        </div>

        <!-- Announcements -->
        <div class="bg-white rounded-lg shadow-sm">
          <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Announcements</h3>
          </div>
          <div class="p-6">
            <div class="text-center py-8">
              <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5.882V19.24a1.76 1.76 0 01-3.417.592l-2.147-6.15M18 13a3 3 0 100-6M5.436 13.683A4.001 4.001 0 017 6h1.832c4.1 0 7.625-1.234 9.168-3v14c-1.543-1.766-5.067-3-9.168-3H7a3.988 3.988 0 01-1.564-.317z"></path>
              </svg>
              <p class="mt-2 text-sm text-gray-500">No new announcements</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores'
import sweetAlert from '@/utils/ui/sweetAlert'

// Composables
const router = useRouter()
const authStore = useAuthStore()

// Mobile menu state
const isMobileMenuOpen = ref(false)

// Computed properties
const statusBadgeClass = computed(() => {
  switch (authStore.userStatus) {
    case 'SecondApproved':
      return 'bg-green-100 text-green-800'
    case 'Approved':
      return 'bg-blue-100 text-blue-800'
    case 'Unapproved':
      return 'bg-yellow-100 text-yellow-800'
    case 'Rejected':
      return 'bg-red-100 text-red-800'
    default:
      return 'bg-gray-100 text-gray-800'
  }
})

const statusDotClass = computed(() => {
  switch (authStore.userStatus) {
    case 'SecondApproved': // Admin
      return 'bg-green-400'
    case 'Approved': // Student
      return 'bg-blue-400'
    case 'Unapproved': // Pending
      return 'bg-yellow-400'
    case 'Rejected':
      return 'bg-red-400'
    default:
      return 'bg-gray-400'
  }
})

const statusIndicatorClass = computed(() => {
  switch (authStore.userStatus) {
    case 'SecondApproved': // Admin
      return 'bg-green-400'
    case 'Approved': // Student
      return 'bg-blue-400'
    case 'Unapproved': // Pending
      return 'bg-yellow-400'
    case 'Rejected':
      return 'bg-red-400'
    default:
      return 'bg-gray-400'
  }
})

// Methods
const getUserInitials = () => {
  const firstName = authStore.user?.firstName || ''
  const lastName = authStore.user?.lastName || ''
  if (firstName && lastName) {
    return (firstName.charAt(0) + lastName.charAt(0)).toUpperCase()
  }
  if (authStore.userFullName) {
    const names = authStore.userFullName.split(' ')
    if (names.length >= 2) {
      return (names[0].charAt(0) + names[names.length - 1].charAt(0)).toUpperCase()
    }
    return names[0].charAt(0).toUpperCase()
  }
  return 'ST' // Default for Student
}

const getStatusText = () => {
  switch (authStore.userStatus) {
    case 'SecondApproved': // Admin
      return 'Admin User'
    case 'Approved': // Student
      return 'Active Student'
    case 'Unapproved': // Pending
      return 'Pending Approval'
    case 'Rejected':
      return 'Access Denied'
    default:
      return 'Student'
  }
}

const handleLogout = async () => {
  const result = await sweetAlert.confirm(
    'Sign Out',
    'Are you sure you want to sign out of your account?',
    'Sign Out',
    'Cancel'
  )
  
  if (result.isConfirmed) {
    await authStore.logout()
    sweetAlert.toast.success('Signed out successfully')
    router.push('/auth/login')
  }
}

const navigateToResults = () => {
  sweetAlert.info('Coming Soon', 'Results viewing feature will be available soon.')
}

const navigateToCertificates = () => {
  sweetAlert.info('Coming Soon', 'Certificate download feature will be available soon.')
}

const navigateToProfile = () => {
  sweetAlert.info('Coming Soon', 'Profile management feature will be available soon.')
}

const navigateToSchedule = () => {
  sweetAlert.info('Coming Soon', 'Examination schedule feature will be available soon.')
}

// Mobile menu toggle
const toggleMobileMenu = () => {
  isMobileMenuOpen.value = !isMobileMenuOpen.value
}
</script>

<style scoped>
/* MANEB Color Scheme - Direct hex values for reliability */
.bg-maneb-primary {
  background-color: #a12c2c;
}

.bg-maneb-primary-light {
  background-color: #c53030;
}

.bg-maneb-red-dark {
  background-color: #8b2424;
}

.bg-maneb-secondary {
  background-color: #D97706;
}

.text-maneb-primary {
  color: #a12c2c;
}

.text-maneb-secondary {
  color: #D97706;
}

.hover-text-maneb-primary-dark:hover {
  color: var(--maneb-red-dark);
}

/* Professional MANEB Logo Styling */
.logo-container {
  position: relative;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.15);
  transition: all 0.3s ease;
}

.logo-container:hover {
  background: rgba(255, 255, 255, 0.12);
  border-color: rgba(255, 255, 255, 0.25);
  transform: translateY(-1px);
}

.maneb-logo {
  /* Keep original MANEB colors - no filter applied */
  transition: all 0.3s ease;
}

.logo-container:hover .maneb-logo {
  /* Subtle enhancement on hover while preserving original colors */
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2)) brightness(1.1);
}
</style>

/**
 * @fileoverview User management form interfaces for MANEB Results Management System
 * @description Standardized interfaces for user management form data and validation
 */

import type { Gender, RecordStatus, PermissionAction } from '../common/base.interface';
import type { ValidationResult } from '../common/base.interface';

/**
 * User form data
 * @description Form data structure for creating/editing users
 */
export interface UserFormData {
  /** First name (required) */
  firstName: string;
  /** Last name (required) */
  lastName: string;
  /** Gender */
  gender?: Gender | '';
  /** ID number */
  idNumber?: string;
  /** ID type */
  idType?: string;
  /** Date of birth */
  dateOfBirth?: Date | string | null;
  /** Email address */
  email?: string;
  /** Username */
  userName?: string;
  /** Role ID */
  roleId?: string;
  /** Password (for creation) */
  password?: string;
  /** Confirm password (for creation) */
  confirmPassword?: string;
  /** Record status */
  status?: RecordStatus;
}

/**
 * Role form data
 * @description Form data structure for creating/editing roles
 */
export interface RoleFormData {
  /** Role name */
  name: string;
  /** Role description */
  description?: string;
  /** Record status */
  status?: RecordStatus;
  /** Permissions for this role */
  permissions?: RolePermissionFormData[];
}

/**
 * Role permission form data
 * @description Form data structure for role permissions
 */
export interface RolePermissionFormData {
  /** Section ID */
  sectionId: string;
  /** Permission action */
  action: PermissionAction;
  /** Whether access is granted */
  canAccess: boolean;
  /** Record status */
  status?: RecordStatus;
}

/**
 * Section form data
 * @description Form data structure for creating/editing sections
 */
export interface SectionFormData {
  /** Section name */
  name: string;
  /** API controller name */
  controller?: string;
  /** API area name */
  area?: string;
  /** Section description */
  description?: string;
  /** Record status */
  status?: RecordStatus;
}

/**
 * Workspace form data
 * @description Form data structure for creating/editing workspaces
 */
export interface WorkspaceFormData {
  /** Workspace name */
  name: string;
  /** Description */
  description?: string;
  /** Owner user ID */
  ownerId: string;
  /** Whether workspace is active */
  isActive: boolean;
  /** Workspace settings */
  settings?: WorkspaceSettingsFormData;
}

/**
 * Workspace settings form data
 * @description Form data for workspace settings
 */
export interface WorkspaceSettingsFormData {
  /** Allow public access */
  allowPublicAccess?: boolean;
  /** Require approval for joining */
  requireApprovalForJoin?: boolean;
  /** Maximum number of members */
  maxMembers?: number | null;
  /** Default role for new members */
  defaultRole?: string;
}

/**
 * User form validation rules
 * @description Validation configuration for user forms
 */
export interface UserFormValidation {
  /** Required fields */
  required: (keyof UserFormData)[];
  /** Email validation pattern */
  emailPattern?: RegExp;
  /** Password requirements */
  passwordRules?: {
    minLength: number;
    requireUppercase: boolean;
    requireLowercase: boolean;
    requireNumbers: boolean;
    requireSpecialChars: boolean;
  };
  /** Username requirements */
  usernameRules?: {
    minLength: number;
    maxLength: number;
    allowedChars: RegExp;
  };
  /** Custom validation rules */
  customRules?: {
    name: string;
    validator: (data: UserFormData) => boolean;
    message: string;
  }[];
}

/**
 * User form errors
 * @description Error state for user forms
 */
export interface UserFormErrors {
  /** First name error */
  firstName?: string;
  /** Last name error */
  lastName?: string;
  /** Gender error */
  gender?: string;
  /** ID number error */
  idNumber?: string;
  /** ID type error */
  idType?: string;
  /** Date of birth error */
  dateOfBirth?: string;
  /** Email error */
  email?: string;
  /** Username error */
  userName?: string;
  /** Role error */
  roleId?: string;
  /** Password error */
  password?: string;
  /** Confirm password error */
  confirmPassword?: string;
  /** Status error */
  status?: string;
  /** General form error */
  general?: string;
}

/**
 * Role form errors
 * @description Error state for role forms
 */
export interface RoleFormErrors {
  /** Role name error */
  name?: string;
  /** Description error */
  description?: string;
  /** Status error */
  status?: string;
  /** Permissions error */
  permissions?: string;
  /** General form error */
  general?: string;
}

/**
 * User form state
 * @description Complete form state for user management
 */
export interface UserFormState {
  /** Form data */
  data: UserFormData;
  /** Form errors */
  errors: UserFormErrors;
  /** Whether form is being submitted */
  isSubmitting: boolean;
  /** Whether form has been touched */
  isTouched: boolean;
  /** Whether form is valid */
  isValid: boolean;
  /** Validation warnings */
  warnings: string[];
  /** Whether password is visible */
  showPassword?: boolean;
  /** Whether confirm password is visible */
  showConfirmPassword?: boolean;
}

/**
 * Role form state
 * @description Complete form state for role management
 */
export interface RoleFormState {
  /** Form data */
  data: RoleFormData;
  /** Form errors */
  errors: RoleFormErrors;
  /** Whether form is being submitted */
  isSubmitting: boolean;
  /** Whether form has been touched */
  isTouched: boolean;
  /** Whether form is valid */
  isValid: boolean;
  /** Validation warnings */
  warnings: string[];
}

/**
 * User form props
 * @description Props interface for user form components
 */
export interface UserFormProps {
  /** Whether modal/form is open */
  isOpen: boolean;
  /** Existing user data for editing */
  user?: any | null;
  /** Whether form is in edit mode */
  isEditing?: boolean;
  /** Available roles */
  availableRoles?: Array<{ id: string; name: string }>;
  /** Form validation rules */
  validationRules?: UserFormValidation;
  /** Whether to show advanced fields */
  showAdvancedFields?: boolean;
}

/**
 * Role form props
 * @description Props interface for role form components
 */
export interface RoleFormProps {
  /** Whether modal/form is open */
  isOpen: boolean;
  /** Existing role data for editing */
  role?: any | null;
  /** Whether form is in edit mode */
  isEditing?: boolean;
  /** Available sections */
  availableSections?: Array<{ id: string; name: string }>;
  /** Whether to show permissions editor */
  showPermissionsEditor?: boolean;
}

/**
 * User form events
 * @description Event interface for user form components
 */
export interface UserFormEvents {
  /** Form close event */
  close: [];
  /** Form success event */
  success: [userId?: string];
  /** Form error event */
  error: [error: string];
  /** Form validation event */
  validate: [result: ValidationResult];
  /** Form data change event */
  change: [data: UserFormData];
}

/**
 * Role form events
 * @description Event interface for role form components
 */
export interface RoleFormEvents {
  /** Form close event */
  close: [];
  /** Form success event */
  success: [roleId?: string];
  /** Form error event */
  error: [error: string];
  /** Form validation event */
  validate: [result: ValidationResult];
  /** Form data change event */
  change: [data: RoleFormData];
}

/**
 * Bulk user operation form data
 * @description Form data for bulk user operations
 */
export interface BulkUserOperationFormData {
  /** Operation type */
  operation: 'create' | 'update' | 'delete' | 'activate' | 'deactivate' | 'assign_role';
  /** User IDs to process */
  userIds: string[];
  /** Data for the operation */
  operationData?: {
    /** Role ID for role assignment */
    roleId?: string;
    /** Status for status change */
    status?: RecordStatus;
    /** Other operation-specific data */
    [key: string]: any;
  };
  /** Additional options */
  options?: {
    /** Whether to send notifications */
    sendNotifications?: boolean;
    /** Whether to create audit log */
    createAuditLog?: boolean;
    /** Whether to stop on first error */
    stopOnError?: boolean;
  };
}

/**
 * User import form data
 * @description Form data for importing users from file
 */
export interface UserImportFormData {
  /** File to import */
  file: File | null;
  /** File format */
  format: 'csv' | 'excel' | 'json';
  /** Default role for imported users */
  defaultRoleId?: string;
  /** Default status for imported users */
  defaultStatus?: RecordStatus;
  /** Whether to send welcome emails */
  sendWelcomeEmails: boolean;
  /** Whether to generate random passwords */
  generatePasswords: boolean;
  /** Column mapping for CSV/Excel */
  columnMapping?: Record<string, string>;
  /** Import options */
  options?: {
    /** Skip header row */
    skipHeader?: boolean;
    /** Delimiter for CSV */
    delimiter?: string;
    /** Sheet name for Excel */
    sheetName?: string;
    /** Whether to update existing users */
    updateExisting?: boolean;
  };
}

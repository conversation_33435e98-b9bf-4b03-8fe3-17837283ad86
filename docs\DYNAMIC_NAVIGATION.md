# Dynamic Navigation System - MANEB Results Management System

## Overview

The MANEB Results Management System now implements a dynamic navigation system that shows or hides navigation sections based on the user's role permissions. This ensures users only see the sections they have access to, providing a cleaner and more secure user experience.

## Features

### 1. **Dynamic Section Visibility**
- Navigation sections are filtered based on actual user permissions from the API
- Sections without permissions are completely hidden (not just disabled)
- System Administrators see all sections regardless of specific permissions

### 2. **Permission-Based Menu Population**
- Each navigation section appears only if the user has at least one permission for that section
- Uses the existing `sectionID` + `action` + `canAccess` permission structure
- Supports granular action-level permissions (Create, Read, Update, Delete, etc.)

### 3. **Action-Level Granularity**
- Sub-menu items are filtered based on specific permissions
- Enhanced filtering for User Management module:
  - **Users**: Shown if user has any user management permission
  - **Roles**: Shown if user has Create, Update, or All permissions
  - **Permissions**: Shown if user has Create, Update, or All permissions
  - **Sections**: Shown if user has Create, Update, or All permissions

### 4. **Supported Navigation Sections**
- **Dashboard**: `dashboard` section
- **Score Entry**: `score_entry` section
- **Grading System Setup**: `grading` section
- **Results Management**: `results_management` section
- **User Management**: `user_management` section
- **Reports**: `reports` section
- **Audit Logs**: `audit_logs` section
- **System Settings**: `system_settings` section

## Implementation Details

### Core Components

#### 1. **useNavigationPermissions Composable**
Location: `src/composables/useNavigationPermissions.ts`

Provides reusable permission checking logic:
- `hasModulePermission(module)`: Check if user has access to a module
- `getModuleActions(module)`: Get user's actions for a module
- `filterNavigationItems(items)`: Filter navigation items based on permissions
- `filterSubmenuItems(module, children)`: Filter submenu items with enhanced logic

#### 2. **Updated Navigation Component**
Location: `src/components/layouts/admin/sigelege-layout-page.vue`

- Removed `PermissionGuard` components (now filtering at navigation level)
- Uses composable for all permission logic
- Provides real-time updates when user permissions change

### Permission Mapping

The system maps permission modules to API section IDs:

```typescript
const moduleToSectionMap = {
  'dashboard': 'dashboard',
  'score_entry': 'score_entry',
  'grading': 'grading',
  'results_management': 'results_management',
  'user_management': 'user_management',
  'reports': 'reports',
  'audit_logs': 'audit_logs',
  'system_settings': 'system_settings'
}
```

### User Permission Structure

The system uses the existing `RolePermissionDto` structure:
```typescript
{
  sectionID: string,    // e.g., 'user_management'
  action: string,       // e.g., 'Create', 'Read', 'Update', 'Delete', 'All'
  canAccess: boolean    // true if permission is granted
}
```

## Testing the System

### 1. **System Administrator Test**
- Login as a System Administrator
- Should see ALL navigation sections
- All submenu items should be visible

### 2. **Limited Role Test**
- Create a role with limited permissions (e.g., only Score Entry)
- Assign user to this role
- Login as this user
- Should only see Dashboard and Score Entry sections

### 3. **User Management Permissions Test**
- Create a role with only "Read" permission for User Management
- User should see User Management section but limited submenu items
- Create a role with "Create" and "Update" permissions
- User should see additional submenu items (Roles, Permissions, Sections)

### 4. **Dynamic Updates Test**
- Change user's role permissions via admin interface
- Navigation should update automatically (may require page refresh)

## Console Debugging

The system provides extensive console logging for debugging:

```javascript
// Check permission summary
console.log('Permission summary:', permissionSummary.value)

// Check specific module access
console.log(`Module ${module} access:`, hasAccess)

// Check user actions for module
console.log(`User actions for ${module}:`, userActions)
```

## Benefits

1. **Security**: Users can't see sections they don't have access to
2. **User Experience**: Cleaner interface with only relevant options
3. **Maintainability**: Centralized permission logic in composable
4. **Flexibility**: Easy to add new modules or modify permission logic
5. **Performance**: Filtering happens at navigation level, not per component

## Future Enhancements

1. **Route-Level Protection**: Extend to protect routes based on permissions
2. **Button-Level Permissions**: Apply same logic to action buttons within pages
3. **Caching**: Cache permission calculations for better performance
4. **Real-Time Updates**: WebSocket integration for instant permission updates
5. **Audit Trail**: Log navigation access attempts for security monitoring

## Troubleshooting

### Common Issues

1. **Navigation not updating**: Check if user permissions are loaded correctly
2. **All sections hidden**: Verify user has valid role with permissions
3. **System Admin not seeing all**: Check `isSystemAdministrator` flag
4. **Submenu items missing**: Verify specific action permissions for the module

### Debug Steps

1. Check browser console for permission logs
2. Verify user permissions in auth store: `authStore.userPermissions`
3. Check role assignment: `authStore.user.role`
4. Verify API responses for role permissions endpoint

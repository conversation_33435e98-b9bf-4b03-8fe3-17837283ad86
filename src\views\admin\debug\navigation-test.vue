<template>
  <div class="min-h-screen bg-gray-50">
    <!-- Header -->
    <div class="bg-white shadow">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="py-6">
          <h1 class="text-2xl font-bold text-gray-900">Navigation Permissions Test</h1>
          <p class="mt-2 text-sm text-gray-600">
            This page helps test and debug the dynamic navigation system based on user permissions.
          </p>
        </div>
      </div>
    </div>

    <!-- Main Content -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <!-- Navigation Permissions Debug Component -->
      <NavigationPermissionsDebug />

      <!-- Navigation Test Results -->
      <div class="bg-white shadow rounded-lg p-6 mt-6">
        <h2 class="text-lg font-semibold text-gray-900 mb-4">Navigation Test Results</h2>
        
        <!-- Expected vs Actual Navigation -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <!-- Expected Navigation -->
          <div>
            <h3 class="text-md font-medium text-gray-700 mb-3">Expected Navigation Items</h3>
            <div class="space-y-2">
              <div v-for="item in expectedNavigation" :key="item.name"
                   class="p-3 border rounded-lg">
                <div class="font-medium">{{ item.name }}</div>
                <div class="text-sm text-gray-600">{{ item.reason }}</div>
                <div v-if="item.children" class="mt-2 ml-4">
                  <div class="text-sm font-medium text-gray-500">Expected Submenu:</div>
                  <ul class="text-sm text-gray-600 list-disc list-inside">
                    <li v-for="child in item.children" :key="child">{{ child }}</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>

          <!-- Actual Navigation -->
          <div>
            <h3 class="text-md font-medium text-gray-700 mb-3">Actual Navigation Items</h3>
            <div class="space-y-2">
              <div v-for="item in actualNavigation" :key="item.name"
                   class="p-3 border rounded-lg">
                <div class="font-medium">{{ item.name }}</div>
                <div class="text-sm text-gray-600">
                  Module: {{ item.permission?.module || 'None' }}
                </div>
                <div v-if="item.children && item.children.length > 0" class="mt-2 ml-4">
                  <div class="text-sm font-medium text-gray-500">Actual Submenu:</div>
                  <ul class="text-sm text-gray-600 list-disc list-inside">
                    <li v-for="child in item.children" :key="child.name">{{ child.name }}</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Test Instructions -->
        <div class="mt-8 p-4 bg-blue-50 rounded-lg">
          <h3 class="text-md font-medium text-blue-900 mb-2">Test Instructions</h3>
          <ol class="text-sm text-blue-800 list-decimal list-inside space-y-1">
            <li>Check if the "Expected Navigation Items" match the "Actual Navigation Items"</li>
            <li>Verify that sections without permissions are completely hidden</li>
            <li>Test with different user roles to see navigation changes</li>
            <li>Check submenu filtering for User Management section</li>
            <li>Verify System Administrator sees all sections</li>
          </ol>
        </div>

        <!-- Test Results Summary -->
        <div class="mt-6 p-4 rounded-lg" :class="testPassed ? 'bg-green-50' : 'bg-red-50'">
          <h3 class="text-md font-medium mb-2" :class="testPassed ? 'text-green-900' : 'text-red-900'">
            Test Results: {{ testPassed ? 'PASSED' : 'FAILED' }}
          </h3>
          <div class="text-sm" :class="testPassed ? 'text-green-800' : 'text-red-800'">
            {{ testResultMessage }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useAuthStore } from '@/stores/auth/auth.store'
import { useNavigationPermissions } from '@/composables/auth/useNavigationPermissions'
import { PermissionModule } from '@/utils/auth/permissions'
import NavigationPermissionsDebug from '@/components/admin/debug/NavigationPermissionsDebug.vue'

const authStore = useAuthStore()
const { hasModulePermission, getModuleActions } = useNavigationPermissions()

// Expected navigation based on user permissions
const expectedNavigation = computed(() => {
  const items = []

  // Dashboard - should always be visible if user has access
  if (hasModulePermission(PermissionModule.DASHBOARD)) {
    items.push({
      name: 'Dashboard',
      reason: 'User has dashboard permissions'
    })
  }

  // Score Entry
  if (hasModulePermission(PermissionModule.SCORE_ENTRY)) {
    items.push({
      name: 'Score Entry',
      reason: 'User has score entry permissions',
      children: ['JCE | 2024', 'MSCE | 2024', 'PSLCE | 2024', 'TTC | 2024']
    })
  }

  // Grading System
  if (hasModulePermission(PermissionModule.GRADING)) {
    items.push({
      name: 'Grading System Setup',
      reason: 'User has grading permissions',
      children: ['Grading System']
    })
  }

  // Results Management
  if (hasModulePermission(PermissionModule.RESULTS_MANAGEMENT)) {
    items.push({
      name: 'Results Management',
      reason: 'User has results management permissions'
    })
  }

  // User Management
  if (hasModulePermission(PermissionModule.USER_MANAGEMENT)) {
    const userActions = getModuleActions(PermissionModule.USER_MANAGEMENT)
    const children = []
    
    if (userActions.length > 0) children.push('Users')
    if (userActions.includes('Create') || userActions.includes('Update') || userActions.includes('All')) {
      children.push('Roles', 'Permissions', 'Sections')
    }

    items.push({
      name: 'User Management',
      reason: 'User has user management permissions',
      children
    })
  }

  // Reports
  if (hasModulePermission(PermissionModule.REPORTS)) {
    items.push({
      name: 'Reports',
      reason: 'User has reports permissions'
    })
  }

  // Audit Logs
  if (hasModulePermission(PermissionModule.AUDIT_LOGS)) {
    items.push({
      name: 'Audit Logs',
      reason: 'User has audit logs permissions'
    })
  }

  // System Settings
  if (hasModulePermission(PermissionModule.SYSTEM_SETTINGS)) {
    items.push({
      name: 'System Settings',
      reason: 'User has system settings permissions'
    })
  }

  return items
})

// Simulate actual navigation (this would come from the navigation component)
const actualNavigation = computed(() => {
  // This is a simplified version - in reality, this would come from the actual navigation component
  const items = []

  if (hasModulePermission(PermissionModule.DASHBOARD)) {
    items.push({
      name: 'Dashboard',
      permission: { module: PermissionModule.DASHBOARD }
    })
  }

  if (hasModulePermission(PermissionModule.SCORE_ENTRY)) {
    items.push({
      name: 'Score Entry',
      permission: { module: PermissionModule.SCORE_ENTRY },
      children: [
        { name: 'JCE | 2024' },
        { name: 'MSCE | 2024' },
        { name: 'PSLCE | 2024' },
        { name: 'TTC | 2024' }
      ]
    })
  }

  if (hasModulePermission(PermissionModule.USER_MANAGEMENT)) {
    const userActions = getModuleActions(PermissionModule.USER_MANAGEMENT)
    const children = []
    
    if (userActions.length > 0) children.push({ name: 'Users' })
    if (userActions.includes('Create') || userActions.includes('Update') || userActions.includes('All')) {
      children.push({ name: 'Roles' }, { name: 'Permissions' }, { name: 'Sections' })
    }

    items.push({
      name: 'User Management',
      permission: { module: PermissionModule.USER_MANAGEMENT },
      children
    })
  }

  // Add other modules...
  if (hasModulePermission(PermissionModule.GRADING)) {
    items.push({
      name: 'Grading System Setup',
      permission: { module: PermissionModule.GRADING },
      children: [{ name: 'Grading System' }]
    })
  }

  if (hasModulePermission(PermissionModule.RESULTS_MANAGEMENT)) {
    items.push({
      name: 'Results Management',
      permission: { module: PermissionModule.RESULTS_MANAGEMENT }
    })
  }

  if (hasModulePermission(PermissionModule.REPORTS)) {
    items.push({
      name: 'Reports',
      permission: { module: PermissionModule.REPORTS }
    })
  }

  if (hasModulePermission(PermissionModule.AUDIT_LOGS)) {
    items.push({
      name: 'Audit Logs',
      permission: { module: PermissionModule.AUDIT_LOGS }
    })
  }

  if (hasModulePermission(PermissionModule.SYSTEM_SETTINGS)) {
    items.push({
      name: 'System Settings',
      permission: { module: PermissionModule.SYSTEM_SETTINGS }
    })
  }

  return items
})

// Test validation
const testPassed = computed(() => {
  return expectedNavigation.value.length === actualNavigation.value.length
})

const testResultMessage = computed(() => {
  if (testPassed.value) {
    return `Navigation filtering is working correctly. ${actualNavigation.value.length} sections are visible based on user permissions.`
  } else {
    return `Navigation filtering mismatch. Expected ${expectedNavigation.value.length} sections, but got ${actualNavigation.value.length}.`
  }
})
</script>

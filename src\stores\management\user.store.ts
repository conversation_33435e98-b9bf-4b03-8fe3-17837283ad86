import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import { userService } from '@/services';
import type { UserDto, CreateUserRequest, UpdateUserRequest, RecordStatus, RoleDto } from '@/interfaces';

export const useUserStore = defineStore('user', () => {
  // State
  const users = ref<UserDto[]>([]);
  const currentUser = ref<UserDto | null>(null);
  const isLoading = ref(false);
  const error = ref<string | null>(null);
  const searchQuery = ref('');
  const statusFilter = ref<RecordStatus | 'All'>('All');
  const roleFilter = ref<string | 'All'>('All');
  const statistics = ref({
    total: 0,
    byStatus: {
      'Unapproved': 0,
      'Approved': 0,
      'SecondApproved': 0,
      'Rejected': 0
    } as Record<RecordStatus, number>,
    byRole: {} as Record<string, number>,
    active: 0,
    deleted: 0
  });
  const pagination = ref({
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 0
  });
  const bulkOperations = ref({
    selectedUsers: [] as string[],
    isProcessing: false,
    lastOperation: null as string | null
  });

  // Getters
  const filteredUsers = computed(() => {
    let filtered = users.value;

    // Filter by search query
    if (searchQuery.value) {
      const query = searchQuery.value.toLowerCase();
      filtered = filtered.filter(user =>
        user.firstName?.toLowerCase().includes(query) ||
        user.lastName?.toLowerCase().includes(query) ||
        user.email?.toLowerCase().includes(query) ||
        user.fullName?.toLowerCase().includes(query) ||
        user.userName?.toLowerCase().includes(query) ||
        user.idNumber?.toLowerCase().includes(query)
      );
    }

    // Filter by status
    if (statusFilter.value !== 'All') {
      filtered = filtered.filter(user => user.status === statusFilter.value);
    }

    // Filter by role
    if (roleFilter.value !== 'All') {
      filtered = filtered.filter(user =>
        user.roles?.some(role => role.id === roleFilter.value)
      );
    }

    return filtered;
  });

  const usersByStatus = computed(() => {
    return {
      unapproved: users.value.filter(u => u.status === 'Unapproved').length,
      approved: users.value.filter(u => u.status === 'Approved').length,
      secondApproved: users.value.filter(u => u.status === 'SecondApproved').length,
      rejected: users.value.filter(u => u.status === 'Rejected').length,
    };
  });

  const totalUsers = computed(() => users.value.length);
  const activeUsers = computed(() => users.value.filter(u => !u.isDeleted).length);

  const selectedUsersCount = computed(() => bulkOperations.value.selectedUsers.length);

  const canPerformBulkOperations = computed(() =>
    selectedUsersCount.value > 0 && !bulkOperations.value.isProcessing
  );

  // Actions
  const fetchUsers = async (): Promise<void> => {
    try {
      isLoading.value = true;
      error.value = null;
      users.value = await userService.getAllUsers();
      updateStatistics();
    } catch (err: any) {
      error.value = err.message || 'Failed to fetch users';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const fetchUsersPaginated = async (
    page: number = 1,
    limit: number = 10,
    filters?: {
      status?: RecordStatus | 'All';
      searchQuery?: string;
      roleId?: string;
    }
  ): Promise<void> => {
    try {
      isLoading.value = true;
      error.value = null;

      const response = await userService.getUsersPaginated(page, limit, filters);
      users.value = response.users;
      pagination.value = {
        page: response.page,
        limit,
        total: response.total,
        totalPages: response.totalPages
      };
    } catch (err: any) {
      error.value = err.message || 'Failed to fetch users';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const updateStatistics = (): void => {
    try {
      // Calculate statistics from existing users data - no API call needed!
      const stats = userService.calculateUserStatistics(users.value);
      statistics.value = stats;
    } catch (err: any) {
      console.error('Failed to update statistics:', err);
    }
  };

  const fetchUserById = async (id: string): Promise<UserDto> => {
    try {
      isLoading.value = true;
      error.value = null;
      const user = await userService.getUserById(id);
      currentUser.value = user;
      return user;
    } catch (err: any) {
      error.value = err.message || 'Failed to fetch user';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const createUser = async (userData: CreateUserRequest): Promise<UserDto> => {
    try {
      isLoading.value = true;
      error.value = null;
      const newUser = await userService.createUser(userData);
      users.value.push(newUser);
      return newUser;
    } catch (err: any) {
      error.value = err.message || 'Failed to create user';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const updateUser = async (id: string, userData: UpdateUserRequest): Promise<UserDto> => {
    try {
      isLoading.value = true;
      error.value = null;
      const updatedUser = await userService.updateUser(id, userData);

      // Update user in local state
      const index = users.value.findIndex(u => u.id === id);
      if (index !== -1) {
        users.value[index] = updatedUser;
      }

      // Update current user if it's the same
      if (currentUser.value?.id === id) {
        currentUser.value = updatedUser;
      }

      updateStatistics();
      return updatedUser;
    } catch (err: any) {
      error.value = err.message || 'Failed to update user';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };



  const deleteUser = async (id: string): Promise<void> => {
    try {
      isLoading.value = true;
      error.value = null;
      await userService.deleteUser(id);
      
      // Remove user from local state
      users.value = users.value.filter(u => u.id !== id);
      
      // Clear current user if it's the deleted one
      if (currentUser.value?.id === id) {
        currentUser.value = null;
      }
    } catch (err: any) {
      error.value = err.message || 'Failed to delete user';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const updateUserStatus = async (id: string, status: RecordStatus): Promise<UserDto> => {
    try {
      isLoading.value = true;
      error.value = null;
      const updatedUser = await userService.updateUserStatus(id, status);

      // Update user status in local state
      const index = users.value.findIndex(u => u.id === id);
      if (index !== -1) {
        users.value[index] = updatedUser;
      }

      // Update current user if it's the same
      if (currentUser.value?.id === id) {
        currentUser.value = updatedUser;
      }

      updateStatistics();
      return updatedUser;
    } catch (err: any) {
      error.value = err.message || 'Failed to update user status';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const searchUsers = async (query: string): Promise<void> => {
    searchQuery.value = query;
    if (query) {
      try {
        isLoading.value = true;
        error.value = null;
        users.value = await userService.searchUsers(query);
      } catch (err: any) {
        error.value = err.message || 'Failed to search users';
        throw err;
      } finally {
        isLoading.value = false;
      }
    } else {
      await fetchUsers();
    }
  };

  const setStatusFilter = (status: RecordStatus | 'All'): void => {
    statusFilter.value = status;
  };

  const setRoleFilter = (roleId: string | 'All'): void => {
    roleFilter.value = roleId;
  };

  const clearError = (): void => {
    error.value = null;
  };

  const clearCurrentUser = (): void => {
    currentUser.value = null;
  };

  // Bulk Operations
  const toggleUserSelection = (userId: string): void => {
    const index = bulkOperations.value.selectedUsers.indexOf(userId);
    if (index > -1) {
      bulkOperations.value.selectedUsers.splice(index, 1);
    } else {
      bulkOperations.value.selectedUsers.push(userId);
    }
  };

  const selectAllUsers = (): void => {
    bulkOperations.value.selectedUsers = filteredUsers.value.map(u => u.id!).filter(Boolean);
  };

  const clearUserSelection = (): void => {
    bulkOperations.value.selectedUsers = [];
  };

  const bulkUpdateStatus = async (status: RecordStatus): Promise<{
    success: string[];
    failed: { id: string; error: string }[];
  }> => {
    try {
      bulkOperations.value.isProcessing = true;
      bulkOperations.value.lastOperation = `Bulk status update to ${status}`;

      const results = await userService.bulkUpdateUserStatus(
        bulkOperations.value.selectedUsers,
        status
      );

      // Update local state for successful updates
      results.success.forEach(userId => {
        const index = users.value.findIndex(u => u.id === userId);
        if (index !== -1) {
          users.value[index].status = status;
        }
      });

      updateStatistics();
      clearUserSelection();

      return results;
    } catch (err: any) {
      error.value = err.message || 'Failed to perform bulk status update';
      throw err;
    } finally {
      bulkOperations.value.isProcessing = false;
    }
  };

  const addRoleToUser = async (userId: string, roleId: string): Promise<UserDto> => {
    try {
      isLoading.value = true;
      error.value = null;
      const updatedUser = await userService.addRoleToUser(userId, roleId);

      // Update user in local state
      const index = users.value.findIndex(u => u.id === userId);
      if (index !== -1) {
        users.value[index] = updatedUser;
      }

      // Update current user if it's the same
      if (currentUser.value?.id === userId) {
        currentUser.value = updatedUser;
      }

      updateStatistics();
      return updatedUser;
    } catch (err: any) {
      error.value = err.message || 'Failed to add role to user';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const removeRoleFromUser = async (userId: string, roleId: string): Promise<UserDto> => {
    try {
      isLoading.value = true;
      error.value = null;
      const updatedUser = await userService.removeRoleFromUser(userId, roleId);

      // Update user in local state
      const index = users.value.findIndex(u => u.id === userId);
      if (index !== -1) {
        users.value[index] = updatedUser;
      }

      // Update current user if it's the same
      if (currentUser.value?.id === userId) {
        currentUser.value = updatedUser;
      }

      updateStatistics();
      return updatedUser;
    } catch (err: any) {
      error.value = err.message || 'Failed to remove role from user';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const removeAllRolesFromUser = async (userId: string): Promise<UserDto> => {
    try {
      isLoading.value = true;
      error.value = null;
      const updatedUser = await userService.removeAllRolesFromUser(userId);

      // Update user in local state
      const index = users.value.findIndex(u => u.id === userId);
      if (index !== -1) {
        users.value[index] = updatedUser;
      }

      // Update current user if it's the same
      if (currentUser.value?.id === userId) {
        currentUser.value = updatedUser;
      }

      updateStatistics();
      return updatedUser;
    } catch (err: any) {
      error.value = err.message || 'Failed to remove all roles from user';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const checkUserHasRole = async (userId: string, roleId: string): Promise<boolean> => {
    try {
      return await userService.checkUserHasRole(userId, roleId);
    } catch (err: any) {
      error.value = err.message || 'Failed to check user role';
      throw err;
    }
  };

  const getUserRoles = async (userId: string): Promise<RoleDto[]> => {
    try {
      isLoading.value = true;
      error.value = null;
      return await userService.getUserRolesByUserId(userId);
    } catch (err: any) {
      error.value = err.message || 'Failed to get user roles';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const getUsersWithRole = async (roleId: string): Promise<UserDto[]> => {
    try {
      isLoading.value = true;
      error.value = null;
      return await userService.getUsersWithRole(roleId);
    } catch (err: any) {
      error.value = err.message || 'Failed to get users with role';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const validateUserData = (userData: CreateUserRequest | UpdateUserRequest) => {
    return userService.validateUserData(userData);
  };

  return {
    // State
    users,
    currentUser,
    isLoading,
    error,
    searchQuery,
    statusFilter,
    roleFilter,
    statistics,
    pagination,
    bulkOperations,

    // Getters
    filteredUsers,
    usersByStatus,
    totalUsers,
    activeUsers,
    selectedUsersCount,
    canPerformBulkOperations,

    // Actions
    fetchUsers,
    fetchUsersPaginated,
    fetchUserById,
    createUser,
    updateUser,
    deleteUser,
    updateUserStatus,
    searchUsers,
    setStatusFilter,
    setRoleFilter,
    clearError,
    clearCurrentUser,
    updateStatistics,

    // Bulk Operations
    toggleUserSelection,
    selectAllUsers,
    clearUserSelection,
    bulkUpdateStatus,

    // Role Management
    addRoleToUser,
    removeRoleFromUser,
    removeAllRolesFromUser,
    checkUserHasRole,
    getUserRoles,
    getUsersWithRole,

    // Validation
    validateUserData,
  };
});

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { candidateService } from '@/services/data/candidate.service'
import type { ScoreCandidateDto, CandidateDto, CandidateFilters } from '@/services/data/candidate.service'
import { useAuthStore } from '../auth/auth.store'

export const useCandidateStore = defineStore('candidate', () => {
  // State
  const candidates = ref<CandidateDto[]>([])
  const scoreCandidates = ref<ScoreCandidateDto[]>([])
  const candidateCount = ref(0)
  const isLoading = ref(false)
  const isLoadingScoreCandidates = ref(false)
  const error = ref<string | null>(null)

  // Getters
  const getCandidateById = computed(() => {
    return (id: string) => candidates.value.find(candidate => candidate.id === id)
  })

  const getScoreCandidateById = computed(() => {
    return (id: string) => scoreCandidates.value.find(candidate => candidate.id === id)
  })

  const hasError = computed(() => !!error.value)

  // Actions
  const clearError = () => {
    error.value = null
  }

  const clearCandidates = () => {
    candidates.value = []
    candidateCount.value = 0
  }

  const clearScoreCandidates = () => {
    scoreCandidates.value = []
  }

  /**
   * Fetch regular candidates with filtering
   */
  const fetchCandidates = async (filters: CandidateFilters = {}) => {
    isLoading.value = true
    error.value = null
    
    try {
      const authStore = useAuthStore()
      if (!authStore.isAuthenticated) {
        throw new Error('Authentication required')
      }

      console.log('🔍 Fetching candidates with filters:', filters)
      const response = await candidateService.fetchCandidates(filters)
      
      candidates.value = response.items
      candidateCount.value = response.totalCount
      
      console.log('✅ Candidates loaded:', response.totalCount, 'items')
      return response
    } catch (err: any) {
      error.value = err.message || 'Failed to fetch candidates'
      console.error('❌ Error fetching candidates:', err)
      throw err
    } finally {
      isLoading.value = false
    }
  }

  /**
   * Fetch score candidates (for score entry)
   */
  const fetchScoreCandidates = async (filters: {
    schoolId?: string;
    centerNo?: string;
    examYear?: number;
    subjectId?: string;
    examTypeId?: string;
    search?: string;
  }) => {
    isLoadingScoreCandidates.value = true
    error.value = null
    
    try {
      const authStore = useAuthStore()
      if (!authStore.isAuthenticated) {
        throw new Error('Authentication required')
      }

      console.log('🔍 Fetching score candidates with filters:', filters)
      const response = await candidateService.fetchScoreCandidates(filters)
      
      scoreCandidates.value = response
      
      console.log('✅ Score candidates loaded:', response.length, 'items')
      return response
    } catch (err: any) {
      error.value = err.message || 'Failed to fetch score candidates'
      console.error('❌ Error fetching score candidates:', err)
      throw err
    } finally {
      isLoadingScoreCandidates.value = false
    }
  }

  /**
   * Search candidates by query
   */
  const searchCandidates = async (query: string, filters: Omit<CandidateFilters, 'search'> = {}) => {
    try {
      console.log('🔍 Searching candidates with query:', query)
      const candidates = await candidateService.searchCandidates(query, filters)
      console.log('✅ Search results:', candidates.length, 'candidates')
      return candidates
    } catch (err: any) {
      error.value = err.message || 'Failed to search candidates'
      console.error('❌ Error searching candidates:', err)
      throw err
    }
  }

  /**
   * Get candidate options for dropdowns
   */
  const getCandidateOptions = async (filters: CandidateFilters = {}) => {
    try {
      console.log('🔍 Getting candidate options with filters:', filters)
      const options = await candidateService.getCandidateOptions(filters)
      console.log('✅ Candidate options loaded:', options.length, 'options')
      return options
    } catch (err: any) {
      error.value = err.message || 'Failed to get candidate options'
      console.error('❌ Error getting candidate options:', err)
      throw err
    }
  }

  /**
   * Reset store state
   */
  const resetStore = () => {
    candidates.value = []
    scoreCandidates.value = []
    candidateCount.value = 0
    isLoading.value = false
    isLoadingScoreCandidates.value = false
    error.value = null
  }

  return {
    // State
    candidates,
    scoreCandidates,
    candidateCount,
    isLoading,
    isLoadingScoreCandidates,
    error,
    
    // Getters
    getCandidateById,
    getScoreCandidateById,
    hasError,
    
    // Actions
    clearError,
    clearCandidates,
    clearScoreCandidates,
    fetchCandidates,
    fetchScoreCandidates,
    searchCandidates,
    getCandidateOptions,
    resetStore
  }
})

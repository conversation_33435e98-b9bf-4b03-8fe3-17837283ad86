import { ref, computed, watch } from 'vue';
import { dropdownDataService, type DropdownOption, type Division, type District, type Center, type Subject } from '@/services/dropdown-data.service';

/**
 * Filter option interface for UI components
 */
export interface FilterOption {
  id: string;
  name: string;
  parentId?: string;
}

/**
 * Dropdown data composable for reactive dropdown management
 */
export function useDropdownData() {
  // Reactive state
  const selectedDivision = ref<string>('');
  const selectedDistrict = ref<string>('');
  const selectedCenter = ref<string>('');
  const selectedExamLevel = ref<string>('');
  const searchQuery = ref<string>('');

  /**
   * Get all divisions as filter options
   */
  const divisions = computed((): FilterOption[] => {
    const divisionData = dropdownDataService.getDivisions();
    return dropdownDataService.toDropdownOptions(divisionData);
  });

  /**
   * Get districts filtered by selected division
   */
  const districts = computed((): FilterOption[] => {
    if (!selectedDivision.value) {
      return dropdownDataService.toDropdownOptions(dropdownDataService.getDistricts());
    }
    
    const districtData = dropdownDataService.getDistrictsByDivision(selectedDivision.value);
    return dropdownDataService.toDropdownOptions(districtData);
  });

  /**
   * Get centers filtered by selected district
   */
  const centers = computed((): FilterOption[] => {
    if (!selectedDistrict.value) {
      return dropdownDataService.toDropdownOptions(dropdownDataService.getCenters());
    }
    
    const centerData = dropdownDataService.getCentersByDistrict(selectedDistrict.value);
    return dropdownDataService.toDropdownOptions(centerData);
  });

  /**
   * Get subjects filtered by selected exam level
   */
  const subjects = computed((): FilterOption[] => {
    if (!selectedExamLevel.value) {
      return dropdownDataService.toDropdownOptions(dropdownDataService.getSubjects());
    }
    
    const subjectData = dropdownDataService.getSubjectsByExamLevel(selectedExamLevel.value);
    return dropdownDataService.toDropdownOptions(subjectData);
  });

  /**
   * Get core subjects for selected exam level
   */
  const coreSubjects = computed((): FilterOption[] => {
    if (!selectedExamLevel.value) {
      return [];
    }
    
    const coreSubjectData = dropdownDataService.getCoreSubjects(selectedExamLevel.value);
    return dropdownDataService.toDropdownOptions(coreSubjectData);
  });

  /**
   * Get filtered subjects based on search query
   */
  const filteredSubjects = computed((): FilterOption[] => {
    const baseSubjects = subjects.value;
    
    if (!searchQuery.value) {
      return baseSubjects;
    }
    
    const searchTerm = searchQuery.value.toLowerCase();
    return baseSubjects.filter(subject => 
      subject.name.toLowerCase().includes(searchTerm)
    );
  });

  /**
   * Reset all selections
   */
  const resetSelections = () => {
    selectedDivision.value = '';
    selectedDistrict.value = '';
    selectedCenter.value = '';
    selectedExamLevel.value = '';
    searchQuery.value = '';
  };

  /**
   * Reset dependent selections when parent changes
   */
  watch(selectedDivision, () => {
    selectedDistrict.value = '';
    selectedCenter.value = '';
  });

  watch(selectedDistrict, () => {
    selectedCenter.value = '';
  });

  /**
   * Get hierarchical path for selected items
   */
  const getSelectionPath = computed(() => {
    const path: string[] = [];
    
    if (selectedDivision.value) {
      const division = dropdownDataService.getDivisionById(selectedDivision.value);
      if (division) path.push(division.name);
    }
    
    if (selectedDistrict.value) {
      const district = dropdownDataService.getDistrictById(selectedDistrict.value);
      if (district) path.push(district.name);
    }
    
    if (selectedCenter.value) {
      const center = dropdownDataService.getCenterById(selectedCenter.value);
      if (center) path.push(center.name);
    }
    
    return path;
  });

  /**
   * Get selection summary
   */
  const selectionSummary = computed(() => {
    const summary: Record<string, string> = {};
    
    if (selectedDivision.value) {
      const division = dropdownDataService.getDivisionById(selectedDivision.value);
      summary.division = division?.name || '';
    }
    
    if (selectedDistrict.value) {
      const district = dropdownDataService.getDistrictById(selectedDistrict.value);
      summary.district = district?.name || '';
    }
    
    if (selectedCenter.value) {
      const center = dropdownDataService.getCenterById(selectedCenter.value);
      summary.center = center?.name || '';
    }
    
    if (selectedExamLevel.value) {
      summary.examLevel = selectedExamLevel.value;
    }
    
    return summary;
  });

  /**
   * Validate current selections
   */
  const validateSelections = computed(() => {
    const errors: string[] = [];
    
    if (selectedDistrict.value && !selectedDivision.value) {
      errors.push('Division must be selected when district is selected');
    }
    
    if (selectedCenter.value && !selectedDistrict.value) {
      errors.push('District must be selected when center is selected');
    }
    
    return {
      isValid: errors.length === 0,
      errors
    };
  });

  /**
   * Get available options count
   */
  const optionCounts = computed(() => ({
    divisions: divisions.value.length,
    districts: districts.value.length,
    centers: centers.value.length,
    subjects: subjects.value.length,
    coreSubjects: coreSubjects.value.length
  }));

  /**
   * Utility functions for specific use cases
   */
  const utils = {
    /**
     * Get subject by code
     */
    getSubjectByCode: (code: string) => {
      return dropdownDataService.getSubjectByCode(code);
    },

    /**
     * Check if exam level has subjects
     */
    hasSubjectsForExamLevel: (examLevel: string) => {
      return dropdownDataService.getSubjectsByExamLevel(examLevel).length > 0;
    },

    /**
     * Get district name by ID
     */
    getDistrictName: (id: string) => {
      return dropdownDataService.getDistrictById(id)?.name || '';
    },

    /**
     * Get center name by ID
     */
    getCenterName: (id: string) => {
      return dropdownDataService.getCenterById(id)?.name || '';
    },

    /**
     * Get subject name by ID
     */
    getSubjectName: (id: string) => {
      return dropdownDataService.getSubjectById(id)?.name || '';
    },

    /**
     * Format selection for display
     */
    formatSelectionDisplay: () => {
      const path = getSelectionPath.value;
      return path.length > 0 ? path.join(' > ') : 'No selection';
    }
  };

  return {
    // Reactive state
    selectedDivision,
    selectedDistrict,
    selectedCenter,
    selectedExamLevel,
    searchQuery,

    // Computed data
    divisions,
    districts,
    centers,
    subjects,
    coreSubjects,
    filteredSubjects,
    selectionPath: getSelectionPath,
    selectionSummary,
    validateSelections,
    optionCounts,

    // Methods
    resetSelections,
    utils,

    // Direct service access for advanced use cases
    service: dropdownDataService
  };
}

/**
 * Specialized composable for exam-level specific dropdowns
 */
export function useExamLevelDropdowns(examLevel: string) {
  const baseDropdowns = useDropdownData();
  
  // Set the exam level
  baseDropdowns.selectedExamLevel.value = examLevel;
  
  return {
    ...baseDropdowns,
    examLevel,
    // Override subjects to be exam-level specific
    subjects: computed(() => {
      const subjectData = dropdownDataService.getSubjectsByExamLevel(examLevel);
      return dropdownDataService.toDropdownOptions(subjectData);
    }),
    coreSubjects: computed(() => {
      const coreSubjectData = dropdownDataService.getCoreSubjects(examLevel);
      return dropdownDataService.toDropdownOptions(coreSubjectData);
    })
  };
}

/**
 * Specialized composable for filtering stage components
 */
export function useFilteringDropdowns() {
  const dropdowns = useDropdownData();
  
  // Additional filtering-specific functionality
  const applyFilters = (filters: {
    division?: string;
    district?: string;
    center?: string;
    examLevel?: string;
  }) => {
    if (filters.division) dropdowns.selectedDivision.value = filters.division;
    if (filters.district) dropdowns.selectedDistrict.value = filters.district;
    if (filters.center) dropdowns.selectedCenter.value = filters.center;
    if (filters.examLevel) dropdowns.selectedExamLevel.value = filters.examLevel;
  };
  
  const getFilterState = () => ({
    division: dropdowns.selectedDivision.value,
    district: dropdowns.selectedDistrict.value,
    center: dropdowns.selectedCenter.value,
    examLevel: dropdowns.selectedExamLevel.value
  });
  
  return {
    ...dropdowns,
    applyFilters,
    getFilterState
  };
}

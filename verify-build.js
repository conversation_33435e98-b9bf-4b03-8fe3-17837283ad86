import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const distPath = path.join(__dirname, 'dist');
const requiredFiles = [
  'index.html',
  'assets',
];

const optionalConfigFiles = [
  '.htaccess',
  '_redirects',
  'web.config'
];

if (!fs.existsSync(distPath)) {
  process.exit(1);
}

let allRequiredFilesExist = true;
requiredFiles.forEach(file => {
  const filePath = path.join(distPath, file);
  if (!fs.existsSync(filePath)) {
    allRequiredFilesExist = false;
  }
});

optionalConfigFiles.forEach(file => {
  const filePath = path.join(distPath, file);
});

const indexPath = path.join(distPath, 'index.html');
if (fs.existsSync(indexPath)) {
  const indexContent = fs.readFileSync(indexPath, 'utf8');

  if (!indexContent.includes('id="app"')) {
    allRequiredFilesExist = false;
  }

  if (!(indexContent.includes('<script') && indexContent.includes('.js'))) {
    allRequiredFilesExist = false;
  }
}

if (!allRequiredFilesExist) {
  process.exit(1);
}

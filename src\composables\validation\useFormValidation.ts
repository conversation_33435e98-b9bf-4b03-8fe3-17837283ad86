import { reactive, ref, computed } from 'vue';

/**
 * Validation rule function type
 */
export type ValidationRule<T = any> = (value: T, formData?: Record<string, any>) => string | null;

/**
 * Field validation configuration
 */
export interface FieldValidation<T = any> {
  /** Field name */
  field: string;
  /** Validation rules for this field */
  rules: ValidationRule<T>[];
  /** Whether to validate on change (default: false) */
  validateOnChange?: boolean;
}

/**
 * Form validation configuration
 */
export interface FormValidationConfig {
  /** Field validations */
  fields: FieldValidation[];
  /** Whether to validate all fields on submit (default: true) */
  validateOnSubmit?: boolean;
  /** Whether to stop validation on first error (default: false) */
  stopOnFirstError?: boolean;
}

/**
 * Validation result
 */
export interface ValidationResult {
  /** Whether the validation passed */
  isValid: boolean;
  /** Error messages by field */
  errors: Record<string, string>;
  /** Warning messages by field */
  warnings: Record<string, string>;
  /** First error message (if any) */
  firstError: string | null;
}

/**
 * Common validation rules
 */
export const ValidationRules = {
  /**
   * Required field validation
   */
  required: (message = 'This field is required'): ValidationRule<string> => {
    return (value: string) => {
      if (!value || !value.toString().trim()) {
        return message;
      }
      return null;
    };
  },

  /**
   * Email validation
   */
  email: (message = 'Please enter a valid email address'): ValidationRule<string> => {
    return (value: string) => {
      if (!value) return null; // Let required rule handle empty values
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(value)) {
        return message;
      }
      return null;
    };
  },

  /**
   * Minimum length validation
   */
  minLength: (min: number, message?: string): ValidationRule<string> => {
    return (value: string) => {
      if (!value) return null; // Let required rule handle empty values
      if (value.length < min) {
        return message || `Must be at least ${min} characters`;
      }
      return null;
    };
  },

  /**
   * Maximum length validation
   */
  maxLength: (max: number, message?: string): ValidationRule<string> => {
    return (value: string) => {
      if (!value) return null;
      if (value.length > max) {
        return message || `Must be no more than ${max} characters`;
      }
      return null;
    };
  },

  /**
   * Password confirmation validation
   */
  confirmPassword: (passwordField = 'password', message = 'Passwords do not match'): ValidationRule<string> => {
    return (value: string, formData?: Record<string, any>) => {
      if (!value) return null;
      if (!formData || value !== formData[passwordField]) {
        return message;
      }
      return null;
    };
  },

  /**
   * Numeric validation
   */
  numeric: (message = 'Must be a valid number'): ValidationRule<string | number> => {
    return (value: string | number) => {
      if (!value && value !== 0) return null;
      if (isNaN(Number(value))) {
        return message;
      }
      return null;
    };
  },

  /**
   * Minimum value validation
   */
  min: (min: number, message?: string): ValidationRule<string | number> => {
    return (value: string | number) => {
      if (!value && value !== 0) return null;
      const numValue = Number(value);
      if (numValue < min) {
        return message || `Must be at least ${min}`;
      }
      return null;
    };
  },

  /**
   * Maximum value validation
   */
  max: (max: number, message?: string): ValidationRule<string | number> => {
    return (value: string | number) => {
      if (!value && value !== 0) return null;
      const numValue = Number(value);
      if (numValue > max) {
        return message || `Must be no more than ${max}`;
      }
      return null;
    };
  },

  /**
   * Custom validation rule
   */
  custom: (validator: (value: any, formData?: Record<string, any>) => boolean, message: string): ValidationRule => {
    return (value: any, formData?: Record<string, any>) => {
      if (!validator(value, formData)) {
        return message;
      }
      return null;
    };
  }
};

/**
 * Form validation composable
 */
export function useFormValidation(config: FormValidationConfig) {
  const errors = reactive<Record<string, string>>({});
  const warnings = reactive<Record<string, string>>({});
  const isValidating = ref(false);

  /**
   * Clear all errors and warnings
   */
  const clearValidation = () => {
    Object.keys(errors).forEach(key => delete errors[key]);
    Object.keys(warnings).forEach(key => delete warnings[key]);
  };

  /**
   * Clear validation for specific field
   */
  const clearFieldValidation = (field: string) => {
    delete errors[field];
    delete warnings[field];
  };

  /**
   * Validate a single field
   */
  const validateField = (field: string, value: any, formData?: Record<string, any>): string | null => {
    const fieldConfig = config.fields.find(f => f.field === field);
    if (!fieldConfig) return null;

    clearFieldValidation(field);

    for (const rule of fieldConfig.rules) {
      const error = rule(value, formData);
      if (error) {
        errors[field] = error;
        return error;
      }
    }

    return null;
  };

  /**
   * Validate all fields
   */
  const validateForm = (formData: Record<string, any>): ValidationResult => {
    isValidating.value = true;
    clearValidation();

    let isValid = true;
    let firstError: string | null = null;

    for (const fieldConfig of config.fields) {
      const value = formData[fieldConfig.field];
      const error = validateField(fieldConfig.field, value, formData);
      
      if (error) {
        isValid = false;
        if (!firstError) {
          firstError = error;
        }
        
        if (config.stopOnFirstError) {
          break;
        }
      }
    }

    isValidating.value = false;

    return {
      isValid,
      errors: { ...errors },
      warnings: { ...warnings },
      firstError
    };
  };

  /**
   * Check if form has any errors
   */
  const hasErrors = computed(() => Object.keys(errors).length > 0);

  /**
   * Check if form has any warnings
   */
  const hasWarnings = computed(() => Object.keys(warnings).length > 0);

  /**
   * Get error for specific field
   */
  const getFieldError = (field: string): string | undefined => errors[field];

  /**
   * Get warning for specific field
   */
  const getFieldWarning = (field: string): string | undefined => warnings[field];

  /**
   * Check if specific field has error
   */
  const hasFieldError = (field: string): boolean => !!errors[field];

  /**
   * Check if specific field has warning
   */
  const hasFieldWarning = (field: string): boolean => !!warnings[field];

  /**
   * Add custom error to field
   */
  const setFieldError = (field: string, message: string) => {
    errors[field] = message;
  };

  /**
   * Add custom warning to field
   */
  const setFieldWarning = (field: string, message: string) => {
    warnings[field] = message;
  };

  return {
    // State
    errors,
    warnings,
    isValidating,
    
    // Computed
    hasErrors,
    hasWarnings,
    
    // Methods
    validateForm,
    validateField,
    clearValidation,
    clearFieldValidation,
    getFieldError,
    getFieldWarning,
    hasFieldError,
    hasFieldWarning,
    setFieldError,
    setFieldWarning
  };
}

/**
 * @fileoverview RBAC Permission System for MANEB Results Management System
 * @description Defines permissions, roles, and authorization utilities
 */

// ============================================================================
// PERMISSION DEFINITIONS
// ============================================================================

/**
 * Available actions in the system
 */
export enum PermissionAction {
  VIEW = 'view',
  CREATE = 'create',
  EDIT = 'edit',
  DELETE = 'delete',
  APPROVE = 'approve',
  EXPORT = 'export',
  IMPORT = 'import',
  MANAGE = 'manage'
}

/**
 * System modules/sections
 */
export enum PermissionModule {
  DASHBOARD = 'dashboard',
  SCORE_ENTRY = 'score_entry',
  RESULTS_MANAGEMENT = 'results_management',
  USER_MANAGEMENT = 'user_management',
  GRADING = 'grading',
  REPORTS = 'reports',
  AUDIT_LOGS = 'audit_logs',
  SYSTEM_SETTINGS = 'system_settings'
}

/**
 * Permission interface
 */
export interface Permission {
  module: PermissionModule;
  action: PermissionAction;
  description?: string;
}

/**
 * Role interface
 */
export interface Role {
  name: string;
  displayName: string;
  permissions: Permission[];
  description?: string;
}

// ============================================================================
// ROLE DEFINITIONS
// ============================================================================

/**
 * Data Clerk Role - Limited access to score entry only
 */
const DATA_CLERK_PERMISSIONS: Permission[] = [
  { module: PermissionModule.DASHBOARD, action: PermissionAction.VIEW },
  { module: PermissionModule.SCORE_ENTRY, action: PermissionAction.VIEW },
  { module: PermissionModule.SCORE_ENTRY, action: PermissionAction.CREATE },
  { module: PermissionModule.SCORE_ENTRY, action: PermissionAction.EDIT }
];

/**
 * System Administrator Role - Complete system access with all permissions
 */
const SYSTEM_ADMIN_PERMISSIONS: Permission[] = [
  // Dashboard - Full access
  { module: PermissionModule.DASHBOARD, action: PermissionAction.VIEW },
  { module: PermissionModule.DASHBOARD, action: PermissionAction.MANAGE },

  // Score Entry - Full access
  { module: PermissionModule.SCORE_ENTRY, action: PermissionAction.VIEW },
  { module: PermissionModule.SCORE_ENTRY, action: PermissionAction.CREATE },
  { module: PermissionModule.SCORE_ENTRY, action: PermissionAction.EDIT },
  { module: PermissionModule.SCORE_ENTRY, action: PermissionAction.DELETE },
  { module: PermissionModule.SCORE_ENTRY, action: PermissionAction.APPROVE },
  { module: PermissionModule.SCORE_ENTRY, action: PermissionAction.EXPORT },
  { module: PermissionModule.SCORE_ENTRY, action: PermissionAction.IMPORT },

  // Results Management - Full access
  { module: PermissionModule.RESULTS_MANAGEMENT, action: PermissionAction.VIEW },
  { module: PermissionModule.RESULTS_MANAGEMENT, action: PermissionAction.CREATE },
  { module: PermissionModule.RESULTS_MANAGEMENT, action: PermissionAction.EDIT },
  { module: PermissionModule.RESULTS_MANAGEMENT, action: PermissionAction.DELETE },
  { module: PermissionModule.RESULTS_MANAGEMENT, action: PermissionAction.APPROVE },
  { module: PermissionModule.RESULTS_MANAGEMENT, action: PermissionAction.EXPORT },
  { module: PermissionModule.RESULTS_MANAGEMENT, action: PermissionAction.IMPORT },
  { module: PermissionModule.RESULTS_MANAGEMENT, action: PermissionAction.MANAGE },

  // User Management - Full access
  { module: PermissionModule.USER_MANAGEMENT, action: PermissionAction.VIEW },
  { module: PermissionModule.USER_MANAGEMENT, action: PermissionAction.CREATE },
  { module: PermissionModule.USER_MANAGEMENT, action: PermissionAction.EDIT },
  { module: PermissionModule.USER_MANAGEMENT, action: PermissionAction.DELETE },
  { module: PermissionModule.USER_MANAGEMENT, action: PermissionAction.APPROVE },
  { module: PermissionModule.USER_MANAGEMENT, action: PermissionAction.MANAGE },

  // Grading - Full access
  { module: PermissionModule.GRADING, action: PermissionAction.VIEW },
  { module: PermissionModule.GRADING, action: PermissionAction.CREATE },
  { module: PermissionModule.GRADING, action: PermissionAction.EDIT },
  { module: PermissionModule.GRADING, action: PermissionAction.DELETE },
  { module: PermissionModule.GRADING, action: PermissionAction.APPROVE },
  { module: PermissionModule.GRADING, action: PermissionAction.EXPORT },
  { module: PermissionModule.GRADING, action: PermissionAction.IMPORT },
  { module: PermissionModule.GRADING, action: PermissionAction.MANAGE },

  // Reports - Full access
  { module: PermissionModule.REPORTS, action: PermissionAction.VIEW },
  { module: PermissionModule.REPORTS, action: PermissionAction.CREATE },
  { module: PermissionModule.REPORTS, action: PermissionAction.EDIT },
  { module: PermissionModule.REPORTS, action: PermissionAction.DELETE },
  { module: PermissionModule.REPORTS, action: PermissionAction.EXPORT },
  { module: PermissionModule.REPORTS, action: PermissionAction.MANAGE },

  // Audit Logs - Full access
  { module: PermissionModule.AUDIT_LOGS, action: PermissionAction.VIEW },
  { module: PermissionModule.AUDIT_LOGS, action: PermissionAction.EXPORT },
  { module: PermissionModule.AUDIT_LOGS, action: PermissionAction.MANAGE },

  // System Settings - Full access
  { module: PermissionModule.SYSTEM_SETTINGS, action: PermissionAction.VIEW },
  { module: PermissionModule.SYSTEM_SETTINGS, action: PermissionAction.CREATE },
  { module: PermissionModule.SYSTEM_SETTINGS, action: PermissionAction.EDIT },
  { module: PermissionModule.SYSTEM_SETTINGS, action: PermissionAction.DELETE },
  { module: PermissionModule.SYSTEM_SETTINGS, action: PermissionAction.MANAGE }
];

/**
 * Admin Role - Standard administrative access
 */
const ADMIN_PERMISSIONS: Permission[] = [
  // Dashboard
  { module: PermissionModule.DASHBOARD, action: PermissionAction.VIEW },
  { module: PermissionModule.DASHBOARD, action: PermissionAction.MANAGE },

  // Score Entry
  { module: PermissionModule.SCORE_ENTRY, action: PermissionAction.VIEW },
  { module: PermissionModule.SCORE_ENTRY, action: PermissionAction.CREATE },
  { module: PermissionModule.SCORE_ENTRY, action: PermissionAction.EDIT },
  { module: PermissionModule.SCORE_ENTRY, action: PermissionAction.DELETE },
  { module: PermissionModule.SCORE_ENTRY, action: PermissionAction.APPROVE },

  // Results Management
  { module: PermissionModule.RESULTS_MANAGEMENT, action: PermissionAction.VIEW },
  { module: PermissionModule.RESULTS_MANAGEMENT, action: PermissionAction.CREATE },
  { module: PermissionModule.RESULTS_MANAGEMENT, action: PermissionAction.EDIT },
  { module: PermissionModule.RESULTS_MANAGEMENT, action: PermissionAction.DELETE },
  { module: PermissionModule.RESULTS_MANAGEMENT, action: PermissionAction.EXPORT },

  // User Management
  { module: PermissionModule.USER_MANAGEMENT, action: PermissionAction.VIEW },
  { module: PermissionModule.USER_MANAGEMENT, action: PermissionAction.CREATE },
  { module: PermissionModule.USER_MANAGEMENT, action: PermissionAction.EDIT },
  { module: PermissionModule.USER_MANAGEMENT, action: PermissionAction.DELETE },
  { module: PermissionModule.USER_MANAGEMENT, action: PermissionAction.MANAGE },

  // Grading
  { module: PermissionModule.GRADING, action: PermissionAction.VIEW },
  { module: PermissionModule.GRADING, action: PermissionAction.CREATE },
  { module: PermissionModule.GRADING, action: PermissionAction.EDIT },
  { module: PermissionModule.GRADING, action: PermissionAction.DELETE },
  { module: PermissionModule.GRADING, action: PermissionAction.APPROVE },

  // Reports
  { module: PermissionModule.REPORTS, action: PermissionAction.VIEW },
  { module: PermissionModule.REPORTS, action: PermissionAction.CREATE },
  { module: PermissionModule.REPORTS, action: PermissionAction.EXPORT },

  // Audit Logs
  { module: PermissionModule.AUDIT_LOGS, action: PermissionAction.VIEW },

  // System Settings
  { module: PermissionModule.SYSTEM_SETTINGS, action: PermissionAction.VIEW },
  { module: PermissionModule.SYSTEM_SETTINGS, action: PermissionAction.EDIT },
  { module: PermissionModule.SYSTEM_SETTINGS, action: PermissionAction.MANAGE }
];

/**
 * User Role - Basic access
 */
const USER_PERMISSIONS: Permission[] = [
  { module: PermissionModule.DASHBOARD, action: PermissionAction.VIEW },
  { module: PermissionModule.RESULTS_MANAGEMENT, action: PermissionAction.VIEW }
];

/**
 * System roles configuration
 */
export const SYSTEM_ROLES: Record<string, Role> = {
  'system_administrator': {
    name: 'system_administrator',
    displayName: 'System Administrator',
    permissions: SYSTEM_ADMIN_PERMISSIONS,
    description: 'Complete system access with all administrative privileges'
  },
  'systemadministrator': {
    name: 'systemadministrator',
    displayName: 'System Administrator',
    permissions: SYSTEM_ADMIN_PERMISSIONS,
    description: 'Complete system access with all administrative privileges'
  },
  'sysadmin': {
    name: 'sysadmin',
    displayName: 'System Administrator',
    permissions: SYSTEM_ADMIN_PERMISSIONS,
    description: 'Complete system access with all administrative privileges'
  },
  'sys_admin': {
    name: 'sys_admin',
    displayName: 'System Administrator',
    permissions: SYSTEM_ADMIN_PERMISSIONS,
    description: 'Complete system access with all administrative privileges'
  },
  'sys admin': {
    name: 'sys admin',
    displayName: 'System Administrator',
    permissions: SYSTEM_ADMIN_PERMISSIONS,
    description: 'Complete system access with all administrative privileges'
  },

  'admin': {
    name: 'admin',
    displayName: 'Administrator',
    permissions: ADMIN_PERMISSIONS,
    description: 'Standard administrative access with most permissions'
  },
  'administrator': {
    name: 'administrator',
    displayName: 'Administrator',
    permissions: ADMIN_PERMISSIONS,
    description: 'Standard administrative access with most permissions'
  },
  'data_clerk': {
    name: 'data_clerk',
    displayName: 'Data Clerk',
    permissions: DATA_CLERK_PERMISSIONS,
    description: 'Limited access to score entry functionality'
  },
  'dataclerk': {
    name: 'dataclerk',
    displayName: 'Data Clerk',
    permissions: DATA_CLERK_PERMISSIONS,
    description: 'Limited access to score entry functionality'
  },
  'clerk': {
    name: 'clerk',
    displayName: 'Data Clerk',
    permissions: DATA_CLERK_PERMISSIONS,
    description: 'Limited access to score entry functionality'
  },
  'user': {
    name: 'user',
    displayName: 'User',
    permissions: USER_PERMISSIONS,
    description: 'Basic user access'
  },
  'unknown': {
    name: 'unknown',
    displayName: 'Unknown',
    permissions: [],
    description: 'No permissions assigned'
  }
};

// ============================================================================
// PERMISSION UTILITIES
// ============================================================================

/**
 * Get role configuration by role name
 */
export function getRoleConfig(roleName: string): Role | null {
  const normalizedRoleName = roleName.toLowerCase().trim();
  return SYSTEM_ROLES[normalizedRoleName] || null;
}

/**
 * Get all permissions for a role
 */
export function getRolePermissions(roleName: string): Permission[] {
  const role = getRoleConfig(roleName);
  return role ? role.permissions : [];
}

/**
 * Get permissions for a role with dynamic fallback
 * This function first checks static roles, then falls back to dynamic permissions
 */
export function getRolePermissionsWithDynamic(roleName: string, dynamicPermissions?: Permission[]): Permission[] {
  // First check static roles
  const staticPermissions = getRolePermissions(roleName);
  if (staticPermissions.length > 0) {
    return staticPermissions;
  }

  // Fall back to dynamic permissions if provided
  return dynamicPermissions || [];
}

/**
 * Check if a role has a specific permission
 */
export function hasPermission(
  roleName: string, 
  module: PermissionModule, 
  action: PermissionAction
): boolean {
  const permissions = getRolePermissions(roleName);
  return permissions.some(p => p.module === module && p.action === action);
}

/**
 * Check if a role has access to a module (any action)
 */
export function hasModuleAccess(roleName: string, module: PermissionModule): boolean {
  const permissions = getRolePermissions(roleName);
  return permissions.some(p => p.module === module);
}

/**
 * Get all modules a role has access to
 */
export function getAccessibleModules(roleName: string): PermissionModule[] {
  const permissions = getRolePermissions(roleName);
  const modules = new Set<PermissionModule>();
  
  permissions.forEach(p => modules.add(p.module));
  return Array.from(modules);
}

/**
 * Get all actions a role can perform on a specific module
 */
export function getModuleActions(roleName: string, module: PermissionModule): PermissionAction[] {
  const permissions = getRolePermissions(roleName);
  return permissions
    .filter(p => p.module === module)
    .map(p => p.action);
}

/**
 * Check if a role can perform any of the specified actions on a module
 */
export function canPerformAnyAction(
  roleName: string, 
  module: PermissionModule, 
  actions: PermissionAction[]
): boolean {
  return actions.some(action => hasPermission(roleName, module, action));
}

/**
 * Check if a role can perform all of the specified actions on a module
 */
export function canPerformAllActions(
  roleName: string, 
  module: PermissionModule, 
  actions: PermissionAction[]
): boolean {
  return actions.every(action => hasPermission(roleName, module, action));
}

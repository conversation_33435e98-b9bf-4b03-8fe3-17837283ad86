/**
 * @fileoverview Center Service for MANEB Results Management System
 * @description Service for fetching centers from the API
 */

import apiClient from '../core/api-client';

/**
 * Center interface matching the API response
 */
export interface CenterDto {
  id: string;
  manebcentreNo: string;
  schoolId: string;
  candidateEntryTypeId: string;
  examTypeId: string;
  createdOn: string;
  createdBy: string;
  modifiedOn: string | null;
  modifiedBy: string | null;
  emiscentreNumber: string | null;
  manebmsceCentreNo: string | null;
  isActive: boolean;
  candidateEntryType: any | null;
  examType: any | null;
  school: any | null;
}

/**
 * API Response interface for paginated centers
 */
export interface CenterResponse {
  totalCount: number;
  pageNumber: number;
  pageSize: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
  items: CenterDto[];
}

/**
 * Simplified center option for dropdowns
 */
export interface CenterOption {
  id: string;
  name: string;
  centerNumber: string;
  examTypeId: string;
  schoolId: string;
  isActive: boolean;
}

/**
 * Center search filters
 */
export interface CenterFilters {
  examTypeId?: string;
  schoolId?: string;
  search?: string;
  activeOnly?: boolean;
}

/**
 * Center Service
 * @description Handles all center related API operations
 */
export class CenterService {
  private static instance: CenterService;
  private cachedCenters: CenterDto[] | null = null;
  private lastFetchTime: number = 0;
  private readonly CACHE_DURATION = 10 * 60 * 1000; // 10 minutes

  private constructor() {}

  /**
   * Get singleton instance
   */
  static getInstance(): CenterService {
    if (!CenterService.instance) {
      CenterService.instance = new CenterService();
    }
    return CenterService.instance;
  }

  /**
   * Fetch centers from API with optional filtering
   */
  async fetchCenters(options: {
    forceRefresh?: boolean;
    pageNumber?: number;
    pageSize?: number;
    search?: string;
  } = {}): Promise<CenterDto[]> {
    const { forceRefresh = false, pageNumber, pageSize, search } = options;
    const now = Date.now();

    // Create cache key based on parameters
    const cacheKey = `centers_${pageNumber || 'all'}_${pageSize || 'default'}_${search || 'none'}`;

    // Return cached data if available and not expired (only for non-parameterized requests)
    if (!forceRefresh && !pageNumber && !pageSize && !search &&
        this.cachedCenters && (now - this.lastFetchTime) < this.CACHE_DURATION) {
      return this.cachedCenters;
    }

    try {
      // Build query parameters
      const params = new URLSearchParams();
      if (pageNumber) params.append('PageNumber', pageNumber.toString());
      if (pageSize) params.append('PageSize', pageSize.toString());
      if (search) params.append('Search', search);

      const url = params.toString() ? `/api/centre?${params.toString()}` : '/api/centre';
      const response: CenterResponse = await apiClient.get(url);

      // Cache only if it's a general request (no specific filters)
      if (!pageNumber && !pageSize && !search) {
        this.cachedCenters = response.items;
        this.lastFetchTime = now;
      }

      return response.items;
    } catch (error) {
      console.error('Error fetching centers:', error);

      // Return cached data if available, even if expired (only for general requests)
      if (!pageNumber && !pageSize && !search && this.cachedCenters) {
        console.warn('Using cached centers due to API error');
        return this.cachedCenters;
      }

      throw error;
    }
  }

  /**
   * Get active centers only
   */
  async getActiveCenters(): Promise<CenterDto[]> {
    const centers = await this.fetchCenters();
    return centers.filter(center => center.isActive);
  }

  /**
   * Get centers filtered by school ID and exam type ID
   */
  async getCentersBySchoolAndExamType(schoolId: string, examTypeId: string): Promise<CenterDto[]> {
    const centers = await this.fetchCenters();
    return centers.filter(center =>
      center.schoolId === schoolId &&
      center.examTypeId === examTypeId &&
      center.isActive
    );
  }

  /**
   * Get centers filtered by exam type ID only (for PLSCE)
   */
  async getCentersByExamType(examTypeId: string): Promise<CenterDto[]> {
    const centers = await this.fetchCenters();
    return centers.filter(center =>
      center.examTypeId === examTypeId &&
      center.isActive
    );
  }

  /**
   * Example of comprehensive API filtering using standardized query parameters
   * This demonstrates the complete implementation pattern for other services
   */
  async searchCentersWithFilters(options: {
    pageNumber?: number;
    pageSize?: number;
    search?: string;
    sortBy?: string;
    sortDescending?: boolean;
    schoolId?: string;
    examTypeId?: string;
    isActive?: boolean;
  }): Promise<CenterDto[]> {
    try {
      // Build query parameters using standardized structure
      const params = new URLSearchParams();

      // Standard pagination parameters
      if (options.pageNumber) params.append('PageNumber', options.pageNumber.toString());
      if (options.pageSize) params.append('PageSize', options.pageSize.toString());
      if (options.search) params.append('Search', options.search);
      if (options.sortBy) params.append('SortBy', options.sortBy);
      if (options.sortDescending !== undefined) params.append('SortDescending', options.sortDescending.toString());

      // Note: Centre endpoint doesn't support schoolId/examTypeId filters in API
      // So we fetch with general parameters and filter client-side
      const url = params.toString() ? `/api/centre?${params.toString()}` : '/api/centre';
      const response: CenterResponse = await apiClient.get(url);

      let filteredCenters = response.items;

      // Apply client-side filtering for fields not supported by API
      if (options.schoolId) {
        filteredCenters = filteredCenters.filter(center => center.schoolId === options.schoolId);
      }
      if (options.examTypeId) {
        filteredCenters = filteredCenters.filter(center => center.examTypeId === options.examTypeId);
      }
      if (options.isActive !== undefined) {
        filteredCenters = filteredCenters.filter(center => center.isActive === options.isActive);
      }

      return filteredCenters;
    } catch (error) {
      console.error('Error searching centers with filters:', error);
      throw error;
    }
  }

  /**
   * Get centers as dropdown options
   */
  async getCenterOptions(activeOnly: boolean = true): Promise<CenterOption[]> {
    const centers = activeOnly ? await this.getActiveCenters() : await this.fetchCenters();
    
    return centers.map(center => ({
      id: center.id,
      name: `Center ${center.manebcentreNo}`,
      centerNumber: center.manebcentreNo,
      examTypeId: center.examTypeId,
      schoolId: center.schoolId,
      isActive: center.isActive
    }));
  }

  /**
   * Get centers filtered by criteria
   */
  async getFilteredCenters(filters: CenterFilters): Promise<CenterDto[]> {
    const centers = filters.activeOnly !== false ? await this.getActiveCenters() : await this.fetchCenters();
    
    return centers.filter(center => {
      if (filters.examTypeId && center.examTypeId !== filters.examTypeId) {
        return false;
      }
      
      if (filters.schoolId && center.schoolId !== filters.schoolId) {
        return false;
      }
      
      if (filters.search) {
        const searchLower = filters.search.toLowerCase();
        return (
          center.manebcentreNo.toLowerCase().includes(searchLower) ||
          center.id.toLowerCase().includes(searchLower) ||
          (center.emiscentreNumber && center.emiscentreNumber.toLowerCase().includes(searchLower))
        );
      }
      
      return true;
    });
  }

  /**
   * Search centers by query
   */
  async searchCenters(query: string): Promise<CenterDto[]> {
    return this.getFilteredCenters({ search: query });
  }



  /**
   * Get centers by school
   */
  async getCentersBySchool(schoolId: string): Promise<CenterDto[]> {
    return this.getFilteredCenters({ schoolId });
  }

  /**
   * Get center by ID
   */
  async getCenterById(id: string): Promise<CenterDto | undefined> {
    const centers = await this.fetchCenters();
    return centers.find(center => center.id === id);
  }

  /**
   * Get center name by ID
   */
  async getCenterName(id: string): Promise<string> {
    const center = await this.getCenterById(id);
    return center ? `Center ${center.manebcentreNo}` : id;
  }

  /**
   * Get unique exam types from centers
   */
  async getExamTypes(): Promise<string[]> {
    const centers = await this.getActiveCenters();
    const examTypes = [...new Set(centers.map(center => center.examTypeId))];
    return examTypes.sort();
  }

  /**
   * Get unique schools from centers
   */
  async getSchools(): Promise<string[]> {
    const centers = await this.getActiveCenters();
    const schools = [...new Set(centers.map(center => center.schoolId))];
    return schools.sort();
  }

  /**
   * Clear cache (useful for testing or forced refresh)
   */
  clearCache(): void {
    this.cachedCenters = null;
    this.lastFetchTime = 0;
  }
}

// Export singleton instance
export const centerService = CenterService.getInstance();

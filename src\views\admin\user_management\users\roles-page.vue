<template>
  <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
    <!-- Breadcrumbs -->
    <BreadcrumbsActions :breadcrumbs="userRolesBreadcrumbs" />
    
    <!-- Loading State -->
    <div v-if="isLoading" class="flex justify-center items-center py-12">
      <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-maneb-primary"></div>
    </div>

    <!-- Error State -->
    <div v-else-if="loadError" class="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
      <div class="flex">
        <div class="flex-shrink-0">
          <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
          </svg>
        </div>
        <div class="ml-3">
          <h3 class="text-sm font-medium text-red-800">Error loading user</h3>
          <div class="mt-2 text-sm text-red-700">{{ loadError }}</div>
        </div>
      </div>
    </div>

    <!-- Role Assignment Content -->
    <div v-else-if="user" class="space-y-6">
      <!-- Header -->
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-2xl font-bold text-gray-900">Assign User Role</h1>
          <p class="mt-1 text-sm text-gray-600">Manage role assignment for {{ user.fullName || `${user.firstName} ${user.lastName}` }}</p>
          <div class="mt-2 flex items-center text-xs text-orange-600">
            <svg class="h-4 w-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
            </svg>
            Only one role per user is allowed. Assigning a new role will replace any existing role.
          </div>
        </div>
        <div class="flex space-x-3">
          <router-link 
            :to="{ name: 'admin.user-management.users.detail', params: { id: user.id } }"
            class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-maneb-primary"
          >
            <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
            </svg>
            View Details
          </router-link>
          <router-link 
            :to="{ name: 'admin.user-management.users' }"
            class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-maneb-primary"
          >
            <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
            </svg>
            Back to Users
          </router-link>
        </div>
      </div>

      <!-- Current Role Display -->
      <div class="bg-white shadow-sm rounded-lg border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
          <h3 class="text-lg font-medium text-gray-900">Current Role Assignment</h3>
        </div>
        <div class="px-6 py-4">
          <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
              <div class="bg-red-50 p-3 rounded-full">
                <svg class="h-6 w-6 text-maneb-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                </svg>
              </div>
              <div>
                <p class="text-sm font-medium text-gray-900">{{ user.fullName || `${user.firstName} ${user.lastName}` }}</p>
                <p class="text-sm text-gray-600">{{ user.email }}</p>
              </div>
            </div>
            <div class="text-right">
              <div v-if="getUserRoles(user).length > 0" class="space-y-1">
                <div v-for="role in getUserRoles(user)" :key="role.id" class="text-right">
                  <p class="text-sm font-medium text-gray-900">{{ role.name }}</p>
                  <p class="text-sm text-gray-600">{{ getRoleStatusText(role.status) }}</p>
                </div>
              </div>
              <div v-else>
                <p class="text-sm font-medium text-gray-900">No roles assigned</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Role Assignment Form -->
      <div class="bg-white shadow-sm rounded-lg border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
          <h3 class="text-lg font-medium text-gray-900">Assign New Role</h3>
          <p class="mt-1 text-sm text-gray-600">Select a role to assign to this user.</p>
        </div>
        
        <form @submit.prevent="handleRoleAssignment" class="px-6 py-4 space-y-6">
          <!-- Role Selection -->
          <div>
            <label for="roleId" class="block text-sm font-medium text-gray-700">
              Select Role <span class="text-red-500">*</span>
            </label>
            <select
              id="roleId"
              v-model="selectedRoleId"
              required
              class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-maneb-primary focus:ring-maneb-primary sm:text-sm"
            >
              <option value="">Choose a role...</option>
              <option value="remove-all">Remove all roles</option>
              <option v-for="role in availableRoles" :key="role.id" :value="role.id">
                {{ role.name }} ({{ getRoleStatusText(role.status) }})
              </option>
            </select>
            <p v-if="errors.roleId" class="mt-1 text-sm text-red-600">{{ errors.roleId }}</p>
          </div>

          <!-- Selected Role Details -->
          <div v-if="selectedRole" class="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h4 class="text-sm font-medium text-blue-900">Selected Role Details</h4>
            <div class="mt-2 text-sm text-blue-700">
              <p><strong>Name:</strong> {{ selectedRole.name }}</p>
              <p><strong>Status:</strong> {{ getRoleStatusText(selectedRole.status) }}</p>
              <p v-if="selectedRole.rolePermissions && selectedRole.rolePermissions.length > 0">
                <strong>Permissions:</strong> {{ selectedRole.rolePermissions.length }} permission(s) assigned
              </p>
            </div>
          </div>

          <!-- Remove Role Confirmation -->
          <div v-if="selectedRoleId === 'remove'" class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <div class="flex">
              <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                </svg>
              </div>
              <div class="ml-3">
                <h3 class="text-sm font-medium text-yellow-800">Remove Role</h3>
                <div class="mt-2 text-sm text-yellow-700">
                  This will remove the current role assignment from the user. The user will have no role assigned.
                </div>
              </div>
            </div>
          </div>

          <!-- Reason for Change -->
          <div>
            <label for="reason" class="block text-sm font-medium text-gray-700">
              Reason for Role Change <span class="text-red-500">*</span>
            </label>
            <textarea
              id="reason"
              v-model="reason"
              rows="3"
              required
              class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-maneb-primary focus:ring-maneb-primary sm:text-sm"
              placeholder="Please provide a reason for this role assignment change..."
            ></textarea>
            <p v-if="errors.reason" class="mt-1 text-sm text-red-600">{{ errors.reason }}</p>
          </div>

          <!-- Error Display -->
          <div v-if="submitError" class="bg-red-50 border border-red-200 rounded-lg p-4">
            <div class="flex">
              <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                </svg>
              </div>
              <div class="ml-3">
                <h3 class="text-sm font-medium text-red-800">Error assigning role</h3>
                <div class="mt-2 text-sm text-red-700">{{ submitError }}</div>
              </div>
            </div>
          </div>

          <!-- Form Actions -->
          <div class="flex justify-between items-center pt-6 border-t border-gray-200">
            <!-- Debug Button (Development Only) -->
            <button
              v-if="isDevelopment"
              type="button"
              @click="runDebugTest"
              class="inline-flex items-center px-3 py-2 border border-blue-300 text-sm font-medium rounded-md text-blue-700 bg-blue-50 hover:bg-blue-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              Test UserRoles API
            </button>
            <div v-else></div>

            <!-- Main Action Buttons -->
            <div class="flex space-x-3">
              <router-link
                :to="{ name: 'admin.user-management.users.detail', params: { id: user.id } }"
                class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-maneb-primary"
              >
                Cancel
              </router-link>
              <button
                type="submit"
                :disabled="isSubmitting || !selectedRoleId || (selectedRoleId !== 'remove-all' && user.roles?.some(r => r.id === selectedRoleId))"
                class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-maneb-primary hover:bg-maneb-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-maneb-primary disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <svg v-if="isSubmitting" class="animate-spin -ml-1 mr-3 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                  <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                {{ isSubmitting ? 'Updating Role...' : (selectedRoleId === 'remove' ? 'Remove Role' : 'Assign Role') }}
              </button>
            </div>
          </div>
        </form>
      </div>

      <!-- Available Roles List -->
      <div class="bg-white shadow-sm rounded-lg border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
          <h3 class="text-lg font-medium text-gray-900">Available Roles</h3>
          <p class="mt-1 text-sm text-gray-600">All roles available for assignment in the system.</p>
        </div>
        <div class="px-6 py-4">
          <div v-if="availableRoles.length === 0" class="text-center py-6">
            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
            </svg>
            <h3 class="mt-2 text-sm font-medium text-gray-900">No roles available</h3>
            <p class="mt-1 text-sm text-gray-500">No approved roles are available for assignment.</p>
          </div>
          <div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div v-for="role in availableRoles" :key="role.id" class="border border-gray-200 rounded-lg p-4">
              <div class="flex items-center justify-between">
                <h4 class="text-sm font-medium text-gray-900">{{ role.name }}</h4>
                <span :class="getRoleStatusBadgeClass(role.status)" class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium">
                  {{ getRoleStatusText(role.status) }}
                </span>
              </div>
              <p v-if="role.rolePermissions" class="mt-1 text-xs text-gray-500">
                {{ role.rolePermissions.length }} permission(s)
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- User Not Found -->
    <div v-else class="text-center py-12">
      <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
      </svg>
      <h3 class="mt-2 text-sm font-medium text-gray-900">User not found</h3>
      <p class="mt-1 text-sm text-gray-500">The requested user could not be found.</p>
      <div class="mt-6">
        <router-link 
          :to="{ name: 'admin.user-management.users' }"
          class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-maneb-primary hover:bg-maneb-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-maneb-primary"
        >
          Back to Users
        </router-link>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useUserStore, useRoleStore } from '@/stores'
import BreadcrumbsActions from '@/components/shared/UI/breadcrumbs/breadcrumbs_actions.vue'
import { showSuccessAlert, showConfirmDialog } from '@/utils/ui/sweetAlert'
import type { UserDto, RoleDto, RecordStatus } from '@/interfaces'
import ApiDebugger from '@/utils/api/api-debug'

// Router
const route = useRoute()
const router = useRouter()

// Stores
const userStore = useUserStore()
const roleStore = useRoleStore()

// Reactive state
const user = ref<UserDto | null>(null)
const isLoading = ref(false)
const loadError = ref<string | null>(null)
const isSubmitting = ref(false)
const submitError = ref<string | null>(null)
const selectedRoleId = ref<string>('')
const reason = ref('')
const availableRoles = ref<RoleDto[]>([])

const errors = reactive<Record<string, string>>({})

// Development flag
const isDevelopment = ref(true) // Enable debug tools

// Computed
const userId = computed(() => route.params.id as string)

const userRolesBreadcrumbs = computed(() => [
  { name: 'Admin', href: '/admin', current: false },
  { name: 'User Management', href: '/admin/user-management', current: false },
  { name: 'Users', href: '/admin/user-management/users', current: false },
  { name: user.value?.fullName || `${user.value?.firstName} ${user.value?.lastName}` || 'Assign Roles', href: '', current: true },
])

const selectedRole = computed(() => {
  if (!selectedRoleId.value || selectedRoleId.value === 'remove') return null
  return availableRoles.value.find(role => role.id === selectedRoleId.value) || null
})

// Methods
// Helper function to get user roles (handles both new single role and legacy roles array)
const getUserRoles = (user: UserDto) => {
  if (user.roles && user.roles.length > 0) {
    // Legacy format: roles array
    return user.roles
  } else if (user.role) {
    // New format: single role object
    return [user.role]
  }
  return []
}

const getRoleStatusText = (status?: RecordStatus) => {
  switch (status) {
    case 'Approved': return 'Approved'
    case 'SecondApproved': return 'Fully Approved'
    case 'Unapproved': return 'Pending'
    case 'Rejected': return 'Rejected'
    default: return 'Unknown'
  }
}

const getRoleStatusBadgeClass = (status?: RecordStatus) => {
  switch (status) {
    case 'Approved':
      return 'bg-green-100 text-green-800'
    case 'SecondApproved':
      return 'bg-blue-100 text-blue-800'
    case 'Unapproved':
      return 'bg-yellow-100 text-yellow-800'
    case 'Rejected':
      return 'bg-red-100 text-red-800'
    default:
      return 'bg-gray-100 text-gray-800'
  }
}

const validateForm = (): boolean => {
  // Clear previous errors
  Object.keys(errors).forEach(key => delete errors[key])

  let isValid = true

  if (!selectedRoleId.value) {
    errors.roleId = 'Please select a role or choose to remove the current role'
    isValid = false
  }

  if (!reason.value.trim()) {
    errors.reason = 'Reason for role change is required'
    isValid = false
  } else if (reason.value.trim().length < 10) {
    errors.reason = 'Please provide a more detailed reason (at least 10 characters)'
    isValid = false
  }

  return isValid
}

const handleRoleAssignment = async () => {
  if (!validateForm() || !user.value?.id) {
    return
  }

  const actionText = selectedRoleId.value === 'remove' ? 'remove the current role' : `assign the role "${selectedRole.value?.name}"`

  // Show confirmation dialog
  const confirmed = await showConfirmDialog(
    'Confirm Role Assignment',
    `Are you sure you want to ${actionText} for this user?`,
    selectedRoleId.value === 'remove' ? 'Yes, Remove Role' : 'Yes, Assign Role'
  )

  if (!confirmed) {
    return
  }

  try {
    isSubmitting.value = true
    submitError.value = null

    // Debug logging
    console.log('=== Role Assignment Debug ===');
    console.log('User ID:', user.value.id);
    console.log('Selected Role ID:', selectedRoleId.value);
    console.log('Current User Data:', user.value);
    console.log('Available Roles:', availableRoles.value);

    // Update user roles using new API structure
    let updatedUser: UserDto;
    let successMessage = '';

    if (selectedRoleId.value === 'remove-all') {
      console.log('Removing all roles from user');
      updatedUser = await userStore.removeAllRolesFromUser(user.value.id);
      successMessage = 'All roles removed successfully!';
    } else {
      console.log('Adding role to user:', selectedRoleId.value);
      updatedUser = await userStore.addRoleToUser(user.value.id, selectedRoleId.value);
      successMessage = `Role "${selectedRole.value?.name}" assigned successfully!`;
    }

    // Update local user data
    user.value = updatedUser;

    // Show success message
    await showSuccessAlert(successMessage)

    // Navigate to user detail page
    router.push({
      name: 'admin.user-management.users.detail',
      params: { id: user.value.id }
    })

  } catch (error: any) {
    // Role Assignment Error (console logging removed)

    submitError.value = error.message || 'Failed to update user role'
  } finally {
    isSubmitting.value = false
  }
}

// Debug function for UserRoles endpoints (console logging removed)
const runDebugTest = async () => {
  if (!user.value?.id) {
    // No user ID available for debug test (console logging removed)
    return;
  }

  try {
    // Starting UserRoles Debug Test (console logging removed)

    // Test UserRoles endpoints
    await ApiDebugger.testUserRolesEndpoints(user.value.id, selectedRoleId.value || undefined);

    // If a role is selected, test the full workflow
    if (selectedRoleId.value && selectedRoleId.value !== 'remove-all') {
      // Testing Role Assignment Workflow (console logging removed)
      await ApiDebugger.testRoleAssignmentWorkflow(user.value.id, selectedRoleId.value);
    }

    alert('UserRoles debug test completed! Check console for details.');
  } catch (error) {
    // UserRoles debug test failed (console logging removed)
    alert('UserRoles debug test failed! Check console for details.');
  }
}

const fetchUserDetails = async () => {
  if (!userId.value) {
    loadError.value = 'No user ID provided'
    return
  }

  try {
    isLoading.value = true
    loadError.value = null
    user.value = await userStore.fetchUserById(userId.value)
  } catch (err: any) {
    loadError.value = err.message || 'Failed to load user details'
    console.error('Error fetching user details:', err)
  } finally {
    isLoading.value = false
  }
}

const fetchRoles = async () => {
  try {
    await roleStore.fetchRoles()
    availableRoles.value = roleStore.roles.filter(role =>
      role.status === 'Approved' || role.status === 'SecondApproved'
    )
  } catch (error) {
    console.error('Error fetching roles:', error)
  }
}

// Lifecycle
onMounted(async () => {
  await Promise.all([
    fetchUserDetails(),
    fetchRoles()
  ])
})
</script>

<style scoped>
/* MANEB Theme Colors */
.bg-maneb-primary {
  background-color: #a12c2c;
}

.text-maneb-primary {
  color: #a12c2c;
}

.bg-maneb-primary-dark {
  background-color: #8b2424;
}

.border-maneb-primary {
  border-color: #a12c2c;
}

.focus\:ring-maneb-primary:focus {
  --tw-ring-color: #a12c2c;
}

.focus\:border-maneb-primary:focus {
  border-color: #a12c2c;
}

.hover\:bg-maneb-primary-dark:hover {
  background-color: #8b2424;
}
</style>

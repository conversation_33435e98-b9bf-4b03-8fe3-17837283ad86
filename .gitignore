# Node dependencies
node_modules/

# Build output
/dist/
/build/
.output/
.data/

# Local environment variables
.env
.env.local
.env.*.local
!.env.example # Keep example file for reference

# Log files
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Editor/IDE specific files
.idea/
.vscode/
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw*

# macOS specific files
.DS_Store

# Nuxt.js specific (if applicable)
.nuxt/
.nitro/
.cache/
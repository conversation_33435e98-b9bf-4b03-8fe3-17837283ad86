/**
 * @fileoverview Table UI interfaces for MANEB Results Management System
 * @description Generic interfaces for table components and data display
 */

/**
 * Table column definition
 * @description Configuration for table columns
 */
export interface TableColumn {
  /** The key to access the data in each row object */
  key: string;
  /** The display name for the column header */
  label: string;
  /** Optional: true if the column can be sorted */
  sortable?: boolean;
}

/**
 * Table row data structure
 * @description Flexible row data structure for table components
 */
export interface TableRowData {
  /** Allows for flexible row data structure with common data types */
  [key: string]: string | number | boolean | Date | null;
}

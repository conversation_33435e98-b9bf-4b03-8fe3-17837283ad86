Stack trace:
Frame         Function      Args
0007FFFF8E90  00021005FE8E (000210285F68, 00021026AB6E, 0007FFFF8E90, 0007FFFF7D90) msys-2.0.dll+0x1FE8E
0007FFFF8E90  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFF9168) msys-2.0.dll+0x67F9
0007FFFF8E90  000210046832 (000210286019, 0007FFFF8D48, 0007FFFF8E90, 000000000000) msys-2.0.dll+0x6832
0007FFFF8E90  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFF8E90  000210068E24 (0007FFFF8EA0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFF9170  00021006A225 (0007FFFF8EA0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FF81EE80000 ntdll.dll
7FF81DA90000 KERNEL32.DLL
7FF81C670000 KERNELBASE.dll
7FF81DC80000 USER32.dll
7FF81C150000 win32u.dll
7FF81EC70000 GDI32.dll
7FF81C020000 gdi32full.dll
7FF81C210000 msvcp_win.dll
7FF81CB00000 ucrtbase.dll
000210040000 msys-2.0.dll
7FF81ED60000 advapi32.dll
7FF81E550000 msvcrt.dll
7FF81D780000 sechost.dll
7FF81D650000 RPCRT4.dll
7FF81B550000 CRYPTBASE.DLL
7FF81CA60000 bcryptPrimitives.dll
7FF81ED20000 IMM32.DLL

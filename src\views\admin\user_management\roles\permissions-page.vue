<template>
  <div class="min-h-screen bg-gray-50 py-6">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <!-- Header -->
      <div class="mb-8">
        <div class="flex items-center justify-between">
          <div>
            <h1 class="text-3xl font-bold text-gray-900">Role Permissions</h1>
            <p class="mt-2 text-gray-600">Assign permissions to roles for system access control</p>
            <!-- Navigation Tabs -->
            <div class="flex space-x-2 mt-4">
              <router-link
                :to="{ name: 'admin.user-management.roles' }"
                class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-700 hover:bg-gray-200 transition-colors duration-200"
              >
                <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"/>
                </svg>
                Roles
              </router-link>
              <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-maneb-primary text-white">
                <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"/>
                </svg>
                Permissions
              </span>
            </div>
          </div>
          <router-link
            :to="{ name: 'admin.user-management.roles' }"
            class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-maneb-primary"
          >
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"/>
            </svg>
            Back to Roles
          </router-link>
        </div>
      </div>

      <!-- Role Selection -->
      <div class="bg-white shadow rounded-lg mb-6">
        <div class="px-6 py-4 border-b border-gray-200">
          <h2 class="text-lg font-medium text-gray-900">Select Role</h2>
        </div>
        <div class="p-6">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label for="roleSelect" class="block text-sm font-medium text-gray-700 mb-2">
                Role <span class="text-red-500">*</span>
              </label>
              <select
                id="roleSelect"
                v-model="selectedRoleId"
                @change="handleRoleChange"
                class="block w-full rounded-lg border border-gray-300 bg-white px-3 py-2.5 text-sm text-gray-900 focus:border-maneb-primary focus:ring-2 focus:ring-maneb-primary"
              >
                <option value="">Select a role...</option>
                <option v-for="role in availableRoles" :key="role.id" :value="role.id">
                  {{ role.name }} ({{ role.status }})
                </option>
              </select>
            </div>
            <div v-if="selectedRole" class="flex items-end">
              <div class="bg-gray-50 rounded-lg p-4 w-full">
                <h3 class="text-sm font-medium text-gray-900">{{ selectedRole.name }}</h3>
                <p class="text-sm text-gray-600 mt-1">{{ selectedRole.description || 'No description' }}</p>
                <p class="text-xs text-maneb-primary mt-2">
                  {{ totalSelectedPermissions }} permission{{ totalSelectedPermissions !== 1 ? 's' : '' }} assigned
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Permissions Assignment -->
      <div v-if="selectedRoleId" class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200">
          <div class="flex items-center justify-between">
            <h2 class="text-lg font-medium text-gray-900">Assign Permissions</h2>
            <div class="flex items-center space-x-4">
              <button
                type="button"
                @click="selectAllPermissions"
                :disabled="isSystemAdminRole"
                class="text-sm text-maneb-primary hover:text-red-700 disabled:text-gray-400 disabled:cursor-not-allowed"
              >
                Select All
              </button>
              <span class="text-gray-300">|</span>
              <button
                type="button"
                @click="clearAllPermissions"
                :disabled="isSystemAdminRole"
                class="text-sm text-gray-600 hover:text-gray-800 disabled:text-gray-400 disabled:cursor-not-allowed"
              >
                Clear All
              </button>
              <span class="text-gray-300">|</span>
              <button
                type="button"
                @click="bulkAssignCommonPermissions"
                :disabled="isSystemAdminRole"
                class="text-sm text-blue-600 hover:text-blue-800 disabled:text-gray-400 disabled:cursor-not-allowed"
              >
                Quick Assign
              </button>
            </div>
          </div>
        </div>

        <div class="p-6">
          <!-- Loading State -->
          <div v-if="isLoadingPermissions" class="text-center py-8">
            <div class="inline-flex items-center">
              <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-maneb-primary" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Loading permissions...
            </div>
          </div>

          <!-- Permissions Grid -->
          <div v-else class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
            <div v-for="module in availableModules" :key="module.key" class="border border-gray-200 rounded-lg">
              <div class="bg-gray-50 px-4 py-3 border-b border-gray-200">
                <div class="flex items-center justify-between">
                  <h3 class="text-sm font-medium text-gray-900">{{ module.name }}</h3>
                  <button
                    type="button"
                    @click="toggleModuleExpansion(module.key)"
                    class="text-gray-400 hover:text-gray-600"
                  >
                    <svg 
                      class="w-5 h-5 transform transition-transform"
                      :class="{ 'rotate-180': expandedModules.has(module.key) }"
                      fill="none" 
                      stroke="currentColor" 
                      viewBox="0 0 24 24"
                    >
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
                    </svg>
                  </button>
                </div>
              </div>
              
              <div v-if="expandedModules.has(module.key)" class="p-4 space-y-3">
                <div v-for="action in module.actions" :key="`${module.key}-${action.key}`" class="flex items-center">
                  <input
                    :id="`${module.key}-${action.key}`"
                    type="checkbox"
                    :checked="isPermissionSelected(module.key, action.key)"
                    @change="togglePermission(module.key, action.key, $event.target.checked)"
                    :disabled="isSystemAdminRole"
                    class="h-4 w-4 text-maneb-primary focus:ring-maneb-primary border-gray-300 rounded disabled:cursor-not-allowed"
                  />
                  <label :for="`${module.key}-${action.key}`" class="ml-3 text-sm text-gray-700">
                    {{ action.name }}
                  </label>
                </div>
              </div>
            </div>
          </div>

          <!-- Save Actions -->
          <div v-if="!isLoadingPermissions" class="mt-8 flex justify-end space-x-4">
            <button
              type="button"
              @click="resetPermissions"
              :disabled="isSubmitting"
              class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-maneb-primary disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Reset
            </button>
            <button
              type="button"
              @click="savePermissions"
              :disabled="isSubmitting || !selectedRoleId"
              class="inline-flex items-center px-6 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-maneb-primary hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-maneb-primary disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <svg v-if="isSubmitting" class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              {{ isSubmitting ? 'Saving...' : 'Save Permissions' }}
            </button>
          </div>
        </div>
      </div>

      <!-- Empty State -->
      <div v-else class="bg-white shadow rounded-lg">
        <div class="text-center py-12">
          <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"/>
          </svg>
          <h3 class="mt-2 text-sm font-medium text-gray-900">No Role Selected</h3>
          <p class="mt-1 text-sm text-gray-500">Select a role above to assign permissions.</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useRoleStore, useSectionStore, useRolePermissionStore } from '@/stores'
import type { RoleDto, PermissionAction, RolePermissionDto, CreateRolePermissionRequest, RecordStatus } from '@/interfaces'
import { sweetAlert } from '@/utils/ui/sweetAlert'

// Helper functions for alerts
const showSuccessAlert = async (title: string, text?: string) => {
  return sweetAlert.success(title, text)
}

const showErrorAlert = async (text: string) => {
  return sweetAlert.error('Error', text)
}

// Router
const router = useRouter()

// Stores
const roleStore = useRoleStore()
const sectionStore = useSectionStore()
const rolePermissionStore = useRolePermissionStore()

// State
const selectedRoleId = ref<string>('')
const selectedRole = ref<RoleDto | null>(null)
const availableRoles = ref<RoleDto[]>([])
const isLoadingPermissions = ref(false)
const isSubmitting = ref(false)
const expandedModules = ref(new Set<string>())
const selectedPermissions = ref(new Map<string, Set<string>>())

// Available modules based on sections from API
const availableModules = ref<Array<{
  key: string;
  name: string;
  actions: Array<{ key: PermissionAction; name: string }>;
}>>([])

// Available actions for each module
const availableActions: Array<{ key: PermissionAction; name: string }> = [
  { key: 'Create', name: 'Create' },
  { key: 'Read', name: 'View' },
  { key: 'Update', name: 'Edit' },
  { key: 'Delete', name: 'Delete' },
  { key: 'Approve', name: 'Approve' },
  { key: 'Export', name: 'Export' }
]

// Computed
const isSystemAdminRole = computed(() => {
  if (!selectedRole.value) return false
  const roleName = selectedRole.value.name?.toLowerCase() || ''
  const systemAdminVariations = [
    'system_administrator',
    'systemadministrator', 
    'sysadmin',
    'system admin',
    'sys_admin',
    'sys admin'
  ]
  return systemAdminVariations.some(variation => roleName.includes(variation.toLowerCase()))
})

const totalSelectedPermissions = computed(() => {
  let total = 0
  selectedPermissions.value.forEach(actions => {
    total += actions.size
  })
  return total
})

// Methods
const fetchRoles = async () => {
  try {
    console.log('🔄 Fetching roles...')
    await roleStore.fetchRoles()
    console.log('📋 All roles from store:', roleStore.roles)

    availableRoles.value = roleStore.roles.filter(role =>
      role.status === 'Approved' || role.status === 'SecondApproved'
    )

    console.log('✅ Available roles after filtering:', availableRoles.value)
    console.log('🔍 Looking for Data Clerk role:', availableRoles.value.find(r => r.name === 'Data Clerk'))
  } catch (error) {
    console.error('Error fetching roles:', error)
    await showErrorAlert('Failed to load roles. Please try again.')
  }
}

const loadSections = async () => {
  try {
    await sectionStore.fetchSections()

    // Show all sections regardless of status - permission filtering will be resolved later
    const sections = sectionStore.sections

    // Convert sections to modules
    availableModules.value = sections.map(section => ({
      key: section.id || section.name,
      name: section.name,
      actions: availableActions
    }))

    // Expand first module by default
    if (availableModules.value.length > 0) {
      expandedModules.value.add(availableModules.value[0].key)
    }
  } catch (error) {
    console.error('Error loading sections:', error)
    await showErrorAlert('Failed to load system modules. Please try again.')
  }
}

const handleRoleChange = async () => {
  console.log(`🎯 Role change handler triggered for: ${selectedRoleId.value}`)
  await loadRolePermissions()
}

const loadRolePermissions = async () => {
  if (!selectedRoleId.value) {
    selectedRole.value = null
    selectedPermissions.value.clear()
    return
  }

  try {
    isLoadingPermissions.value = true
    console.log(`Loading permissions for role: ${selectedRoleId.value}`)

    selectedRole.value = availableRoles.value.find(role => role.id === selectedRoleId.value) || null
    console.log('Selected role:', selectedRole.value)

    if (selectedRole.value) {
      // Load existing permissions
      selectedPermissions.value.clear()

      if (selectedRole.value.rolePermissions) {
        console.log(`Found ${selectedRole.value.rolePermissions.length} existing permissions`)

        selectedRole.value.rolePermissions.forEach((permission: RolePermissionDto) => {
          console.log('Processing permission:', permission)

          const moduleKey = permission.sectionID
          if (!selectedPermissions.value.has(moduleKey)) {
            selectedPermissions.value.set(moduleKey, new Set())
          }
          if (permission.canAccess) {
            selectedPermissions.value.get(moduleKey)?.add(permission.action)
            console.log(`Added permission: ${moduleKey}:${permission.action}`)
          } else {
            console.log(`Skipped permission (canAccess=false): ${moduleKey}:${permission.action}`)
          }
        })
      } else {
        console.log('No existing permissions found for role')
      }

      console.log('Final selected permissions:', selectedPermissions.value)
    } else {
      console.warn('Selected role not found in available roles')
    }
  } catch (error) {
    console.error('Error loading role permissions:', error)
    await showErrorAlert('Failed to load role permissions. Please try again.')
  } finally {
    isLoadingPermissions.value = false
  }
}

const toggleModuleExpansion = (moduleKey: string) => {
  if (expandedModules.value.has(moduleKey)) {
    expandedModules.value.delete(moduleKey)
  } else {
    expandedModules.value.add(moduleKey)
  }
}

const isPermissionSelected = (moduleKey: string, actionKey: string): boolean => {
  return selectedPermissions.value.get(moduleKey)?.has(actionKey) || false
}

const togglePermission = (moduleKey: string, actionKey: string, isSelected: boolean) => {
  console.log(`🔄 Toggling permission: ${moduleKey}:${actionKey} = ${isSelected}`)
  console.log('Current selectedPermissions before toggle:', selectedPermissions.value)

  if (!selectedPermissions.value.has(moduleKey)) {
    selectedPermissions.value.set(moduleKey, new Set())
    console.log(`📝 Created new module set for: ${moduleKey}`)
  }

  const modulePermissions = selectedPermissions.value.get(moduleKey)!
  if (isSelected) {
    modulePermissions.add(actionKey)
    console.log(`✅ Added permission: ${moduleKey}:${actionKey}`)
  } else {
    modulePermissions.delete(actionKey)
    console.log(`❌ Removed permission: ${moduleKey}:${actionKey}`)
  }

  console.log('Current selectedPermissions after toggle:', selectedPermissions.value)
  console.log(`Total permissions selected: ${Array.from(selectedPermissions.value.values()).reduce((total, set) => total + set.size, 0)}`)
}

const selectAllPermissions = () => {
  availableModules.value.forEach(module => {
    const modulePermissions = new Set<string>()
    module.actions.forEach(action => {
      modulePermissions.add(action.key)
    })
    selectedPermissions.value.set(module.key, modulePermissions)
  })
}

const clearAllPermissions = () => {
  selectedPermissions.value.clear()
}

const bulkAssignCommonPermissions = async () => {
  if (isSystemAdminRole.value) {
    return
  }

  try {
    const result = await sweetAlert.confirm(
      'Quick Assign Permissions',
      'This will assign common permissions (View, Create, Update) to all modules. Continue?',
      'question'
    )

    if (result.isConfirmed) {
      selectedPermissions.value.clear()

      // Assign common permissions to all modules
      availableModules.value.forEach(module => {
        const commonActions = new Set(['View', 'Create', 'Update'])
        selectedPermissions.value.set(module.key, commonActions)
      })

      await sweetAlert.success('Quick Assignment Complete', 'Common permissions have been assigned to all modules.')
    }
  } catch (error) {
    console.error('Error in bulk assign:', error)
    await showErrorAlert('Failed to perform bulk assignment. Please try again.')
  }
}

// Validation functions
const validatePermissionSelection = (): { isValid: boolean; errors: string[] } => {
  const errors: string[] = []

  // Check if any permissions are selected
  if (selectedPermissions.value.size === 0) {
    errors.push('At least one permission must be selected')
    return { isValid: false, errors }
  }

  // Check for conflicting permissions (e.g., if 'All' is selected with specific actions)
  selectedPermissions.value.forEach((actions, moduleKey) => {
    if (actions.has('All') && actions.size > 1) {
      errors.push(`Module "${moduleKey}" has conflicting permissions: 'All' cannot be combined with specific actions`)
    }
  })

  // Check for duplicate permissions (shouldn't happen with Set, but good to verify)
  const allPermissions: string[] = []
  selectedPermissions.value.forEach((actions, moduleKey) => {
    actions.forEach(action => {
      const permissionKey = `${moduleKey}:${action}`
      if (allPermissions.includes(permissionKey)) {
        errors.push(`Duplicate permission detected: ${permissionKey}`)
      } else {
        allPermissions.push(permissionKey)
      }
    })
  })

  return { isValid: errors.length === 0, errors }
}

const validateRoleSelection = (): { isValid: boolean; errors: string[] } => {
  const errors: string[] = []

  if (!selectedRoleId.value) {
    errors.push('A role must be selected')
  }

  if (!selectedRole.value) {
    errors.push('Selected role data is invalid')
  }

  // Check if role is System Administrator (should not be modified)
  if (selectedRole.value?.name === 'System Administrator') {
    errors.push('System Administrator role permissions cannot be modified')
  }

  return { isValid: errors.length === 0, errors }
}

const checkForPermissionConflicts = (permissions: Array<{ sectionId: string; action: PermissionAction; canAccess: boolean }>): string[] => {
  const conflicts: string[] = []
  const permissionMap = new Map<string, string[]>()

  // Group permissions by section
  permissions.forEach(permission => {
    if (!permissionMap.has(permission.sectionId)) {
      permissionMap.set(permission.sectionId, [])
    }
    permissionMap.get(permission.sectionId)!.push(permission.action)
  })

  // Check for conflicts within each section
  permissionMap.forEach((actions, sectionId) => {
    // If 'All' is present with other actions, it's a conflict
    if (actions.includes('All') && actions.length > 1) {
      conflicts.push(`Section "${sectionId}" has conflicting permissions: 'All' action cannot be combined with specific actions (${actions.filter(a => a !== 'All').join(', ')})`)
    }

    // Check for duplicate actions
    const uniqueActions = new Set(actions)
    if (uniqueActions.size !== actions.length) {
      conflicts.push(`Section "${sectionId}" has duplicate actions`)
    }
  })

  return conflicts
}

const resetPermissions = async () => {
  if (!selectedRoleId.value) return

  const result = await sweetAlert.confirm(
    'Reset Permissions',
    'Are you sure you want to reset all permissions for this role? This will reload the original permissions from the database.',
    'Yes, Reset'
  )

  if (result.isConfirmed) {
    await loadRolePermissions()
    await sweetAlert.success('Permissions Reset', 'Role permissions have been reset to their original state.')
  }
}

const savePermissions = async () => {
  // Validate role selection
  const roleValidation = validateRoleSelection()
  if (!roleValidation.isValid) {
    await showErrorAlert(`Role validation failed: ${roleValidation.errors.join(', ')}`)
    return
  }

  // Validate permission selection
  const permissionValidation = validatePermissionSelection()
  if (!permissionValidation.isValid) {
    await showErrorAlert(`Permission validation failed: ${permissionValidation.errors.join(', ')}`)
    return
  }

  try {
    isSubmitting.value = true

    // Prepare permissions data with validation
    const permissions: Array<{ sectionId: string; action: PermissionAction; canAccess: boolean }> = []

    selectedPermissions.value.forEach((actions, moduleKey) => {
      actions.forEach(action => {
        // Validate moduleKey and action
        if (moduleKey && action && moduleKey.trim() !== '' && action.trim() !== '') {
          permissions.push({
            sectionId: moduleKey.trim(),
            action: action.trim() as PermissionAction,
            canAccess: true
          })
        } else {
          console.warn('Skipping invalid permission:', { moduleKey, action })
        }
      })
    })

    if (permissions.length === 0) {
      await showErrorAlert('No valid permissions found to save.')
      return
    }

    // Check for permission conflicts
    const conflicts = checkForPermissionConflicts(permissions)
    if (conflicts.length > 0) {
      await showErrorAlert(`Permission conflicts detected: ${conflicts.join('; ')}`)
      return
    }

    console.log(`Saving ${permissions.length} permissions for role ${selectedRole.value.name}`)
    console.log('Permissions to save:', permissions)

    // Convert permissions to the correct format for the API
    const permissionRequests: CreateRolePermissionRequest[] = permissions.map(permission => ({
      roleId: selectedRoleId.value,
      sectionID: permission.sectionId,
      action: permission.action,
      canAccess: permission.canAccess,
      status: 'Approved' as RecordStatus
    }))

    // Use the new bulk replace operation for better performance
    await rolePermissionStore.replaceRolePermissions(selectedRoleId.value, permissionRequests)

    await showSuccessAlert(
      'Permissions Saved Successfully!',
      `${permissions.length} permissions have been assigned to role "${selectedRole.value.name}".`
    )

    // Reload the role permissions to reflect changes
    await loadRolePermissions()

  } catch (error: any) {
    // Error saving permissions (console logging removed)

    // Provide more detailed error messages
    let errorMessage = 'Failed to save permissions. Please try again.'

    if (error.message) {
      if (error.message.includes('Bulk creation failed')) {
        errorMessage = `Permission assignment partially failed: ${error.message}`
      } else if (error.message.includes('No valid permissions')) {
        errorMessage = 'No valid permissions found. Please check your selections and try again.'
      } else if (error.message.includes('roleId must be a valid GUID')) {
        errorMessage = 'Invalid role selected. Please refresh the page and try again.'
      } else {
        errorMessage = `Failed to save permissions: ${error.message}`
      }
    }

    await showErrorAlert(errorMessage)
  } finally {
    isSubmitting.value = false
  }
}

// Lifecycle
onMounted(async () => {
  await Promise.all([
    fetchRoles(),
    loadSections()
  ])
})

// Watchers
watch(selectedRoleId, async (newRoleId, oldRoleId) => {
  console.log(`🔍 Role selection changed from ${oldRoleId} to ${newRoleId}`)
  if (newRoleId !== oldRoleId) {
    await loadRolePermissions()
  }
}, { immediate: false })
</script>

<style scoped>
/* MANEB Theme Colors */
.bg-maneb-primary {
  background-color: #a12c2c;
}

.text-maneb-primary {
  color: #a12c2c;
}

.border-maneb-primary {
  border-color: #a12c2c;
}

.focus\:ring-maneb-primary:focus {
  --tw-ring-color: #a12c2c;
}

.focus\:border-maneb-primary:focus {
  border-color: #a12c2c;
}

.hover\:bg-red-700:hover {
  background-color: #b91c1c;
}
</style>

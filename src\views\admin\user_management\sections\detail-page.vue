<template>
  <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
    <!-- Loading State -->
    <div v-if="isLoading" class="flex justify-center items-center py-12">
      <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-maneb-primary"></div>
    </div>

    <!-- Error State -->
    <div v-else-if="loadError" class="bg-red-50 border border-red-200 rounded-md p-4 mb-6">
      <div class="flex">
        <div class="flex-shrink-0">
          <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
          </svg>
        </div>
        <div class="ml-3">
          <h3 class="text-sm font-medium text-red-800">Error loading section</h3>
          <p class="mt-1 text-sm text-red-700">{{ loadError }}</p>
        </div>
      </div>
    </div>

    <!-- Section Details Content -->
    <div v-else-if="section" class="space-y-6">
      <!-- Breadcrumbs -->
      <nav class="flex" aria-label="Breadcrumb">
        <ol class="inline-flex items-center space-x-1 md:space-x-3">
          <li class="inline-flex items-center">
            <router-link to="/admin" class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-maneb-primary">
              Admin
            </router-link>
          </li>
          <li>
            <div class="flex items-center">
              <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
              </svg>
              <router-link to="/admin/user-management" class="ml-1 text-sm font-medium text-gray-700 hover:text-maneb-primary md:ml-2">User Management</router-link>
            </div>
          </li>
          <li>
            <div class="flex items-center">
              <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
              </svg>
              <router-link to="/admin/user-management/sections" class="ml-1 text-sm font-medium text-gray-700 hover:text-maneb-primary md:ml-2">Sections</router-link>
            </div>
          </li>
          <li aria-current="page">
            <div class="flex items-center">
              <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
              </svg>
              <span class="ml-1 text-sm font-medium text-gray-500 md:ml-2">{{ section.name }}</span>
            </div>
          </li>
        </ol>
      </nav>

      <!-- Header -->
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
          <div class="flex items-center justify-center w-12 h-12 bg-red-50 rounded-lg">
            <svg class="h-6 w-6 text-maneb-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
            </svg>
          </div>
          <div>
            <h1 class="text-2xl font-bold text-gray-900">{{ section.name }}</h1>
            <p class="text-gray-600">Section Details</p>
          </div>
        </div>
        <div class="flex items-center space-x-3">
          <span :class="getStatusBadgeClass(section.status)" class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium">
            {{ section.status }}
          </span>
          <router-link 
            :to="{ name: 'admin.user-management.sections.edit', params: { id: section.id } }"
            class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-maneb-primary"
          >
            <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
            </svg>
            Edit Section
          </router-link>
          <router-link 
            :to="{ name: 'admin.user-management.sections' }"
            class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-maneb-primary"
          >
            <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
            </svg>
            Back to Sections
          </router-link>
        </div>
      </div>

      <!-- Section Information Card -->
      <div class="bg-white shadow-sm border border-gray-200 rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200">
          <h3 class="text-lg font-medium text-black">Section Information</h3>
        </div>
        <div class="px-6 py-4">
          <dl class="grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2">
            <div>
              <dt class="text-sm font-medium text-gray-500">Section ID</dt>
              <dd class="mt-1 text-sm text-black">{{ section.id }}</dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-gray-500">Name</dt>
              <dd class="mt-1 text-sm text-black">{{ section.name }}</dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-gray-500">Controller</dt>
              <dd class="mt-1 text-sm text-black">{{ section.controller || 'N/A' }}</dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-gray-500">Area</dt>
              <dd class="mt-1 text-sm text-black">{{ section.area || 'N/A' }}</dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-gray-500">Status</dt>
              <dd class="mt-1">
                <span :class="getStatusBadgeClass(section.status)" class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium">
                  {{ section.status }}
                </span>
              </dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-gray-500">Created Date</dt>
              <dd class="mt-1 text-sm text-black">{{ formatDate(section.dateCreated) }}</dd>
            </div>
          </dl>
        </div>
      </div>

      <!-- Related Permissions Section -->
      <div class="bg-white shadow-sm border border-gray-200 rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200">
          <h3 class="text-lg font-medium text-black">Related Permissions</h3>
        </div>
        <div class="px-6 py-4">
          <div v-if="relatedPermissions.length > 0" class="space-y-2">
            <div v-for="permission in relatedPermissions" :key="permission.id" class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div>
                <p class="text-sm font-medium text-black">{{ permission.action }}</p>
                <p class="text-xs text-gray-500">Role: {{ permission.roleId }}</p>
              </div>
              <div class="flex items-center space-x-2">
                <span :class="permission.canAccess ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'" class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium">
                  {{ permission.canAccess ? 'Allowed' : 'Denied' }}
                </span>
                <span :class="getStatusBadgeClass(permission.status)" class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium">
                  {{ permission.status }}
                </span>
              </div>
            </div>
          </div>
          <div v-else class="text-center py-6">
            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
            </svg>
            <h3 class="mt-2 text-sm font-medium text-gray-900">No permissions found</h3>
            <p class="mt-1 text-sm text-gray-500">No permissions are currently associated with this section.</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { useSectionStore, useRoleStore } from '@/stores'
import type { SectionDto, RolePermissionDto, RecordStatus } from '@/interfaces'

// Router
const route = useRoute()

// Stores
const sectionStore = useSectionStore()
const roleStore = useRoleStore()

// Reactive state
const section = ref<SectionDto | null>(null)
const isLoading = ref(false)
const loadError = ref<string | null>(null)
const relatedPermissions = ref<RolePermissionDto[]>([])

// Computed
const sectionId = computed(() => route.params.id as string)

// Methods
const getStatusBadgeClass = (status?: RecordStatus) => {
  switch (status) {
    case 'Approved':
      return 'bg-green-100 text-green-800'
    case 'SecondApproved':
      return 'bg-blue-100 text-blue-800'
    case 'Rejected':
      return 'bg-red-100 text-red-800'
    case 'Unapproved':
    default:
      return 'bg-yellow-100 text-yellow-800'
  }
}

const formatDate = (date: Date | undefined) => {
  if (!date) return 'N/A'
  return new Date(date).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}

const fetchSectionDetails = async () => {
  if (!sectionId.value) {
    loadError.value = 'No section ID provided'
    return
  }

  try {
    isLoading.value = true
    loadError.value = null
    section.value = await sectionStore.fetchSectionById(sectionId.value)
  } catch (err: any) {
    loadError.value = err.message || 'Failed to load section details'
    console.error('Error fetching section details:', err)
  } finally {
    isLoading.value = false
  }
}

const fetchRelatedPermissions = async () => {
  if (!sectionId.value) return

  try {
    await roleStore.fetchRolePermissions()
    relatedPermissions.value = roleStore.rolePermissions.filter(
      permission => permission.sectionID === sectionId.value
    )
  } catch (error) {
    console.error('Error fetching related permissions:', error)
  }
}

// Lifecycle
onMounted(async () => {
  await Promise.all([
    fetchSectionDetails(),
    fetchRelatedPermissions()
  ])
})
</script>

<style scoped>
/* MANEB Theme Colors */
.bg-maneb-primary {
  background-color: #a12c2c;
}

.text-maneb-primary {
  color: #a12c2c;
}

.focus\:ring-maneb-primary:focus {
  --tw-ring-color: #a12c2c;
}
</style>

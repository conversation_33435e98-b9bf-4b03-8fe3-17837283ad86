<template>
  <div class="bg-white shadow-sm border border-gray-200 rounded-lg p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-4">Dynamic Role System Test</h3>
    
    <!-- Current User Info -->
    <div class="mb-6">
      <h4 class="text-md font-medium text-gray-700 mb-2">Current User Information</h4>
      <div class="bg-gray-50 p-4 rounded-md">
        <p><strong>User:</strong> {{ authStore.user?.fullName || 'N/A' }}</p>
        <p><strong>Role:</strong> {{ authStore.userRole || 'N/A' }}</p>
        <p><strong>Status:</strong> {{ authStore.user?.status || 'N/A' }}</p>
        <p><strong>Is System Admin:</strong> {{ authStore.isSystemAdministrator ? 'Yes' : 'No' }}</p>
        <p><strong>Has Admin Privileges:</strong> {{ authStore.hasAdminPrivileges ? 'Yes' : 'No' }}</p>
        <p><strong>Is Data Clerk:</strong> {{ authStore.isDataClerk ? 'Yes' : 'No' }}</p>
      </div>
    </div>

    <!-- Dynamic Permissions -->
    <div class="mb-6">
      <h4 class="text-md font-medium text-gray-700 mb-2">Dynamic Permissions</h4>
      <div class="bg-gray-50 p-4 rounded-md">
        <p><strong>Permissions Count:</strong> {{ authStore.userPermissions?.length || 0 }}</p>
        <div v-if="authStore.userPermissions && authStore.userPermissions.length > 0" class="mt-2">
          <p class="text-sm font-medium text-gray-600 mb-1">Permissions:</p>
          <ul class="text-sm text-gray-600 space-y-1">
            <li v-for="permission in authStore.userPermissions" :key="permission.id" class="flex items-center">
              <span :class="permission.canAccess ? 'text-green-600' : 'text-red-600'" class="mr-2">
                {{ permission.canAccess ? '✓' : '✗' }}
              </span>
              {{ permission.sectionID }} - {{ permission.action }}
            </li>
          </ul>
        </div>
        <p v-else class="text-sm text-gray-500">No dynamic permissions loaded</p>
      </div>
    </div>

    <!-- Navigation Access -->
    <div class="mb-6">
      <h4 class="text-md font-medium text-gray-700 mb-2">Navigation Access</h4>
      <div class="bg-gray-50 p-4 rounded-md">
        <p><strong>Allowed Navigation:</strong></p>
        <ul class="text-sm text-gray-600 mt-1">
          <li v-for="nav in authStore.getAllowedNavigation" :key="nav" class="flex items-center">
            <span class="text-green-600 mr-2">✓</span>
            {{ nav }}
          </li>
        </ul>
        <p v-if="authStore.getAllowedNavigation.length === 0" class="text-sm text-gray-500">No navigation access</p>
      </div>
    </div>

    <!-- Permission Tests -->
    <div class="mb-6">
      <h4 class="text-md font-medium text-gray-700 mb-2">Permission Tests</h4>
      <div class="bg-gray-50 p-4 rounded-md space-y-2">
        <div class="grid grid-cols-2 gap-4">
          <div>
            <p class="text-sm font-medium text-gray-600">Dashboard Access:</p>
            <p :class="canAccessDashboard ? 'text-green-600' : 'text-red-600'" class="text-sm">
              {{ canAccessDashboard ? 'Allowed' : 'Denied' }}
            </p>
          </div>
          <div>
            <p class="text-sm font-medium text-gray-600">User Management Access:</p>
            <p :class="canAccessUserManagement ? 'text-green-600' : 'text-red-600'" class="text-sm">
              {{ canAccessUserManagement ? 'Allowed' : 'Denied' }}
            </p>
          </div>
          <div>
            <p class="text-sm font-medium text-gray-600">Score Entry Access:</p>
            <p :class="canAccessScoreEntry ? 'text-green-600' : 'text-red-600'" class="text-sm">
              {{ canAccessScoreEntry ? 'Allowed' : 'Denied' }}
            </p>
          </div>
          <div>
            <p class="text-sm font-medium text-gray-600">Reports Access:</p>
            <p :class="canAccessReports ? 'text-green-600' : 'text-red-600'" class="text-sm">
              {{ canAccessReports ? 'Allowed' : 'Denied' }}
            </p>
          </div>
        </div>
      </div>
    </div>

    <!-- Actions -->
    <div class="flex space-x-4">
      <button
        @click="refreshPermissions"
        :disabled="isLoading"
        class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-maneb-primary hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-maneb-primary disabled:opacity-50"
      >
        <svg v-if="isLoading" class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
          <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
        {{ isLoading ? 'Loading...' : 'Refresh Permissions' }}
      </button>
      
      <button
        @click="testPermissionCheck"
        class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-maneb-primary"
      >
        Test Permission Check
      </button>
    </div>

    <!-- Test Results -->
    <div v-if="testResults.length > 0" class="mt-6">
      <h4 class="text-md font-medium text-gray-700 mb-2">Test Results</h4>
      <div class="bg-gray-50 p-4 rounded-md">
        <ul class="text-sm space-y-1">
          <li v-for="(result, index) in testResults" :key="index" :class="result.success ? 'text-green-600' : 'text-red-600'">
            {{ result.message }}
          </li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { useAuthStore } from '@/store/auth.store';

// Store
const authStore = useAuthStore();

// State
const isLoading = ref(false);
const testResults = ref<Array<{ message: string; success: boolean }>>([]);

// Permission checks
const canAccessDashboard = computed(() => {
  return authStore.hasModuleAccess ? authStore.hasModuleAccess('dashboard') : false;
});

const canAccessUserManagement = computed(() => {
  return authStore.hasModuleAccess ? authStore.hasModuleAccess('user_management') : false;
});

const canAccessScoreEntry = computed(() => {
  return authStore.hasModuleAccess ? authStore.hasModuleAccess('score_entry') : false;
});

const canAccessReports = computed(() => {
  return authStore.hasModuleAccess ? authStore.hasModuleAccess('reports') : false;
});

// Methods
const refreshPermissions = async () => {
  isLoading.value = true;
  testResults.value = [];
  
  try {
    await authStore.loadUserPermissions();
    testResults.value.push({
      message: 'Permissions refreshed successfully',
      success: true
    });
  } catch (error) {
    testResults.value.push({
      message: `Failed to refresh permissions: ${error}`,
      success: false
    });
  } finally {
    isLoading.value = false;
  }
};

const testPermissionCheck = () => {
  testResults.value = [];
  
  // Test various permission scenarios
  const tests = [
    {
      name: 'Dashboard View Permission',
      check: () => authStore.hasPermission ? authStore.hasPermission('dashboard', 'View') : false
    },
    {
      name: 'User Management Create Permission',
      check: () => authStore.hasPermission ? authStore.hasPermission('user_management', 'Create') : false
    },
    {
      name: 'Score Entry All Permission',
      check: () => authStore.hasPermission ? authStore.hasPermission('score_entry', 'All') : false
    },
    {
      name: 'System Administrator Check',
      check: () => authStore.isSystemAdministrator
    }
  ];

  tests.forEach(test => {
    try {
      const result = test.check();
      testResults.value.push({
        message: `${test.name}: ${result ? 'PASS' : 'FAIL'}`,
        success: result
      });
    } catch (error) {
      testResults.value.push({
        message: `${test.name}: ERROR - ${error}`,
        success: false
      });
    }
  });
};
</script>

<style scoped>
.text-maneb-primary {
  color: #a12c2c;
}

.bg-maneb-primary {
  background-color: #a12c2c;
}

.border-maneb-primary {
  border-color: #a12c2c;
}

.focus\:ring-maneb-primary:focus {
  --tw-ring-color: #a12c2c;
}
</style>

/**
 * @fileoverview User Management Service for MANEB Results Management System
 * @description Comprehensive service for managing users with full CRUD operations and API alignment
 */

import apiClient from '../core/api-client';
import type { 
  UserDto, 
  CreateUserRequest, 
  UpdateUserRequest,
  UserFilter,
  RecordStatus,
  PaginatedResponse
} from '@/interfaces';

export class UserManagementService {
  /**
   * Get all users
   */
  async getAllUsers(): Promise<UserDto[]> {
    try {
      return await apiClient.get<UserDto[]>('/api/User');
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to fetch users');
    }
  }

  /**
   * Get users with pagination and filters - matching API schema
   */
  async getUsersPaginated(
    pageNumber: number = 1, 
    pageSize: number = 10,
    filters?: {
      RoleId?: string;
      Gender?: 'Male' | 'Female' | 'Other';
      Search?: string;
      SortBy?: string;
      SortDescending?: boolean;
      status?: RecordStatus;
    }
  ): Promise<UserDto[]> {
    try {
      const params = new URLSearchParams({
        PageNumber: pageNumber.toString(),
        PageSize: pageSize.toString()
      });

      // Add filters if provided
      if (filters?.RoleId) params.append('RoleId', filters.RoleId);
      if (filters?.Gender) params.append('Gender', filters.Gender);
      if (filters?.Search) params.append('Search', filters.Search);
      if (filters?.SortBy) params.append('SortBy', filters.SortBy);
      if (filters?.SortDescending !== undefined) params.append('SortDescending', filters.SortDescending.toString());
      if (filters?.status) params.append('status', filters.status);

      return await apiClient.get<UserDto[]>(`/api/User/paginated?${params}`);
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to fetch users');
    }
  }

  /**
   * Get user by ID
   */
  async getUserById(id: string): Promise<UserDto> {
    try {
      return await apiClient.get<UserDto>(`/api/User/${id}`);
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to fetch user');
    }
  }

  /**
   * Create new user
   */
  async createUser(userData: CreateUserRequest): Promise<UserDto> {
    try {
      return await apiClient.post<UserDto>('/api/User', userData);
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to create user');
    }
  }

  /**
   * Update user
   */
  async updateUser(id: string, userData: UpdateUserRequest): Promise<void> {
    try {
      await apiClient.put(`/api/User/${id}`, userData);
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to update user');
    }
  }

  /**
   * Delete user
   */
  async deleteUser(id: string): Promise<void> {
    try {
      await apiClient.delete(`/api/User/${id}`);
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to delete user');
    }
  }

  /**
   * Approve user - matching API endpoint
   */
  async approveUser(id: string): Promise<void> {
    try {
      await apiClient.put(`/api/User/${id}/approve`);
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to approve user');
    }
  }

  /**
   * Filter users using local filtering (for backward compatibility)
   */
  async filterUsers(filters: UserFilter): Promise<UserDto[]> {
    try {
      let users = await this.getAllUsers();

      if (filters.searchQuery) {
        const searchTerm = filters.searchQuery.toLowerCase();
        users = users.filter(user => 
          user.firstName?.toLowerCase().includes(searchTerm) ||
          user.lastName?.toLowerCase().includes(searchTerm) ||
          user.email?.toLowerCase().includes(searchTerm) ||
          user.userName?.toLowerCase().includes(searchTerm)
        );
      }

      if (filters.roleId && filters.roleId !== 'All') {
        users = users.filter(user => 
          user.roles?.some(role => role.id === filters.roleId)
        );
      }

      if (filters.status && filters.status !== 'All') {
        users = users.filter(user => user.status === filters.status);
      }

      if (filters.gender && filters.gender !== 'All') {
        users = users.filter(user => user.gender === filters.gender);
      }

      return users;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to filter users');
    }
  }

  /**
   * Search users by query
   */
  async searchUsers(query: string): Promise<UserDto[]> {
    try {
      return await this.getUsersPaginated(1, 100, { Search: query });
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to search users');
    }
  }

  /**
   * Get users by role
   */
  async getUsersByRole(roleId: string): Promise<UserDto[]> {
    try {
      return await this.getUsersPaginated(1, 100, { RoleId: roleId });
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to fetch users by role');
    }
  }

  /**
   * Get users by status
   */
  async getUsersByStatus(status: RecordStatus): Promise<UserDto[]> {
    try {
      return await this.getUsersPaginated(1, 100, { status });
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to fetch users by status');
    }
  }
}

export const userManagementService = new UserManagementService();

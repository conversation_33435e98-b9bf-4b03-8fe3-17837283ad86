# EMS Frontend Environment Configuration

# Application Configuration
VITE_APP_ID=EMSFRONTEND
VITE_APP_NAME='EMS Frontend'
VITE_APP_VERSION=1.0.0
VITE_APP_SECRET=your-app-secret-here

# API Configuration
# Main EMS API (Local Development)
VITE_API_BASE_URL=https://localhost:7266/

# Grading & Audit API (External Service)
VITE_GRADING_API_BASE_URL=http://************:5000/api

# Developer Information (Optional)
VITE_APP_DEVELOPER_NAME=
VITE_APP_DEVELOPER_WEBSITE=
VITE_APP_DEVELOPER_GITHUB=
VITE_APP_DEVELOPER_EMAIL=
VITE_APP_DEVELOPER_PHONE=
VITE_APP_DEVELOPER_ADDRESS=

# Environment Notes:
# - VITE_API_BASE_URL: Main EMS API for user management, roles, etc.
# - VITE_GRADING_API_BASE_URL: External API for grading, scores, and audit functionality
# - Both APIs require JWT Bearer authentication
# - Make sure to include trailing slash for VITE_API_BASE_URL
# - Do not include trailing slash for VITE_GRADING_API_BASE_URL

import apiClient from '../core/api-client';
import type {
  UserDto,
  CreateUserRequest,
  UpdateUserRequest,
  RecordStatus,
  RoleDto,
  ApiResponse
} from '@/interfaces';
import { getCurrentUserId, addCreateAuditFields, addUpdateAuditFields, validateAuthentication } from '@/utils/auth/auth-utils';

export class UserService {

  /**
   * Helper method to safely extract string value from array or string (same as auth store)
   */
  private extractStringValue(value: any, propertyName: string): string | null {
    console.log(`🔍 UserService extracting ${propertyName}:`, typeof value, value);

    if (Array.isArray(value)) {
      if (value.length > 0 && typeof value[0] === 'string') {
        console.log(`✅ ${propertyName} is array, using first element:`, value[0]);
        return value[0];
      } else {
        console.log(`❌ ${propertyName} is empty array or first element is not string`);
        return null;
      }
    } else if (typeof value === 'string') {
      console.log(`✅ ${propertyName} is string:`, value);
      return value;
    } else {
      console.log(`❌ ${propertyName} is neither string nor array:`, typeof value, value);
      return null;
    }
  }

  /**
   * Helper method to get user roles (handles both new single role and legacy roles array)
   * WITH ARRAY/STRING SUPPORT for role.name and role.id properties
   */
  private getUserRoles(user: UserDto): RoleDto[] {
    console.log('🔍 UserService getUserRoles - User object:', user);
    console.log('🔍 UserService getUserRoles - User.roles:', user.roles);
    console.log('🔍 UserService getUserRoles - User.role:', user.role);

    const roles: RoleDto[] = [];

    // Handle legacy roles array format
    if (user.roles && user.roles.length > 0) {
      console.log('✅ Found legacy roles array format');
      // Process each role to handle array/string properties
      user.roles.forEach((role, index) => {
        const processedRole = this.processRoleObject(role, `roles[${index}]`);
        if (processedRole) {
          roles.push(processedRole);
        }
      });
    }

    // Handle new single role format
    if (user.role) {
      console.log('✅ Found single role format');
      const processedRole = this.processRoleObject(user.role, 'role');
      if (processedRole) {
        roles.push(processedRole);
      }
    }

    console.log('✅ UserService getUserRoles final result:', roles);
    return roles;
  }

  /**
   * Process a role object to handle array/string properties safely
   */
  private processRoleObject(role: any, context: string): RoleDto | null {
    if (!role) {
      console.log(`❌ ${context} is null/undefined`);
      return null;
    }

    console.log(`🔍 Processing ${context}:`, role);

    // Extract role name (handle array/string)
    const roleName = role.name ? this.extractStringValue(role.name, `${context}.name`) : null;

    // Extract role ID (handle array/string)
    const roleId = role.id ? this.extractStringValue(role.id, `${context}.id`) : null;

    if (!roleName && !roleId) {
      console.log(`❌ ${context} has no valid name or id`);
      return null;
    }

    // Create processed role object
    const processedRole: RoleDto = {
      id: roleId || role.id || '',
      name: roleName || role.name || '',
      status: role.status || 'Unapproved',
      createdBy: role.createdBy || '',
      dateCreated: role.dateCreated || new Date().toISOString(),
      isDeleted: role.isDeleted || false,
      rolePermissions: role.rolePermissions || []
    };

    console.log(`✅ Processed ${context}:`, processedRole);
    return processedRole;
  }

  /**
   * Get all users with enhanced error handling
   */
  async getAllUsers(): Promise<UserDto[]> {
    try {
      const response = await apiClient.get<UserDto[]>('/api/User');
      return response || [];
    } catch (error: any) {
      console.error('Error fetching users:', error);
      throw new Error(
        error.response?.data?.message ||
        error.response?.data?.title ||
        'Failed to fetch users'
      );
    }
  }

  /**
   * Get users with standardized query parameters
   */
  async getUsersWithFilters(options: {
    pageNumber?: number;
    pageSize?: number;
    search?: string;
    sortBy?: string;
    sortDescending?: boolean;
    roleId?: string;
    gender?: 'Male' | 'Female' | 'Other';
  } = {}): Promise<UserDto[]> {
    try {
      // Build query parameters using standardized structure
      const params = new URLSearchParams();

      // Standard pagination parameters
      if (options.pageNumber) params.append('PageNumber', options.pageNumber.toString());
      if (options.pageSize) params.append('PageSize', options.pageSize.toString());
      if (options.search) params.append('Search', options.search);
      if (options.sortBy) params.append('SortBy', options.sortBy);
      if (options.sortDescending !== undefined) params.append('SortDescending', options.sortDescending.toString());

      // User-specific filter parameters (based on API documentation)
      if (options.roleId) params.append('RoleId', options.roleId);
      if (options.gender) params.append('Gender', options.gender);

      const url = params.toString() ? `/api/User/paginated?${params.toString()}` : '/api/User';
      const response = await apiClient.get<UserDto[]>(url);
      return response || [];
    } catch (error: any) {
      console.error('Error fetching users with filters:', error);
      throw new Error(
        error.response?.data?.message ||
        error.response?.data?.title ||
        'Failed to fetch users with filters'
      );
    }
  }

  /**
   * Get user by ID with enhanced error handling
   */
  async getUserById(id: string): Promise<UserDto> {
    try {
      if (!id) {
        throw new Error('User ID is required');
      }
      return await apiClient.get<UserDto>(`/api/User/${id}`);
    } catch (error: any) {
      console.error(`Error fetching user ${id}:`, error);
      throw new Error(
        error.response?.data?.message ||
        error.response?.data?.title ||
        'Failed to fetch user'
      );
    }
  }

  /**
   * Create new user with validation
   */
  async createUser(userData: CreateUserRequest): Promise<UserDto> {
    try {
      // Validate required fields
      if (!userData.firstName || !userData.lastName) {
        throw new Error('First name and last name are required');
      }

      // Validate authentication
      validateAuthentication();

      // Ensure proper data format
      const baseData = {
        ...userData,
        dateOfBirth: userData.dateOfBirth ? new Date(userData.dateOfBirth).toISOString() : undefined,
        status: userData.status || 'Unapproved' as RecordStatus
      };

      // Add audit fields
      const formattedData = addCreateAuditFields(baseData);

      return await apiClient.post<UserDto>('/api/User', formattedData);
    } catch (error: any) {
      console.error('Error creating user:', error);
      throw new Error(
        error.response?.data?.message ||
        error.response?.data?.title ||
        'Failed to create user'
      );
    }
  }

  /**
   * Update user with enhanced validation and error handling
   */
  async updateUser(id: string, userData: UpdateUserRequest): Promise<UserDto> {
    try {
      if (!id) {
        throw new Error('User ID is required');
      }

      // Validate authentication
      validateAuthentication();

      // Get the current user first to ensure we have all required fields
      const currentUser = await this.getUserById(id);

      // Build complete user object with ALL required fields for PUT request
      const completeUserData = {
        // Required base fields
        id: id,
        createdBy: currentUser.createdBy || getCurrentUserId(),
        dateCreated: currentUser.dateCreated || new Date().toISOString(),
        status: userData.status || currentUser.status || 'Unapproved',
        isDeleted: currentUser.isDeleted || false,

        // User profile fields
        firstName: userData.firstName || currentUser.firstName,
        lastName: userData.lastName || currentUser.lastName,
        gender: userData.gender || currentUser.gender,
        idNumber: userData.idNumber || currentUser.idNumber,
        idType: userData.idType || currentUser.idType,
        dateOfBirth: userData.dateOfBirth ?
          new Date(userData.dateOfBirth).toISOString() :
          (currentUser.dateOfBirth ?
            (typeof currentUser.dateOfBirth === 'string' ? currentUser.dateOfBirth : currentUser.dateOfBirth.toISOString()) :
            null),

        // Authentication fields
        email: userData.email || currentUser.email,
        userName: userData.userName || currentUser.userName,

        // Roles array (preserve existing roles)
        roles: currentUser.roles || []
      };

      console.log('Updating user with complete data:', completeUserData);
      console.log('API endpoint:', `/api/User/${id}`);

      const response = await apiClient.put<UserDto>(`/api/User/${id}`, completeUserData);
      return response;
    } catch (error: any) {
      console.error(`Error updating user ${id}:`, error);
      console.error('Error response:', error.response?.data);
      console.error('Error status:', error.response?.status);
      console.error('Error headers:', error.response?.headers);

      // Enhanced error message with more details
      const errorMessage = error.response?.data?.message ||
                          error.response?.data?.title ||
                          error.response?.data?.errors?.[0] ||
                          error.message ||
                          'Failed to update user';

      throw new Error(errorMessage);
    }
  }

  /**
   * Delete user with confirmation
   */
  async deleteUser(id: string): Promise<void> {
    try {
      if (!id) {
        throw new Error('User ID is required');
      }
      await apiClient.delete(`/api/User/${id}`);
    } catch (error: any) {
      console.error(`Error deleting user ${id}:`, error);
      throw new Error(
        error.response?.data?.message ||
        error.response?.data?.title ||
        'Failed to delete user'
      );
    }
  }

  /**
   * Get users by status with enhanced filtering
   */
  async getUsersByStatus(status: RecordStatus): Promise<UserDto[]> {
    try {
      const users = await this.getAllUsers();
      return users.filter(user => user.status === status);
    } catch (error: any) {
      console.error(`Error fetching users by status ${status}:`, error);
      throw new Error(
        error.response?.data?.message ||
        error.response?.data?.title ||
        'Failed to fetch users by status'
      );
    }
  }

  /**
   * Update user status with validation
   */
  async updateUserStatus(id: string, status: RecordStatus): Promise<UserDto> {
    try {
      if (!id) {
        throw new Error('User ID is required');
      }

      if (!['Unapproved', 'Approved', 'SecondApproved', 'Rejected'].includes(status)) {
        throw new Error('Invalid status value');
      }

      // Use the updateUser method which now handles complete object structure
      return await this.updateUser(id, { status });
    } catch (error: any) {
      console.error(`Error updating user status for ${id}:`, error);
      throw new Error(
        error.response?.data?.message ||
        error.response?.data?.title ||
        'Failed to update user status'
      );
    }
  }

  /**
   * Search users with enhanced filtering capabilities
   */
  async searchUsers(query: string): Promise<UserDto[]> {
    try {
      if (!query || query.trim().length === 0) {
        return await this.getAllUsers();
      }

      const users = await this.getAllUsers();
      const searchTerm = query.toLowerCase().trim();

      return users.filter(user =>
        user.firstName?.toLowerCase().includes(searchTerm) ||
        user.lastName?.toLowerCase().includes(searchTerm) ||
        user.email?.toLowerCase().includes(searchTerm) ||
        user.fullName?.toLowerCase().includes(searchTerm) ||
        user.userName?.toLowerCase().includes(searchTerm) ||
        user.idNumber?.toLowerCase().includes(searchTerm)
      );
    } catch (error: any) {
      console.error('Error searching users:', error);
      throw new Error(
        error.response?.data?.message ||
        error.response?.data?.title ||
        'Failed to search users'
      );
    }
  }

  /**
   * Get users with enhanced pagination and filtering
   */
  async getUsersPaginated(
    page: number = 1,
    limit: number = 10,
    filters?: {
      status?: RecordStatus | 'All';
      searchQuery?: string;
      roleId?: string;
    }
  ): Promise<{
    users: UserDto[];
    total: number;
    page: number;
    totalPages: number;
    filters?: any;
  }> {
    try {
      let allUsers = await this.getAllUsers();

      // Apply filters
      if (filters) {
        if (filters.status && filters.status !== 'All') {
          allUsers = allUsers.filter(user => user.status === filters.status);
        }

        if (filters.searchQuery && filters.searchQuery.trim()) {
          const searchTerm = filters.searchQuery.toLowerCase().trim();
          allUsers = allUsers.filter(user =>
            user.firstName?.toLowerCase().includes(searchTerm) ||
            user.lastName?.toLowerCase().includes(searchTerm) ||
            user.email?.toLowerCase().includes(searchTerm) ||
            user.fullName?.toLowerCase().includes(searchTerm) ||
            user.userName?.toLowerCase().includes(searchTerm) ||
            user.idNumber?.toLowerCase().includes(searchTerm)
          );
        }

        if (filters.roleId && filters.roleId !== 'All') {
          allUsers = allUsers.filter(user => {
            // Use the robust getUserRoles method that handles array/string data types
            const userRoles = this.getUserRoles(user);
            return userRoles.some(role => {
              // Handle both array and string formats for role.id
              const roleId = role.id ? this.extractStringValue(role.id, 'filter.role.id') : null;
              return roleId === filters.roleId;
            });
          });
        }
      }

      const startIndex = (page - 1) * limit;
      const endIndex = startIndex + limit;

      return {
        users: allUsers.slice(startIndex, endIndex),
        total: allUsers.length,
        page,
        totalPages: Math.ceil(allUsers.length / limit),
        filters
      };
    } catch (error: any) {
      console.error('Error fetching paginated users:', error);
      throw new Error(
        error.response?.data?.message ||
        error.response?.data?.title ||
        'Failed to fetch paginated users'
      );
    }
  }

  /**
   * Add role to user using the new UserRoles/assign endpoint
   * ENFORCES SINGLE ROLE PER USER: Removes existing roles before adding new one
   */
  async addRoleToUser(userId: string, roleId: string): Promise<UserDto> {
    try {
      if (!userId || !roleId) {
        throw new Error('User ID and Role ID are required');
      }

      console.log(`Adding role ${roleId} to user ${userId} using UserRoles/assign endpoint`);
      console.log('🔒 SINGLE ROLE ENFORCEMENT: Checking for existing roles...');

      // Get current user to check existing roles
      const currentUser = await this.getUserById(userId);

      // Check if user has existing roles and remove them first (SINGLE ROLE ENFORCEMENT)
      const existingRoles = this.getUserRoles(currentUser);
      if (existingRoles.length > 0) {
        console.log(`🔒 User has ${existingRoles.length} existing role(s). Removing them first...`);

        for (const existingRole of existingRoles) {
          if (existingRole.id && existingRole.id !== roleId) {
            console.log(`🗑️ Removing existing role: ${existingRole.name} (${existingRole.id})`);
            try {
              await this.removeRoleFromUser(userId, existingRole.id);
            } catch (removeError) {
              console.warn(`Warning: Could not remove existing role ${existingRole.id}:`, removeError);
              // Continue with assignment even if removal fails
            }
          }
        }
      } else {
        console.log('✅ User has no existing roles. Proceeding with assignment...');
      }

      // Use the new UserRoles/assign endpoint
      const assignmentData = {
        userId: userId,
        roleId: roleId
      };

      console.log('Sending role assignment request:', JSON.stringify(assignmentData, null, 2));

      // Call the new endpoint
      await apiClient.post('/api/UserRoles/assign', assignmentData);

      // Fetch and return updated user data
      const updatedUser = await this.getUserById(userId);
      console.log('✅ Single role assignment successful, updated user:', updatedUser);

      return updatedUser;
    } catch (error: any) {
      console.error(`Error adding role ${roleId} to user ${userId}:`, error);
      throw new Error(
        error.response?.data?.message ||
        error.response?.data?.title ||
        error.message ||
        'Failed to add role to user'
      );
    }
  }

  /**
   * Remove role from user using the UserRoles/remove endpoint
   */
  async removeRoleFromUser(userId: string, roleId: string): Promise<UserDto> {
    try {
      if (!userId || !roleId) {
        throw new Error('User ID and Role ID are required');
      }

      console.log(`Removing role ${roleId} from user ${userId} using UserRoles/remove endpoint`);

      // Check if user has this role using the dedicated check endpoint
      const hasRole = await this.checkUserHasRole(userId, roleId);
      if (!hasRole) {
        throw new Error('User does not have this role assigned');
      }

      // Use the dedicated remove endpoint
      console.log(`Calling DELETE /api/UserRoles/remove/${userId}/${roleId}`);

      // Call the remove endpoint
      await apiClient.delete(`/api/UserRoles/remove/${userId}/${roleId}`);

      // Fetch and return updated user data
      const updatedUser = await this.getUserById(userId);
      console.log('Role removal successful, updated user:', updatedUser);

      return updatedUser;
    } catch (error: any) {
      console.error(`Error removing role ${roleId} from user ${userId}:`, error);
      throw new Error(
        error.response?.data?.message ||
        error.response?.data?.title ||
        error.message ||
        'Failed to remove role from user'
      );
    }
  }

  /**
   * Remove all roles from user by removing each role individually
   */
  async removeAllRolesFromUser(userId: string): Promise<UserDto> {
    try {
      if (!userId) {
        throw new Error('User ID is required');
      }

      console.log(`Removing all roles from user ${userId}`);

      // Get current user to check existing roles
      const currentUser = await this.getUserById(userId);

      // Collect all role IDs to remove
      const roleIdsToRemove: string[] = [];

      // Handle both legacy roles array and new single role format
      if (currentUser.roles && currentUser.roles.length > 0) {
        roleIdsToRemove.push(...currentUser.roles.map(r => r.id).filter(id => id !== undefined) as string[]);
      }
      if (currentUser.role && currentUser.role.id) {
        roleIdsToRemove.push(currentUser.role.id);
      }

      // Remove duplicates
      const uniqueRoleIds = [...new Set(roleIdsToRemove)];

      if (uniqueRoleIds.length === 0) {
        console.log('User has no roles to remove');
        return currentUser;
      }

      console.log(`Removing ${uniqueRoleIds.length} roles:`, uniqueRoleIds);

      // Remove each role individually using the remove endpoint
      for (const roleId of uniqueRoleIds) {
        try {
          console.log(`Removing role ${roleId} from user ${userId}`);
          await apiClient.delete(`/api/UserRoles/remove/${userId}/${roleId}`);
        } catch (roleError: any) {
          console.error(`Failed to remove role ${roleId}:`, roleError);
          // Continue with other roles even if one fails
        }
      }

      // Fetch and return updated user data
      const updatedUser = await this.getUserById(userId);
      console.log('All roles removal completed, updated user:', updatedUser);

      return updatedUser;
    } catch (error: any) {
      console.error(`Error removing all roles from user ${userId}:`, error);
      throw new Error(
        error.response?.data?.message ||
        error.response?.data?.title ||
        error.message ||
        'Failed to remove all roles from user'
      );
    }
  }

  /**
   * Check if user has a specific role using the UserRoles/check endpoint
   */
  async checkUserHasRole(userId: string, roleId: string): Promise<boolean> {
    try {
      if (!userId || !roleId) {
        throw new Error('User ID and Role ID are required');
      }

      console.log(`Checking if user ${userId} has role ${roleId}`);

      // Use the check endpoint
      const response = await apiClient.get<boolean>(`/api/UserRoles/check/${userId}/${roleId}`);

      console.log(`User ${userId} has role ${roleId}:`, response);
      return response;
    } catch (error: any) {
      console.error(`Error checking if user ${userId} has role ${roleId}:`, error);
      // Return false if check fails (user doesn't have role or error occurred)
      return false;
    }
  }

  /**
   * Get all roles for a specific user using the UserRoles/user endpoint
   */
  async getUserRolesByUserId(userId: string): Promise<RoleDto[]> {
    try {
      if (!userId) {
        throw new Error('User ID is required');
      }

      console.log(`Getting roles for user ${userId}`);

      // Use the user roles endpoint
      const response = await apiClient.get<RoleDto[]>(`/api/UserRoles/user/${userId}`);

      console.log(`Roles for user ${userId}:`, response);
      return response;
    } catch (error: any) {
      console.error(`Error getting roles for user ${userId}:`, error);
      throw new Error(
        error.response?.data?.message ||
        error.response?.data?.title ||
        error.message ||
        'Failed to get user roles'
      );
    }
  }

  /**
   * Get all users with a specific role using the UserRoles/role endpoint
   */
  async getUsersWithRole(roleId: string): Promise<UserDto[]> {
    try {
      if (!roleId) {
        throw new Error('Role ID is required');
      }

      console.log(`Getting users with role ${roleId}`);

      // Use the role users endpoint
      const response = await apiClient.get<UserDto[]>(`/api/UserRoles/role/${roleId}`);

      console.log(`Users with role ${roleId}:`, response);
      return response;
    } catch (error: any) {
      console.error(`Error getting users with role ${roleId}:`, error);
      throw new Error(
        error.response?.data?.message ||
        error.response?.data?.title ||
        error.message ||
        'Failed to get users with role'
      );
    }
  }

  /**
   * Update user roles array
   */
  private async updateUserRoles(userId: string, roles: RoleDto[]): Promise<UserDto> {
    try {
      // Get current user data
      const currentUser = await this.getUserById(userId);

      // For the API schema, we need to send a single role object and roleId
      // If multiple roles, we'll use the first one (or handle differently based on business logic)
      const primaryRole = roles.length > 0 ? roles[0] : null;

      // Build complete user object according to API schema
      const completeUserData = {
        // Required base fields
        id: userId,
        createdBy: currentUser.createdBy || getCurrentUserId(),
        dateCreated: currentUser.dateCreated || new Date().toISOString(),
        status: currentUser.status || 'Unapproved',
        isDeleted: currentUser.isDeleted || false,

        // User profile fields
        firstName: currentUser.firstName,
        lastName: currentUser.lastName,
        gender: currentUser.gender,
        idNumber: currentUser.idNumber,
        idType: currentUser.idType,
        dateOfBirth: currentUser.dateOfBirth ?
          (typeof currentUser.dateOfBirth === 'string' ? currentUser.dateOfBirth : currentUser.dateOfBirth.toISOString()) :
          null,

        // Authentication fields
        email: currentUser.email,
        userName: currentUser.userName,

        // Role fields according to API schema
        roleId: primaryRole?.id || null,
        role: primaryRole ? {
          id: primaryRole.id,
          createdBy: primaryRole.createdBy || getCurrentUserId(),
          dateCreated: primaryRole.dateCreated || new Date().toISOString(),
          status: primaryRole.status || 'Unapproved',
          isDeleted: primaryRole.isDeleted || false,
          name: primaryRole.name,
          rolePermissions: primaryRole.rolePermissions || []
        } : null
      };

      console.log('Updating user roles with data:', completeUserData);

      const response = await apiClient.put<UserDto>(`/api/User/${userId}`, completeUserData);
      return response;
    } catch (error: any) {
      console.error(`Error updating user roles for ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Bulk update user status
   */
  async bulkUpdateUserStatus(userIds: string[], status: RecordStatus): Promise<{
    success: string[];
    failed: { id: string; error: string }[];
  }> {
    const results = {
      success: [] as string[],
      failed: [] as { id: string; error: string }[]
    };

    for (const userId of userIds) {
      try {
        await this.updateUserStatus(userId, status);
        results.success.push(userId);
      } catch (error: any) {
        results.failed.push({
          id: userId,
          error: error.message || 'Failed to update status'
        });
      }
    }

    return results;
  }

  /**
   * Calculate user statistics from provided user array
   */
  calculateUserStatistics(users: UserDto[]): {
    total: number;
    byStatus: Record<RecordStatus, number>;
    byRole: Record<string, number>;
    active: number;
    deleted: number;
  } {
    const stats = {
      total: users.length,
      byStatus: {
        'Unapproved': 0,
        'Approved': 0,
        'SecondApproved': 0,
        'Rejected': 0
      } as Record<RecordStatus, number>,
      byRole: {} as Record<string, number>,
      active: users.filter(u => !u.isDeleted).length,
      deleted: users.filter(u => u.isDeleted).length
    };

    users.forEach(user => {
      // Count by status
      if (user.status) {
        stats.byStatus[user.status] = (stats.byStatus[user.status] || 0) + 1;
      }

      // Count by roles (with array/string support)
      const userRoles = this.getUserRoles(user);
      if (userRoles.length > 0) {
        userRoles.forEach(role => {
          // Handle both array and string formats for role.name
          const roleName = role.name ? this.extractStringValue(role.name, 'stats.role.name') : null;
          if (roleName) {
            stats.byRole[roleName] = (stats.byRole[roleName] || 0) + 1;
          }
        });
      } else {
        // Count users with no roles
        stats.byRole['No Role'] = (stats.byRole['No Role'] || 0) + 1;
      }
    });

    return stats;
  }

  /**
   * Get user statistics (deprecated - use calculateUserStatistics with existing data)
   */
  async getUserStatistics(): Promise<{
    total: number;
    byStatus: Record<RecordStatus, number>;
    byRole: Record<string, number>;
    active: number;
    deleted: number;
  }> {
    try {
      const users = await this.getAllUsers();
      return this.calculateUserStatistics(users);
    } catch (error: any) {
      console.error('Error fetching user statistics:', error);
      throw new Error(
        error.response?.data?.message ||
        error.response?.data?.title ||
        'Failed to fetch user statistics'
      );
    }
  }

  /**
   * Validate user data before submission
   */
  validateUserData(userData: CreateUserRequest | UpdateUserRequest): {
    isValid: boolean;
    errors: string[];
  } {
    const errors: string[] = [];

    // Check required fields for creation
    if ('firstName' in userData && 'lastName' in userData) {
      if (!userData.firstName?.trim()) {
        errors.push('First name is required');
      }
      if (!userData.lastName?.trim()) {
        errors.push('Last name is required');
      }
    }

    // Validate email format
    if (userData.email) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(userData.email)) {
        errors.push('Invalid email format');
      }
    }

    // Validate date of birth
    if (userData.dateOfBirth) {
      const dob = new Date(userData.dateOfBirth);
      const now = new Date();
      if (dob > now) {
        errors.push('Date of birth cannot be in the future');
      }
    }

    // Validate gender
    if (userData.gender && !['Male', 'Female', 'Other'].includes(userData.gender)) {
      errors.push('Invalid gender value');
    }

    // Validate status
    if (userData.status && !['Unapproved', 'Approved', 'SecondApproved', 'Rejected'].includes(userData.status)) {
      errors.push('Invalid status value');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }
}

export const userService = new UserService();

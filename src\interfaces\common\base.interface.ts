/**
 * @fileoverview Base interfaces and common types for MANEB Results Management System
 * @description Contains shared interfaces used across all modules
 */

// ============================================================================
// ENUMS
// ============================================================================

/**
 * Student attendance status enum
 * Maps to the StudentStatus enum from the API
 */
export enum StudentStatus {
  Present = 0,
  Absent = 1,
  Missing = 2
}

/**
 * Base entity interface with common system fields
 * @description All database entities extend this interface
 */
export interface BaseEntity {
  /** Unique identifier for the entity */
  id?: string;
  /** ID of the user who created this record */
  createdBy?: string;
  /** Timestamp when the record was created */
  dateCreated?: Date | string;
  /** Soft delete flag */
  isDeleted?: boolean;
}

/**
 * Record status for approval workflows
 * @description Status values used in the approval process
 */
export type RecordStatus = 'Unapproved' | 'Approved' | 'SecondApproved' | 'Rejected';

/**
 * Gender options
 * @description Standard gender classifications
 */
export type Gender = 'Male' | 'Female' | 'Other';

/**
 * Permission actions for role-based access control
 * @description Available actions that can be granted to roles - matching API schema
 */
export type PermissionAction =
  | 'Login'
  | 'Logout'
  | 'Update'
  | 'Delete'
  | 'Authorise'
  | 'Approve'
  | 'Reject'
  | 'SecondApprove'
  | 'All'
  | 'View'
  | 'ViewAll'
  | 'Create'
  | 'Check'
  | 'Verify'
  | 'Active';

/**
 * Audit action types for system logging
 * @description Types of actions that are logged in the audit trail
 */
export type AuditAction = 
  | 'Login' 
  | 'Logout' 
  | 'Create' 
  | 'Update' 
  | 'Delete' 
  | 'View' 
  | 'Export' 
  | 'Import' 
  | 'Approve' 
  | 'Reject' 
  | 'StatusChange' 
  | 'RoleAssign' 
  | 'PermissionChange';

/**
 * Audit status for logged actions
 * @description Status of audit log entries
 */
export type AuditStatus = 'Success' | 'Failed' | 'Warning' | 'Info';

/**
 * Standard API response wrapper
 * @template T - The type of data being returned
 * @description Consistent response format for all API calls
 */
export interface ApiResponse<T = any> {
  /** The response data */
  data?: T;
  /** Human-readable message */
  message?: string;
  /** Whether the operation was successful */
  success: boolean;
  /** Array of error messages if any */
  errors?: string[];
}

/**
 * Paginated response wrapper
 * @template T - The type of items in the data array
 * @description Response format for paginated API calls
 */
export interface PaginatedResponse<T> {
  /** Array of items for the current page */
  data: T[];
  /** Total number of items across all pages */
  totalCount: number;
  /** Current page number (1-based) */
  pageNumber: number;
  /** Number of items per page */
  pageSize: number;
  /** Total number of pages */
  totalPages: number;
}

/**
 * Error response format
 * @description Standard error response structure
 */
export interface ApiError {
  /** Error message */
  message: string;
  /** HTTP status code */
  statusCode: number;
  /** Additional error details */
  details?: string;
  /** Timestamp when the error occurred */
  timestamp: Date;
}

/**
 * API client configuration
 * @description Configuration options for API clients
 */
export interface ApiClientConfig {
  /** Base URL for the API */
  baseURL: string;
  /** Request timeout in milliseconds */
  timeout?: number;
}

/**
 * Request configuration options
 * @description Additional options for API requests
 */
export interface RequestConfig {
  /** Custom headers for the request */
  headers?: Record<string, string>;
  /** Query parameters */
  params?: Record<string, any>;
}

/**
 * Filter base interface
 * @description Common filtering options for list queries
 */
export interface BaseFilter {
  /** Search query string */
  searchQuery?: string;
  /** Page number for pagination (1-based) */
  page?: number;
  /** Number of items per page */
  pageSize?: number;
  /** Field to sort by */
  sortBy?: string;
  /** Sort direction */
  sortDirection?: 'asc' | 'desc';
}

/**
 * Date range filter
 * @description Filter for date-based queries
 */
export interface DateRangeFilter {
  /** Start date for the range */
  dateFrom?: Date | string;
  /** End date for the range */
  dateTo?: Date | string;
}

/**
 * Status filter options
 * @description Common status filtering options
 */
export interface StatusFilter {
  /** Record status filter */
  status?: RecordStatus | 'All';
  /** Active/inactive filter */
  isActive?: boolean | 'All';
}

/**
 * Create request base interface
 * @template T - The entity type being created
 * @description Base interface for create operations
 */
export type CreateRequest<T extends BaseEntity> = Omit<T, 'id' | 'createdBy' | 'dateCreated'>;

/**
 * Update request base interface
 * @template T - The entity type being updated
 * @description Base interface for update operations
 */
export type UpdateRequest<T extends BaseEntity> = Partial<CreateRequest<T>>;

/**
 * Bulk operation request
 * @template T - The type of items being processed
 * @description Interface for bulk operations
 */
export interface BulkOperationRequest<T> {
  /** Array of items to process */
  items: T[];
  /** Operation to perform */
  operation: 'create' | 'update' | 'delete';
  /** Additional options */
  options?: Record<string, any>;
}

/**
 * Bulk operation response
 * @template T - The type of items that were processed
 * @description Response format for bulk operations
 */
export interface BulkOperationResponse<T> {
  /** Whether the overall operation was successful */
  success: boolean;
  /** Successfully processed items */
  successfulItems: T[];
  /** Items that failed to process */
  failedItems: Array<{
    item: T;
    error: string;
  }>;
  /** Summary statistics */
  summary: {
    total: number;
    successful: number;
    failed: number;
  };
}

/**
 * Validation error details
 * @description Detailed validation error information
 */
export interface ValidationError {
  /** Field that failed validation */
  field: string;
  /** Error message */
  message: string;
  /** Invalid value that was provided */
  value?: any;
  /** Validation rule that was violated */
  rule?: string;
}

/**
 * Form validation result
 * @description Result of form validation
 */
export interface ValidationResult {
  /** Whether validation passed */
  isValid: boolean;
  /** Array of validation errors */
  errors: ValidationError[];
  /** Warnings that don't prevent submission */
  warnings?: string[];
}

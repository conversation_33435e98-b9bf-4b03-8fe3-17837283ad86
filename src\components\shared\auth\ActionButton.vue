<!--
  @fileoverview Action Button with Permission Checking
  @description Button component that shows/hides based on user permissions
-->

<template>
  <PermissionGuard
    :module="module"
    :action="action"
    :any-actions="anyActions"
    :all-actions="allActions"
    :role="role"
    :not="not"
  >
    <button
      :type="type"
      :disabled="disabled || isLoading"
      :class="buttonClasses"
      @click="handleClick"
    >
      <!-- Loading spinner -->
      <svg
        v-if="isLoading"
        class="animate-spin -ml-1 mr-2 h-4 w-4"
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        viewBox="0 0 24 24"
      >
        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
      </svg>

      <!-- Icon -->
      <component
        v-if="icon && !isLoading"
        :is="icon"
        :class="iconClasses"
      />

      <!-- Text -->
      <span v-if="$slots.default || text">
        <slot>{{ text }}</slot>
      </span>
    </button>
  </PermissionGuard>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import PermissionGuard from './PermissionGuard.vue'
import { PermissionModule, PermissionAction } from '@/utils/permissions'

interface Props {
  /** Button text */
  text?: string
  /** Button type */
  type?: 'button' | 'submit' | 'reset'
  /** Button variant */
  variant?: 'primary' | 'secondary' | 'danger' | 'success' | 'warning' | 'ghost'
  /** Button size */
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl'
  /** Disabled state */
  disabled?: boolean
  /** Loading state */
  isLoading?: boolean
  /** Icon component */
  icon?: any
  /** Icon position */
  iconPosition?: 'left' | 'right'
  /** Module to check permission for */
  module?: PermissionModule
  /** Action to check permission for */
  action?: PermissionAction
  /** Multiple actions - user needs ANY of these */
  anyActions?: PermissionAction[]
  /** Multiple actions - user needs ALL of these */
  allActions?: PermissionAction[]
  /** Role to check directly */
  role?: string
  /** Invert permission check */
  not?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  type: 'button',
  variant: 'primary',
  size: 'md',
  disabled: false,
  isLoading: false,
  iconPosition: 'left',
  not: false
})

const emit = defineEmits<{
  click: [event: MouseEvent]
}>()

/**
 * Button CSS classes based on variant and size
 */
const buttonClasses = computed(() => {
  const baseClasses = [
    'inline-flex items-center justify-center font-medium rounded-lg transition-colors duration-200',
    'focus:outline-none focus:ring-2 focus:ring-offset-2',
    'disabled:opacity-50 disabled:cursor-not-allowed'
  ]

  // Size classes
  const sizeClasses = {
    xs: 'px-2 py-1 text-xs',
    sm: 'px-3 py-1.5 text-sm',
    md: 'px-4 py-2 text-sm',
    lg: 'px-6 py-3 text-base',
    xl: 'px-8 py-4 text-lg'
  }

  // Variant classes
  const variantClasses = {
    primary: 'bg-maneb-primary text-white hover:bg-red-700 focus:ring-maneb-primary',
    secondary: 'bg-gray-200 text-gray-900 hover:bg-gray-300 focus:ring-gray-500',
    danger: 'bg-red-600 text-white hover:bg-red-700 focus:ring-red-500',
    success: 'bg-green-600 text-white hover:bg-green-700 focus:ring-green-500',
    warning: 'bg-yellow-600 text-white hover:bg-yellow-700 focus:ring-yellow-500',
    ghost: 'text-gray-700 hover:bg-gray-100 focus:ring-gray-500'
  }

  return [
    ...baseClasses,
    sizeClasses[props.size],
    variantClasses[props.variant]
  ].join(' ')
})

/**
 * Icon CSS classes
 */
const iconClasses = computed(() => {
  const baseClasses = ['flex-shrink-0']
  
  const sizeClasses = {
    xs: 'h-3 w-3',
    sm: 'h-4 w-4',
    md: 'h-4 w-4',
    lg: 'h-5 w-5',
    xl: 'h-6 w-6'
  }

  const positionClasses = {
    left: props.text || props.$slots.default ? 'mr-2' : '',
    right: props.text || props.$slots.default ? 'ml-2' : ''
  }

  return [
    ...baseClasses,
    sizeClasses[props.size],
    positionClasses[props.iconPosition]
  ].join(' ')
})

/**
 * Handle button click
 */
const handleClick = (event: MouseEvent) => {
  if (!props.disabled && !props.isLoading) {
    emit('click', event)
  }
}
</script>

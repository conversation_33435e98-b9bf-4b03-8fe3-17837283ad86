<template>
  <div class="bg-white p-6 rounded-2xl shadow-md w-full max-w-md">
    <!-- Header -->
    <div class="flex items-center justify-between mb-6">
      <h2 class="text-xl font-bold text-gray-800">Your Transactions</h2>
      <div class="flex items-center text-gray-500 text-sm">
        <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path :d="props.calendarIconSvgPath"></path>
        </svg>
        <span>{{ props.dateRangeText }}</span>
      </div>
    </div>

    <!-- Transaction Groups -->
    <div v-for="groupData in props.transactions" :key="groupData.group" class="mb-6 last:mb-0">
      <h3 class="text-gray-500 text-xs font-semibold uppercase mb-3">{{ groupData.group }}</h3>
      <ul>
        <li v-for="(item, index) in groupData.items" :key="index" class="flex items-center justify-between py-3 border-b border-gray-200 last:border-b-0">
          <div class="flex items-center">
            <!-- Icon based on type -->
            <div :class="['w-8 h-8 rounded-full flex items-center justify-center mr-3', {
              'bg-red-100 text-red-500': item.type === 'expense',
              'bg-green-100 text-green-500': item.type === 'income',
              'bg-blue-100 text-blue-500': item.type === 'pending'
            }]">
              <svg v-if="item.type === 'expense'" class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 13l-4-4h8l-4 4z"></path>
              </svg>
              <svg v-else-if="item.type === 'income'" class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0-2v10h2V2h-2zM12 20v-8h-2v8h2zM7 10h10V8H7v2z"></path>
              </svg>
              <svg v-else-if="item.type === 'pending'" class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-6h2v6zm0-8h-2V7h2v2z"></path>
              </svg>
            </div>
            <div>
              <span class="font-medium text-gray-800 block">{{ item.name }}</span>
              <span class="text-gray-500 text-xs">{{ item.date }}, at {{ item.time }}</span>
            </div>
          </div>
          <span :class="{
            'text-red-500': item.type === 'expense',
            'text-green-500': item.type === 'income',
            'text-blue-500': item.type === 'pending' // For pending text
          }" class="font-medium">
            {{ item.amount }}
          </span>
        </li>
      </ul>
    </div>
  </div>
</template>

<script lang="ts" setup>
interface TransactionItem {
  name: string;
  date: string;
  time: string;
  amount: string;
  type: 'expense' | 'income' | 'pending';
}

interface TransactionGroup {
  group: string;
  items: TransactionItem[];
}

interface Props {
  transactions: TransactionGroup[];
  calendarIconSvgPath?: string;
  dateRangeText?: string;
}

const props = withDefaults(defineProps<Props>(), {
  transactions: () => [],
  calendarIconSvgPath: 'M17 12h-2v2h2v-2zm-6 0H9v2h2v-2zm6-4h-2v2h2V8zm-6 0H9v2h2V8zm-4 4H5v2h2v-2zm6 0h-2v2h2v-2zm-6-4H5v2h2V8zm6-4h-2v2h2V4zM19 2h-1V1h-2v1H8V1H6v1H5c-1.11 0-1.99.9-1.99 2L3 20c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm0 18H5V9h14v11z',
  dateRangeText: '23 - 30 March 2020'
})
</script>

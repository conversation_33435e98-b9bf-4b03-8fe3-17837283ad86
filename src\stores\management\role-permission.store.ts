import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import { rolePermissionService } from '@/services';
import type {
  RolePermissionDto,
  CreateRolePermissionRequest,
  UpdateRolePermissionRequest,
  RolePermissionFilterDto,
  PermissionAction,
  RecordStatus
} from '@/interfaces';

export const useRolePermissionStore = defineStore('rolePermission', () => {
  // State
  const rolePermissions = ref<RolePermissionDto[]>([]);
  const currentRolePermission = ref<RolePermissionDto | null>(null);
  const isLoading = ref(false);
  const error = ref<string | null>(null);
  const searchQuery = ref('');
  const filters = ref<RolePermissionFilterDto>({
    searchQuery: '',
    roleId: 'All',
    sectionId: 'All',
    action: 'All',
    canAccess: 'All',
    status: 'All'
  });
  const pagination = ref({
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 0
  });

  // Getters
  const filteredRolePermissions = computed(() => {
    let filtered = rolePermissions.value;

    if (searchQuery.value) {
      const query = searchQuery.value.toLowerCase();
      filtered = filtered.filter(rp => 
        rp.action?.toLowerCase().includes(query) ||
        rp.roleId?.toLowerCase().includes(query) ||
        rp.sectionID?.toLowerCase().includes(query)
      );
    }

    if (filters.value.roleId !== 'All') {
      filtered = filtered.filter(rp => rp.roleId === filters.value.roleId);
    }

    if (filters.value.sectionId !== 'All') {
      filtered = filtered.filter(rp => rp.sectionID === filters.value.sectionId);
    }

    if (filters.value.action !== 'All') {
      filtered = filtered.filter(rp => rp.action === filters.value.action);
    }

    if (filters.value.canAccess !== 'All') {
      filtered = filtered.filter(rp => rp.canAccess === filters.value.canAccess);
    }

    if (filters.value.status !== 'All') {
      filtered = filtered.filter(rp => rp.status === filters.value.status);
    }

    return filtered;
  });

  const rolePermissionsByRole = computed(() => {
    const grouped: Record<string, RolePermissionDto[]> = {};
    rolePermissions.value.forEach(rp => {
      if (rp.roleId) {
        if (!grouped[rp.roleId]) {
          grouped[rp.roleId] = [];
        }
        grouped[rp.roleId].push(rp);
      }
    });
    return grouped;
  });

  const rolePermissionsBySection = computed(() => {
    const grouped: Record<string, RolePermissionDto[]> = {};
    rolePermissions.value.forEach(rp => {
      if (rp.sectionID) {
        if (!grouped[rp.sectionID]) {
          grouped[rp.sectionID] = [];
        }
        grouped[rp.sectionID].push(rp);
      }
    });
    return grouped;
  });

  const rolePermissionsByStatus = computed(() => {
    return {
      approved: rolePermissions.value.filter(rp => rp.status === 'Approved').length,
      secondApproved: rolePermissions.value.filter(rp => rp.status === 'SecondApproved').length,
      unapproved: rolePermissions.value.filter(rp => rp.status === 'Unapproved').length,
      rejected: rolePermissions.value.filter(rp => rp.status === 'Rejected').length,
      canAccess: rolePermissions.value.filter(rp => rp.canAccess).length,
      cannotAccess: rolePermissions.value.filter(rp => !rp.canAccess).length,
    };
  });

  const totalRolePermissions = computed(() => rolePermissions.value.length);
  const activeRolePermissions = computed(() => rolePermissions.value.filter(rp => rp.canAccess).length);

  // Actions
  const fetchRolePermissions = async (): Promise<void> => {
    try {
      isLoading.value = true;
      error.value = null;
      rolePermissions.value = await rolePermissionService.getRolePermissions();
    } catch (err: any) {
      error.value = err.message || 'Failed to fetch role permissions';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const fetchRolePermissionsPaginated = async (page: number = 1, limit: number = 10): Promise<void> => {
    try {
      isLoading.value = true;
      error.value = null;

      // Since there's no paginated endpoint, get all and paginate locally
      const allPermissions = await rolePermissionService.getRolePermissions();
      const startIndex = (page - 1) * limit;
      const endIndex = startIndex + limit;

      rolePermissions.value = allPermissions.slice(startIndex, endIndex);
      pagination.value = {
        page,
        limit,
        total: allPermissions.length,
        totalPages: Math.ceil(allPermissions.length / limit)
      };
    } catch (err: any) {
      error.value = err.message || 'Failed to fetch role permissions';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const fetchRolePermissionById = async (id: string): Promise<RolePermissionDto> => {
    try {
      isLoading.value = true;
      error.value = null;
      const rolePermission = await rolePermissionService.getRolePermissionById(id);
      currentRolePermission.value = rolePermission;
      return rolePermission;
    } catch (err: any) {
      error.value = err.message || 'Failed to fetch role permission';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const createRolePermission = async (rolePermissionData: CreateRolePermissionRequest): Promise<RolePermissionDto> => {
    try {
      isLoading.value = true;
      error.value = null;
      const newRolePermission = await rolePermissionService.createRolePermission(rolePermissionData);
      rolePermissions.value.push(newRolePermission);
      return newRolePermission;
    } catch (err: any) {
      error.value = err.message || 'Failed to create role permission';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const updateRolePermission = async (id: string, rolePermissionData: UpdateRolePermissionRequest): Promise<RolePermissionDto> => {
    try {
      isLoading.value = true;
      error.value = null;
      const updatedRolePermission = await rolePermissionService.updateRolePermission(id, rolePermissionData);
      
      const index = rolePermissions.value.findIndex(rp => rp.id === id);
      if (index !== -1) {
        rolePermissions.value[index] = updatedRolePermission;
      }
      
      if (currentRolePermission.value?.id === id) {
        currentRolePermission.value = updatedRolePermission;
      }
      
      return updatedRolePermission;
    } catch (err: any) {
      error.value = err.message || 'Failed to update role permission';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const deleteRolePermission = async (id: string): Promise<void> => {
    try {
      isLoading.value = true;
      error.value = null;
      await rolePermissionService.deleteRolePermission(id);
      
      rolePermissions.value = rolePermissions.value.filter(rp => rp.id !== id);
      
      if (currentRolePermission.value?.id === id) {
        currentRolePermission.value = null;
      }
    } catch (err: any) {
      error.value = err.message || 'Failed to delete role permission';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const fetchRolePermissionsByRoleId = async (roleId: string): Promise<RolePermissionDto[]> => {
    try {
      return await rolePermissionService.getRolePermissionsByRoleId(roleId);
    } catch (err: any) {
      error.value = err.message || 'Failed to fetch role permissions by role';
      throw err;
    }
  };

  const fetchRolePermissionsBySectionId = async (sectionId: string): Promise<RolePermissionDto[]> => {
    try {
      return await rolePermissionService.getRolePermissionsBySectionId(sectionId);
    } catch (err: any) {
      error.value = err.message || 'Failed to fetch role permissions by section';
      throw err;
    }
  };

  const bulkCreateRolePermissions = async (permissions: CreateRolePermissionRequest[]): Promise<RolePermissionDto[]> => {
    try {
      isLoading.value = true;
      error.value = null;

      // Create permissions individually since there's no bulk endpoint
      const newRolePermissions: RolePermissionDto[] = [];
      for (const permission of permissions) {
        const created = await rolePermissionService.createRolePermission(permission);
        newRolePermissions.push(created);
      }

      rolePermissions.value.push(...newRolePermissions);
      return newRolePermissions;
    } catch (err: any) {
      error.value = err.message || 'Failed to bulk create role permissions';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const bulkAssignPermissionsToRole = async (roleId: string, permissions: CreateRolePermissionRequest[]): Promise<RolePermissionDto[]> => {
    try {
      isLoading.value = true;
      error.value = null;
      const newRolePermissions = await rolePermissionService.bulkAssignPermissionsToRole(roleId, permissions);

      // Update store with new permissions
      rolePermissions.value.push(...newRolePermissions);

      return newRolePermissions;
    } catch (err: any) {
      error.value = err.message || 'Failed to bulk assign permissions to role';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const bulkRemovePermissionsFromRole = async (roleId: string, permissionIds?: string[], sectionIds?: string[]): Promise<void> => {
    try {
      isLoading.value = true;
      error.value = null;
      await rolePermissionService.bulkRemovePermissionsFromRole(roleId, permissionIds, sectionIds);

      // Remove permissions from store
      if (permissionIds && permissionIds.length > 0) {
        rolePermissions.value = rolePermissions.value.filter(rp => !permissionIds.includes(rp.id || ''));
      } else if (sectionIds && sectionIds.length > 0) {
        rolePermissions.value = rolePermissions.value.filter(rp =>
          !(rp.roleId === roleId && sectionIds.includes(rp.sectionID))
        );
      } else {
        // Remove all permissions for the role
        rolePermissions.value = rolePermissions.value.filter(rp => rp.roleId !== roleId);
      }
    } catch (err: any) {
      error.value = err.message || 'Failed to bulk remove permissions from role';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const replaceRolePermissions = async (roleId: string, newPermissions: CreateRolePermissionRequest[]): Promise<RolePermissionDto[]> => {
    try {
      isLoading.value = true;
      error.value = null;
      const updatedPermissions = await rolePermissionService.replaceRolePermissions(roleId, newPermissions);

      // Remove old permissions and add new ones
      rolePermissions.value = rolePermissions.value.filter(rp => rp.roleId !== roleId);
      rolePermissions.value.push(...updatedPermissions);

      return updatedPermissions;
    } catch (err: any) {
      error.value = err.message || 'Failed to replace role permissions';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const bulkUpdateRolePermissions = async (updates: Array<{ id: string; data: UpdateRolePermissionRequest }>): Promise<RolePermissionDto[]> => {
    try {
      isLoading.value = true;
      error.value = null;

      // Update permissions individually since there's no bulk endpoint
      const updatedPermissions: RolePermissionDto[] = [];
      for (const update of updates) {
        const updated = await rolePermissionService.updateRolePermission(update.id, update.data);
        updatedPermissions.push(updated);
      }

      // Update permissions in store
      updatedPermissions.forEach(updated => {
        const index = rolePermissions.value.findIndex(rp => rp.id === updated.id);
        if (index !== -1) {
          rolePermissions.value[index] = updated;
        }
      });

      return updatedPermissions;
    } catch (err: any) {
      error.value = err.message || 'Failed to bulk update role permissions';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const checkRolePermission = async (roleId: string, sectionId: string, action: string): Promise<boolean> => {
    try {
      return await rolePermissionService.checkRolePermission(roleId, sectionId, action);
    } catch (err: any) {
      error.value = err.message || 'Failed to check role permission';
      throw err;
    }
  };

  const searchRolePermissions = async (query: string): Promise<void> => {
    searchQuery.value = query;
    if (query) {
      try {
        isLoading.value = true;
        error.value = null;

        // Get all permissions and filter locally
        const allPermissions = await rolePermissionService.getRolePermissions();
        const searchTerm = query.toLowerCase();
        rolePermissions.value = allPermissions.filter(rp =>
          rp.action?.toLowerCase().includes(searchTerm) ||
          rp.roleId?.toLowerCase().includes(searchTerm) ||
          rp.sectionID?.toLowerCase().includes(searchTerm)
        );
      } catch (err: any) {
        error.value = err.message || 'Failed to search role permissions';
        throw err;
      } finally {
        isLoading.value = false;
      }
    } else {
      await fetchRolePermissions();
    }
  };

  const filterRolePermissions = async (filterOptions: RolePermissionFilterDto): Promise<void> => {
    filters.value = { ...filterOptions };
    try {
      isLoading.value = true;
      error.value = null;

      // Get all permissions and filter locally
      let allPermissions = await rolePermissionService.getRolePermissions();

      // Apply filters
      if (filterOptions.roleId && filterOptions.roleId !== 'All') {
        allPermissions = allPermissions.filter(rp => rp.roleId === filterOptions.roleId);
      }

      if (filterOptions.sectionId && filterOptions.sectionId !== 'All') {
        allPermissions = allPermissions.filter(rp => rp.sectionID === filterOptions.sectionId);
      }

      if (filterOptions.action && filterOptions.action !== 'All') {
        allPermissions = allPermissions.filter(rp => rp.action === filterOptions.action);
      }

      if (filterOptions.canAccess && filterOptions.canAccess !== 'All') {
        const canAccessBool = filterOptions.canAccess === 'true';
        allPermissions = allPermissions.filter(rp => rp.canAccess === canAccessBool);
      }

      if (filterOptions.status && filterOptions.status !== 'All') {
        allPermissions = allPermissions.filter(rp => rp.status === filterOptions.status);
      }

      rolePermissions.value = allPermissions;
    } catch (err: any) {
      error.value = err.message || 'Failed to filter role permissions';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const clearError = (): void => {
    error.value = null;
  };

  const clearCurrentRolePermission = (): void => {
    currentRolePermission.value = null;
  };

  const resetFilters = (): void => {
    filters.value = {
      searchQuery: '',
      roleId: 'All',
      sectionId: 'All',
      action: 'All',
      canAccess: 'All',
      status: 'All'
    };
    searchQuery.value = '';
  };

  return {
    // State
    rolePermissions,
    currentRolePermission,
    isLoading,
    error,
    searchQuery,
    filters,
    pagination,
    
    // Getters
    filteredRolePermissions,
    rolePermissionsByRole,
    rolePermissionsBySection,
    rolePermissionsByStatus,
    totalRolePermissions,
    activeRolePermissions,
    
    // Actions
    fetchRolePermissions,
    fetchRolePermissionsPaginated,
    fetchRolePermissionById,
    createRolePermission,
    updateRolePermission,
    deleteRolePermission,
    fetchRolePermissionsByRoleId,
    fetchRolePermissionsBySectionId,
    bulkCreateRolePermissions,
    bulkAssignPermissionsToRole,
    bulkRemovePermissionsFromRole,
    replaceRolePermissions,
    bulkUpdateRolePermissions,
    checkRolePermission,
    searchRolePermissions,
    filterRolePermissions,
    clearError,
    clearCurrentRolePermission,
    resetFilters,
  };
});

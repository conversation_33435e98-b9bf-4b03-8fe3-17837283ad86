import { useFormValidation, ValidationRules, type FormValidationConfig } from './useFormValidation';

/**
 * User form data interface
 */
export interface UserFormData {
  firstName: string;
  lastName: string;
  email: string;
  userName: string;
  password?: string;
  confirmPassword?: string;
  gender?: string;
  dateOfBirth?: Date | string;
  idType?: string;
  idNumber?: string;
  roleId?: string;
}

/**
 * User form validation configuration for create mode
 */
const createUserValidationConfig: FormValidationConfig = {
  fields: [
    {
      field: 'firstName',
      rules: [
        ValidationRules.required('First name is required'),
        ValidationRules.minLength(2, 'First name must be at least 2 characters'),
        ValidationRules.maxLength(50, 'First name must be no more than 50 characters')
      ]
    },
    {
      field: 'lastName',
      rules: [
        ValidationRules.required('Last name is required'),
        ValidationRules.minLength(2, 'Last name must be at least 2 characters'),
        ValidationRules.maxLength(50, 'Last name must be no more than 50 characters')
      ]
    },
    {
      field: 'email',
      rules: [
        ValidationRules.required('Email is required'),
        ValidationRules.email('Please enter a valid email address'),
        ValidationRules.maxLength(100, 'Email must be no more than 100 characters')
      ]
    },
    {
      field: 'userName',
      rules: [
        ValidationRules.required('Username is required'),
        ValidationRules.minLength(3, 'Username must be at least 3 characters'),
        ValidationRules.maxLength(30, 'Username must be no more than 30 characters'),
        ValidationRules.custom(
          (value: string) => /^[a-zA-Z0-9_.-]+$/.test(value),
          'Username can only contain letters, numbers, dots, hyphens, and underscores'
        )
      ]
    },
    {
      field: 'password',
      rules: [
        ValidationRules.required('Password is required'),
        ValidationRules.minLength(6, 'Password must be at least 6 characters'),
        ValidationRules.maxLength(100, 'Password must be no more than 100 characters'),
        ValidationRules.custom(
          (value: string) => /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(value),
          'Password must contain at least one uppercase letter, one lowercase letter, and one number'
        )
      ]
    },
    {
      field: 'confirmPassword',
      rules: [
        ValidationRules.required('Please confirm your password'),
        ValidationRules.confirmPassword('password', 'Passwords do not match')
      ]
    }
  ],
  validateOnSubmit: true,
  stopOnFirstError: false
};

/**
 * User form validation configuration for edit mode (no password required)
 */
const editUserValidationConfig: FormValidationConfig = {
  fields: [
    {
      field: 'firstName',
      rules: [
        ValidationRules.required('First name is required'),
        ValidationRules.minLength(2, 'First name must be at least 2 characters'),
        ValidationRules.maxLength(50, 'First name must be no more than 50 characters')
      ]
    },
    {
      field: 'lastName',
      rules: [
        ValidationRules.required('Last name is required'),
        ValidationRules.minLength(2, 'Last name must be at least 2 characters'),
        ValidationRules.maxLength(50, 'Last name must be no more than 50 characters')
      ]
    },
    {
      field: 'email',
      rules: [
        ValidationRules.required('Email is required'),
        ValidationRules.email('Please enter a valid email address'),
        ValidationRules.maxLength(100, 'Email must be no more than 100 characters')
      ]
    },
    {
      field: 'userName',
      rules: [
        ValidationRules.required('Username is required'),
        ValidationRules.minLength(3, 'Username must be at least 3 characters'),
        ValidationRules.maxLength(30, 'Username must be no more than 30 characters'),
        ValidationRules.custom(
          (value: string) => /^[a-zA-Z0-9_.-]+$/.test(value),
          'Username can only contain letters, numbers, dots, hyphens, and underscores'
        )
      ]
    }
  ],
  validateOnSubmit: true,
  stopOnFirstError: false
};

/**
 * User form validation composable
 */
export function useUserFormValidation(mode: 'create' | 'edit' = 'create') {
  const config = mode === 'create' ? createUserValidationConfig : editUserValidationConfig;
  const validation = useFormValidation(config);

  /**
   * Validate user form data
   */
  const validateUserForm = (formData: UserFormData) => {
    return validation.validateForm(formData);
  };

  /**
   * Validate specific user field
   */
  const validateUserField = (field: keyof UserFormData, value: any, formData: UserFormData) => {
    return validation.validateField(field, value, formData);
  };

  /**
   * Additional user-specific validation methods
   */
  const validateEmailUniqueness = async (email: string, currentUserId?: string): Promise<boolean> => {
    // This would typically make an API call to check email uniqueness
    // For now, return true (valid) - implement API call as needed
    console.log('Email uniqueness validation would be implemented here', { email, currentUserId });
    return true;
  };

  const validateUsernameUniqueness = async (username: string, currentUserId?: string): Promise<boolean> => {
    // This would typically make an API call to check username uniqueness
    // For now, return true (valid) - implement API call as needed
    console.log('Username uniqueness validation would be implemented here', { username, currentUserId });
    return true;
  };

  /**
   * Validate password strength (additional to basic rules)
   */
  const validatePasswordStrength = (password: string): { score: number; feedback: string[] } => {
    let score = 0;
    const feedback: string[] = [];

    if (password.length >= 8) {
      score += 1;
    } else {
      feedback.push('Use at least 8 characters');
    }

    if (/[a-z]/.test(password)) {
      score += 1;
    } else {
      feedback.push('Include lowercase letters');
    }

    if (/[A-Z]/.test(password)) {
      score += 1;
    } else {
      feedback.push('Include uppercase letters');
    }

    if (/\d/.test(password)) {
      score += 1;
    } else {
      feedback.push('Include numbers');
    }

    if (/[^a-zA-Z0-9]/.test(password)) {
      score += 1;
      feedback.push('Great! Special characters make passwords stronger');
    } else {
      feedback.push('Consider adding special characters');
    }

    return { score, feedback };
  };

  /**
   * Get user-friendly error messages
   */
  const getUserFriendlyError = (field: keyof UserFormData): string | undefined => {
    const error = validation.getFieldError(field);
    if (!error) return undefined;

    // Map technical errors to user-friendly messages if needed
    const friendlyMessages: Record<string, string> = {
      'First name is required': 'Please enter your first name',
      'Last name is required': 'Please enter your last name',
      'Email is required': 'Please enter your email address',
      'Username is required': 'Please choose a username',
      'Password is required': 'Please create a password'
    };

    return friendlyMessages[error] || error;
  };

  return {
    // Base validation methods
    ...validation,
    
    // User-specific methods
    validateUserForm,
    validateUserField,
    validateEmailUniqueness,
    validateUsernameUniqueness,
    validatePasswordStrength,
    getUserFriendlyError,
    
    // Configuration
    mode,
    config
  };
}

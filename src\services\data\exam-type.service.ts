/**
 * @fileoverview Exam Type Service for MANEB Results Management System
 * @description Service for fetching exam types from the API
 */

import apiClient from '../core/api-client';

/**
 * Exam Type interface matching the API response
 */
export interface ExamTypeDto {
  id: string;
  name: string;
  isActive: boolean;
  numericId: number;
  configurationJson: string | null;
  createdOn: string;
  createdBy: string;
  modifiedOn: string | null;
  modifiedBy: string | null;
}

/**
 * API Response interface for paginated exam types
 */
export interface ExamTypeResponse {
  totalCount: number;
  pageNumber: number;
  pageSize: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
  items: ExamTypeDto[];
}

/**
 * Simplified exam type option for dropdowns
 */
export interface ExamTypeOption {
  value: string;
  label: string;
  description: string;
  isActive: boolean;
}

/**
 * Exam Type Service
 * @description Handles all exam type related API operations
 */
export class ExamTypeService {
  private static instance: ExamTypeService;
  private cachedExamTypes: ExamTypeDto[] | null = null;
  private lastFetchTime: number = 0;
  private readonly CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

  private constructor() {}

  /**
   * Get singleton instance
   */
  static getInstance(): ExamTypeService {
    if (!ExamTypeService.instance) {
      ExamTypeService.instance = new ExamTypeService();
    }
    return ExamTypeService.instance;
  }

  /**
   * Fetch exam types from API
   */
  async fetchExamTypes(forceRefresh: boolean = false): Promise<ExamTypeDto[]> {
    const now = Date.now();

    // Return cached data if available and not expired
    if (!forceRefresh && this.cachedExamTypes && (now - this.lastFetchTime) < this.CACHE_DURATION) {
      return this.cachedExamTypes;
    }

    try {
      const response: ExamTypeResponse = await apiClient.get('/api/ExamType');
      this.cachedExamTypes = response.items;
      this.lastFetchTime = now;
      return this.cachedExamTypes;
    } catch (error: any) {
      console.error('❌ Error fetching exam types:', error);

      // Return cached data if available, even if expired
      if (this.cachedExamTypes) {
        console.warn('⚠️ Using cached exam types due to API error');
        return this.cachedExamTypes;
      }

      throw error;
    }
  }

  /**
   * Get active exam types only
   */
  async getActiveExamTypes(): Promise<ExamTypeDto[]> {
    const examTypes = await this.fetchExamTypes();
    return examTypes.filter(type => type.isActive);
  }

  /**
   * Get exam types as dropdown options
   */
  async getExamTypeOptions(activeOnly: boolean = true): Promise<ExamTypeOption[]> {
    const examTypes = activeOnly ? await this.getActiveExamTypes() : await this.fetchExamTypes();
    
    return examTypes.map(type => ({
      value: type.id,
      label: type.id,
      description: type.name,
      isActive: type.isActive
    }));
  }

  /**
   * Get exam type by ID
   */
  async getExamTypeById(id: string): Promise<ExamTypeDto | undefined> {
    const examTypes = await this.fetchExamTypes();
    return examTypes.find(type => type.id === id);
  }

  /**
   * Get exam type description by ID
   */
  async getExamTypeDescription(id: string): Promise<string> {
    const examType = await this.getExamTypeById(id);
    return examType?.name || id;
  }

  /**
   * Check if exam type ID is valid
   */
  async isValidExamType(id: string): Promise<boolean> {
    const examType = await this.getExamTypeById(id);
    return !!examType && examType.isActive;
  }

  /**
   * Clear cache (useful for testing or forced refresh)
   */
  clearCache(): void {
    this.cachedExamTypes = null;
    this.lastFetchTime = 0;
  }

  /**
   * Get exam types formatted for filter options
   */
  async getFilterOptions(parentId?: string): Promise<Array<{id: string, name: string, parentId?: string}>> {
    const examTypes = await this.getActiveExamTypes();
    
    return examTypes.map(type => ({
      id: type.id,
      name: `${type.id} - ${type.name}`,
      parentId
    }));
  }
}

// Export singleton instance
export const examTypeService = ExamTypeService.getInstance();

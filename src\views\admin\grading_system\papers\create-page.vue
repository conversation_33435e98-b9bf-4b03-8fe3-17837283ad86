<template>
  <div class="mx-auto max-w-4xl px-4 sm:px-6 lg:px-8 py-6">
    <!-- Enhanced <PERSON>er with MANEB Branding -->
    <div class="mb-8">
      <nav class="flex mb-5" aria-label="Breadcrumb">
        <ol class="inline-flex items-center space-x-1 text-sm font-medium md:space-x-2">
          <li class="inline-flex items-center">
            <router-link
              to="/admin/dashboard"
              class="inline-flex items-center text-gray-700 hover:text-maneb-primary dark:text-gray-300 dark:hover:text-white transition-colors duration-200"
            >
              <svg class="w-5 h-5 mr-2.5" fill="currentColor" viewBox="0 0 20 20">
                <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"/>
              </svg>
              Admin Dashboard
            </router-link>
          </li>
          <li>
            <div class="flex items-center">
              <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
              </svg>
              <router-link
                to="/admin/grading-system"
                class="ml-1 text-gray-700 hover:text-maneb-primary md:ml-2 dark:text-gray-300 dark:hover:text-white transition-colors duration-200"
              >
                Grading System
              </router-link>
            </div>
          </li>
          <li>
            <div class="flex items-center">
              <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
              </svg>
              <span class="ml-1 text-gray-400 md:ml-2 dark:text-gray-500" aria-current="page">Create Paper</span>
            </div>
          </li>
        </ol>
      </nav>

      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-3xl font-bold text-gray-900 dark:text-white">Create Paper</h1>
          <p class="mt-2 text-sm text-gray-600 dark:text-gray-400">Add a new paper to the grading system</p>
        </div>

        <!-- MANEB Logo/Branding -->
        <div class="hidden md:flex items-center space-x-3">
          <div class="flex items-center space-x-2 px-3 py-2 bg-red-50 rounded-lg border border-red-100">
            <div class="w-8 h-8 bg-maneb-primary rounded-full flex items-center justify-center">
              <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                <path d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
              </svg>
            </div>
            <span class="text-sm font-medium text-maneb-primary">MANEB EMS</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Enhanced Create Paper Form -->
    <div class="bg-white shadow-sm rounded-lg border border-gray-200 dark:bg-gray-800 dark:border-gray-700">
      <div class="px-6 py-5 border-b border-gray-200 dark:border-gray-700">
        <div class="flex items-center space-x-3">
          <div class="w-10 h-10 bg-red-50 rounded-lg flex items-center justify-center">
            <svg class="w-6 h-6 text-maneb-primary" fill="currentColor" viewBox="0 0 20 20">
              <path d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
            </svg>
          </div>
          <div>
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Paper Information</h3>
            <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">Please fill in all required fields to create a new paper.</p>
          </div>
        </div>
      </div>

      <form @submit.prevent="handleSubmit" class="px-6 py-6 space-y-8">
        <!-- Basic Information Section -->
        <div class="space-y-6">
          <div class="border-l-4 border-maneb-primary pl-4">
            <h4 class="text-lg font-semibold text-gray-900 dark:text-white">Basic Information</h4>
            <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">Configure the paper details and subject association</p>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Subject -->
            <div class="space-y-2">
              <label for="subjectId" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Subject <span class="text-red-500">*</span>
              </label>
              <select
                id="subjectId"
                v-model="formData.subjectId"
                required
                :class="[
                  'block w-full rounded-lg border px-3 py-2.5 text-sm transition-colors duration-200',
                  'focus:ring-2 focus:ring-maneb-primary focus:border-maneb-primary',
                  'dark:bg-gray-700 dark:border-gray-600 dark:text-white dark:placeholder-gray-400',
                  errors.subjectId
                    ? 'border-red-300 bg-red-50 text-red-900 focus:border-red-500 focus:ring-red-500'
                    : 'border-gray-300 bg-white text-gray-900'
                ]"
              >
                <option value="">Select subject</option>
                <option v-for="subject in subjects" :key="subject.id" :value="subject.id">
                  {{ subject.name }} ({{ subject.code }})
                </option>
              </select>
              <p v-if="errors.subjectId" class="text-sm text-red-600 dark:text-red-400">{{ errors.subjectId }}</p>
            </div>

            <!-- Paper Name -->
            <div class="space-y-2">
              <label for="name" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Paper Name <span class="text-red-500">*</span>
              </label>
              <input
                id="name"
                v-model="formData.name"
                type="text"
                required
                :class="[
                  'block w-full rounded-lg border px-3 py-2.5 text-sm transition-colors duration-200',
                  'focus:ring-2 focus:ring-maneb-primary focus:border-maneb-primary',
                  'dark:bg-gray-700 dark:border-gray-600 dark:text-white dark:placeholder-gray-400',
                  errors.name
                    ? 'border-red-300 bg-red-50 text-red-900 focus:border-red-500 focus:ring-red-500'
                    : 'border-gray-300 bg-white text-gray-900'
                ]"
                placeholder="Enter paper name"
              />
              <p v-if="errors.name" class="text-sm text-red-600 dark:text-red-400">{{ errors.name }}</p>
            </div>

            <!-- Paper Code -->
            <div class="space-y-2">
              <label for="code" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Paper Code <span class="text-red-500">*</span>
              </label>
              <input
                id="code"
                v-model="formData.code"
                type="text"
                required
                :class="[
                  'block w-full rounded-lg border px-3 py-2.5 text-sm transition-colors duration-200',
                  'focus:ring-2 focus:ring-maneb-primary focus:border-maneb-primary',
                  'dark:bg-gray-700 dark:border-gray-600 dark:text-white dark:placeholder-gray-400',
                  errors.code
                    ? 'border-red-300 bg-red-50 text-red-900 focus:border-red-500 focus:ring-red-500'
                    : 'border-gray-300 bg-white text-gray-900'
                ]"
                placeholder="Enter paper code"
              />
              <p v-if="errors.code" class="text-sm text-red-600 dark:text-red-400">{{ errors.code }}</p>
            </div>

            <!-- Paper Number -->
            <div class="space-y-2">
              <label for="paperNumber" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Paper Number <span class="text-red-500">*</span>
              </label>
              <input
                id="paperNumber"
                v-model.number="formData.paperNumber"
                type="number"
                required
                min="1"
                max="10"
                :class="[
                  'block w-full rounded-lg border px-3 py-2.5 text-sm transition-colors duration-200',
                  'focus:ring-2 focus:ring-maneb-primary focus:border-maneb-primary',
                  'dark:bg-gray-700 dark:border-gray-600 dark:text-white dark:placeholder-gray-400',
                  errors.paperNumber
                    ? 'border-red-300 bg-red-50 text-red-900 focus:border-red-500 focus:ring-red-500'
                    : 'border-gray-300 bg-white text-gray-900'
                ]"
                placeholder="Enter paper number"
              />
              <p v-if="errors.paperNumber" class="text-sm text-red-600 dark:text-red-400">{{ errors.paperNumber }}</p>
            </div>
          </div>
        </div>

        <!-- Exam Details Section -->
        <div class="space-y-6">
          <div class="border-l-4 border-blue-500 pl-4">
            <h4 class="text-lg font-semibold text-gray-900 dark:text-white">Exam Details</h4>
            <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">Configure duration and maximum marks</p>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Duration -->
            <div class="space-y-2">
              <label for="duration" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Duration (minutes) <span class="text-red-500">*</span>
              </label>
              <input
                id="duration"
                v-model.number="formData.duration"
                type="number"
                required
                min="30"
                max="480"
                :class="[
                  'block w-full rounded-lg border px-3 py-2.5 text-sm transition-colors duration-200',
                  'focus:ring-2 focus:ring-maneb-primary focus:border-maneb-primary',
                  'dark:bg-gray-700 dark:border-gray-600 dark:text-white dark:placeholder-gray-400',
                  errors.duration
                    ? 'border-red-300 bg-red-50 text-red-900 focus:border-red-500 focus:ring-red-500'
                    : 'border-gray-300 bg-white text-gray-900'
                ]"
                placeholder="Enter duration in minutes"
              />
              <p v-if="errors.duration" class="text-sm text-red-600 dark:text-red-400">{{ errors.duration }}</p>
              <p class="text-xs text-gray-500 dark:text-gray-400">Duration: {{ formatDuration(formData.duration) }}</p>
            </div>

            <!-- Maximum Marks -->
            <div class="space-y-2">
              <label for="maxMarks" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Maximum Marks <span class="text-red-500">*</span>
              </label>
              <input
                id="maxMarks"
                v-model.number="formData.maxMarks"
                type="number"
                required
                min="1"
                max="1000"
                :class="[
                  'block w-full rounded-lg border px-3 py-2.5 text-sm transition-colors duration-200',
                  'focus:ring-2 focus:ring-maneb-primary focus:border-maneb-primary',
                  'dark:bg-gray-700 dark:border-gray-600 dark:text-white dark:placeholder-gray-400',
                  errors.maxMarks
                    ? 'border-red-300 bg-red-50 text-red-900 focus:border-red-500 focus:ring-red-500'
                    : 'border-gray-300 bg-white text-gray-900'
                ]"
                placeholder="Enter maximum marks"
              />
              <p v-if="errors.maxMarks" class="text-sm text-red-600 dark:text-red-400">{{ errors.maxMarks }}</p>
            </div>
          </div>
        </div>

        <!-- Additional Information Section -->
        <div class="space-y-6">
          <div class="border-l-4 border-green-500 pl-4">
            <h4 class="text-lg font-semibold text-gray-900 dark:text-white">Additional Information</h4>
            <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">Optional description and status settings</p>
          </div>

          <div class="space-y-6">
            <!-- Description -->
            <div class="space-y-2">
              <label for="description" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Description</label>
              <textarea
                id="description"
                v-model="formData.description"
                rows="3"
                class="block w-full rounded-lg border border-gray-300 bg-white px-3 py-2.5 text-sm text-gray-900 placeholder-gray-500 focus:border-maneb-primary focus:ring-2 focus:ring-maneb-primary dark:bg-gray-700 dark:border-gray-600 dark:text-white dark:placeholder-gray-400"
                placeholder="Enter description (optional)"
              />
            </div>

            <!-- Active Status -->
            <div class="flex items-center">
              <input
                id="isActive"
                v-model="formData.isActive"
                type="checkbox"
                class="h-4 w-4 text-maneb-primary focus:ring-maneb-primary border-gray-300 rounded"
              />
              <label for="isActive" class="ml-2 block text-sm text-gray-900 dark:text-gray-300">
                Active (paper is currently in use)
              </label>
            </div>
          </div>
        </div>

        <!-- Enhanced Error Display -->
        <div v-if="submitError" class="bg-red-50 border border-red-200 rounded-lg p-4 dark:bg-red-900/20 dark:border-red-800">
          <div class="flex">
            <div class="flex-shrink-0">
              <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
              </svg>
            </div>
            <div class="ml-3">
              <h3 class="text-sm font-medium text-red-800 dark:text-red-400">Error creating paper</h3>
              <div class="mt-2 text-sm text-red-700 dark:text-red-300">{{ submitError }}</div>
            </div>
          </div>
        </div>

        <!-- Enhanced Form Actions -->
        <div class="flex flex-col sm:flex-row justify-end space-y-3 sm:space-y-0 sm:space-x-3 pt-6 border-t border-gray-200 dark:border-gray-700">
          <router-link
            :to="{ name: 'admin.grading-system' }"
            class="inline-flex items-center justify-center px-6 py-3 border border-gray-300 text-sm font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-maneb-primary transition-colors duration-200 dark:bg-gray-700 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-600"
          >
            <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
            </svg>
            Cancel
          </router-link>
          <button
            type="submit"
            :disabled="isSubmitting"
            class="inline-flex items-center justify-center px-6 py-3 border border-transparent text-sm font-medium rounded-lg text-white bg-maneb-primary hover:bg-maneb-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-maneb-primary disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200 shadow-sm"
          >
            <svg v-if="isSubmitting" class="animate-spin -ml-1 mr-3 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            <svg v-else class="h-4 w-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
            </svg>
            {{ isSubmitting ? 'Creating Paper...' : 'Create Paper' }}
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useGradingStore } from '@/stores'
import type { CreatePaperRequest } from '@/interfaces'
import sweetAlert from '@/utils/ui/sweetAlert'

// Router
const router = useRouter()

// Stores
const gradingStore = useGradingStore()

// Reactive state
const formData = reactive<{
  subjectId: string
  name: string
  code: string
  description: string
  paperNumber: number | null
  duration: number | null
  maxMarks: number | null
  isActive: boolean
}>({
  subjectId: '',
  name: '',
  code: '',
  description: '',
  paperNumber: 1,
  duration: null,
  maxMarks: null,
  isActive: true
})

const errors = reactive<Record<string, string>>({})
const submitError = ref<string | null>(null)
const isSubmitting = ref(false)
const subjects = ref<any[]>([])

// Methods
const formatDuration = (minutes: number | null): string => {
  if (!minutes) return ''
  const hours = Math.floor(minutes / 60)
  const mins = minutes % 60
  if (hours > 0) {
    return mins > 0 ? `${hours}h ${mins}m` : `${hours}h`
  }
  return `${mins}m`
}

const validateForm = (): boolean => {
  // Clear previous errors
  Object.keys(errors).forEach(key => delete errors[key])

  let isValid = true

  // Required field validation
  if (!formData.subjectId.trim()) {
    errors.subjectId = 'Subject is required'
    isValid = false
  }

  if (!formData.name.trim()) {
    errors.name = 'Paper name is required'
    isValid = false
  }

  if (!formData.code.trim()) {
    errors.code = 'Paper code is required'
    isValid = false
  }

  if (formData.paperNumber === null || formData.paperNumber === undefined) {
    errors.paperNumber = 'Paper number is required'
    isValid = false
  } else if (formData.paperNumber < 1 || formData.paperNumber > 10) {
    errors.paperNumber = 'Paper number must be between 1 and 10'
    isValid = false
  }

  if (formData.duration === null || formData.duration === undefined) {
    errors.duration = 'Duration is required'
    isValid = false
  } else if (formData.duration < 30 || formData.duration > 480) {
    errors.duration = 'Duration must be between 30 and 480 minutes'
    isValid = false
  }

  if (formData.maxMarks === null || formData.maxMarks === undefined) {
    errors.maxMarks = 'Maximum marks is required'
    isValid = false
  } else if (formData.maxMarks < 1 || formData.maxMarks > 1000) {
    errors.maxMarks = 'Maximum marks must be between 1 and 1000'
    isValid = false
  }

  return isValid
}

const handleSubmit = async () => {
  if (!validateForm()) {
    return
  }

  try {
    isSubmitting.value = true
    submitError.value = null

    // Prepare data for submission
    const submitData: CreatePaperRequest = {
      subjectId: formData.subjectId,
      name: formData.name.trim(),
      code: formData.code.trim(),
      description: formData.description.trim() || undefined,
      paperNumber: formData.paperNumber!,
      duration: formData.duration!,
      maxMarks: formData.maxMarks!,
      isActive: formData.isActive
    }

    // Submit to grading store
    await gradingStore.createPaper(submitData)

    // Show success message
    await sweetAlert.success('Paper created successfully!')

    // Navigate back to grading system
    router.push({ name: 'admin.grading-system' })

  } catch (error: any) {
    submitError.value = error.message || 'Failed to create paper'
    // Error creating paper (console logging removed)
  } finally {
    isSubmitting.value = false
  }
}

const fetchSubjects = async () => {
  try {
    await gradingStore.fetchSubjects()
    subjects.value = gradingStore.subjects
  } catch (error) {
    // Error fetching subjects (console logging removed)
  }
}

// Lifecycle
onMounted(() => {
  fetchSubjects()
})
</script>

<style scoped>
/* MANEB Theme Colors */
.bg-maneb-primary {
  background-color: #a12c2c;
}

.text-maneb-primary {
  color: #a12c2c;
}

.bg-maneb-primary-dark {
  background-color: #8b2424;
}

.border-maneb-primary {
  border-color: #a12c2c;
}

.focus\:ring-maneb-primary:focus {
  --tw-ring-color: #a12c2c;
}

.focus\:border-maneb-primary:focus {
  border-color: #a12c2c;
}

.hover\:bg-maneb-primary-dark:hover {
  background-color: #8b2424;
}
</style>

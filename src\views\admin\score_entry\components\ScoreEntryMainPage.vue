<template>
  <div class="space-y-4">

    <div class="bg-white rounded-lg border border-gray-200 p-3">
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-2 items-end">

        <div v-if="!props.hasRouteParams">
          <label for="examType" class="block text-xs font-medium text-gray-700 mb-1">
            Exam Type <span class="text-red-500">*</span>
          </label>
          <select
            id="examType"
            v-model="filters.examType"
            @change="onExamTypeChange"
            :disabled="isLoading"
            class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-maneb-primary focus:border-maneb-primary block w-full p-2 disabled:bg-gray-100 disabled:cursor-not-allowed"
          >
            <option value="">
              {{ isLoading ? 'Loading...' : 'Select Exam Type' }}
            </option>
            <option v-for="examType in examTypes" :key="examType.id" :value="examType.id">
              {{ examType.name }}
            </option>
          </select>
        </div>

        <div v-if="isPrimarySchoolExamType">
          <label for="district" class="block text-xs font-medium text-gray-700 mb-1">
            District <span class="text-red-500">*</span>
          </label>

          <div class="relative">
            <input
              type="text"
              v-model="districtSearchQuery"
              @focus="showDistrictDropdown = true"
              @blur="handleDistrictBlur"
              @input="filterDistricts"
              @keydown="handleDistrictKeydown"
              :placeholder="isLoadingDistricts ? 'Loading districts...' : 'Search districts...'"
              :disabled="isLoadingDistricts"
              class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-maneb-primary focus:border-maneb-primary block w-full p-2 disabled:bg-gray-100 disabled:cursor-not-allowed"
            />

            <div
              v-if="showDistrictDropdown && filteredDistricts.length > 0"
              class="absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg max-h-48 overflow-y-auto"
            >
              <ul class="py-1">
                <li
                  v-for="(district, index) in filteredDistricts"
                  :key="district.id"
                  @mousedown="selectDistrict(district)"
                  @mouseenter="highlightedDistrictIndex = index"
                  :class="[
                    'px-3 py-2 cursor-pointer text-sm transition-colors duration-150',
                    index === highlightedDistrictIndex ? 'bg-red-50 text-gray-900 border-l-4 border-maneb-primary' : 'text-gray-900 hover:bg-gray-50'
                  ]"
                >
                  <div class="font-medium">{{ district.name }}</div>
                </li>
              </ul>
            </div>
          </div>

          <!-- Loading indicator -->
          <div v-if="isLoadingDistricts" class="mt-1 text-xs text-gray-500 flex items-center">
            <svg class="animate-spin -ml-1 mr-1 h-3 w-3 text-gray-500" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 714 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            Loading...
          </div>

          <!-- Error indicator -->
          <div v-if="districtsError" class="mt-1 text-xs text-red-500">
            {{ districtsError }}
          </div>
        </div>

        <div>
          <label for="subject" class="block text-xs font-medium text-gray-700 mb-1">
            Subject <span class="text-red-500">*</span>
          </label>

          <div class="relative">
            <input
              type="text"
              v-model="subjectSearchQuery"
              @focus="showSubjectDropdown = true"
              @blur="handleSubjectBlur"
              @input="filterSubjects"
              @keydown="handleSubjectKeydown"
              :placeholder="isLoadingSubjects ? 'Loading subjects...' : (!filters.examType && !props.hasRouteParams) ? 'Waiting for exam type...' : 'Search subjects...'"
              :disabled="isLoadingSubjects || (!filters.examType && !props.hasRouteParams)"
              class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-maneb-primary focus:border-maneb-primary block w-full p-2 disabled:bg-gray-100 disabled:cursor-not-allowed"
            />


            <div
              v-if="showSubjectDropdown && filteredSubjects.length > 0"
              class="absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg max-h-48 overflow-y-auto"
            >
              <ul class="py-1">
                <li
                  v-for="(subject, index) in filteredSubjects"
                  :key="subject.id"
                  @mousedown="selectSubject(subject)"
                  @mouseenter="highlightedSubjectIndex = index"
                  :class="[
                    'px-3 py-2 cursor-pointer text-sm transition-colors duration-150',
                    index === highlightedSubjectIndex ? 'bg-red-50 text-gray-900 border-l-4 border-maneb-primary' : 'text-gray-900 hover:bg-gray-50'
                  ]"
                >
                  <div class="font-medium">{{ subject.name }}</div>
                </li>
              </ul>
            </div>
          </div>



          <!-- Loading indicator -->
          <div v-if="isLoadingSubjects" class="mt-1 text-xs text-gray-500 flex items-center">
            <svg class="animate-spin -ml-1 mr-1 h-3 w-3 text-gray-500" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            Loading...
          </div>

          <!-- Error indicator -->
          <div v-if="subjectsError" class="mt-1 text-xs text-red-500">
            {{ subjectsError }}
          </div>
        </div>


        <div>
          <label for="paper" class="block text-xs font-medium text-gray-700 mb-1">
            Paper <span class="text-red-500">*</span>
          </label>
          <select
            id="paper"
            v-model="filters.paper"
            @change="onPaperChange"
            :disabled="!filters.subject || isLoadingPapers"
            class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-maneb-primary focus:border-maneb-primary block w-full p-2 disabled:bg-gray-100 disabled:cursor-not-allowed"
          >
            <option value="">
              {{ isLoadingPapers ? 'Loading papers...' : !filters.subject ? 'Select a subject first' : 'Select Paper' }}
            </option>
            <option v-for="paper in sortedPapers" :key="paper.id" :value="paper.id">
              {{ paper.name }}
            </option>
          </select>

          <!-- Loading indicator -->
          <div v-if="isLoadingPapers" class="mt-1 text-xs text-gray-500 flex items-center">
            <svg class="animate-spin -ml-1 mr-1 h-3 w-3 text-gray-500" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            Loading...
          </div>
        </div>


        <div>
          <label for="centerNumber" class="block text-xs font-medium text-gray-700 mb-1">
            Center Number <span class="text-red-500">*</span>
          </label>
          <input
            id="centerNumber"
            v-model="filters.centerNumber"
            @keydown="handleCenterKeydown"
            type="text"
            placeholder="Enter center number..."
            class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-maneb-primary focus:border-maneb-primary block w-full p-2"
          />
        </div>


        <div>
          <button
            @click="applyFiltersAndLoadStudents"
            :disabled="!allRequiredFiltersSelected || isLoadingStudents"
            class="px-3 py-2 text-sm font-medium rounded-lg focus:outline-none focus:ring-2 transition-colors duration-200"
            :class="allRequiredFiltersSelected && !isLoadingStudents ?
              'bg-maneb-primary text-white hover:bg-maneb-primary-dark focus:ring-maneb-primary' :
              'bg-gray-300 text-gray-500 cursor-not-allowed focus:ring-gray-300'"
          >
            <span v-if="isLoadingStudents" class="flex items-center justify-center">
              <svg class="animate-spin -ml-1 mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 714 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Loading...
            </span>
            <span v-else>Apply Filters</span>
          </button>
        </div>
      </div>


      <div class="mt-2 flex items-center">
        <div class="w-2 h-2 rounded-full mr-2" :class="allRequiredFiltersSelected ? 'bg-green-500' : 'bg-yellow-500'"></div>
        <span class="text-xs text-gray-600">
          {{ allRequiredFiltersSelected ? 'Ready for score entry' : 'Complete all filters above' }}
        </span>
        <span v-if="allRequiredFiltersSelected" class="ml-2 text-xs text-green-600 font-medium">✓</span>
      </div>
    </div>


    <div v-if="allRequiredFiltersSelected && shouldLoadStudents && !isLoadingStudents">
      <ScoreEntryStage
        :selected-filters="currentFilters"
        @score-updated="handleScoreUpdated"
      />
    </div>


    <div v-if="isLoadingStudents" class="mt-6 text-center py-8">
      <div class="flex items-center justify-center">
        <svg class="animate-spin -ml-1 mr-3 h-8 w-8 text-maneb-primary" fill="none" viewBox="0 0 24 24">
          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
          <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 714 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
        <span class="text-lg text-gray-700">Loading students for score entry...</span>
      </div>
      <p class="mt-2 text-sm text-gray-500">Please wait while we fetch the candidates for scoring.</p>
    </div>


    <div v-if="allRequiredFiltersSelected && !shouldLoadStudents && !isLoadingStudents" class="bg-blue-50 border border-blue-200 rounded-lg p-4 text-center">
      <div class="text-blue-600">
        <svg class="mx-auto h-8 w-8 text-blue-500 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
        </svg>
        <p class="text-sm font-medium text-blue-700">Ready to Load Students</p>
        <p class="text-xs text-blue-600 mt-1">Press Enter in center field or click "Apply Filters"</p>
      </div>
    </div>

    <div v-if="!allRequiredFiltersSelected" class="bg-gray-50 border border-gray-200 rounded-lg p-4 text-center">
      <div class="text-gray-500">
        <svg class="mx-auto h-8 w-8 text-gray-400 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
        </svg>
        <p class="text-sm text-gray-600">Complete all filters above to begin score entry</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'

import ScoreEntryStage from './ScoreEntryStage.vue'
import { districtService, examTypeService } from '@/services'
import { useSchoolStore, usePaperStore, useCenterStore } from '@/stores'


interface Props {
  routeExamType?: string
  routeExamYear?: number
  hasRouteParams?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  routeExamType: '',
  routeExamYear: new Date().getFullYear(),
  hasRouteParams: false
})


const schoolStore = useSchoolStore()
const paperStore = usePaperStore()
const centerStore = useCenterStore()


const filters = ref({
  district: '',
  subject: '',
  paper: '',
  centerNumber: '',
  examType: '', // Will be set from route params in loadInitialData
  examYear: props.routeExamYear || new Date().getFullYear()
})

// Store the selected center object to extract schoolId
const selectedCenter = ref<any>(null)


const districts = ref<any[]>([])
const schools = ref<any[]>([])
const availableSubjects = ref<any[]>([])
const availablePapers = ref<any[]>([])
const examTypes = ref<any[]>([])
const allLoadedPapers = ref<any[]>([]) // Store all papers loaded from the API


const subjectSearchQuery = ref('')
const filteredSubjects = ref<any[]>([])
const showSubjectDropdown = ref(false)
const highlightedSubjectIndex = ref(-1)

const districtSearchQuery = ref('')
const filteredDistricts = ref<any[]>([])
const showDistrictDropdown = ref(false)
const highlightedDistrictIndex = ref(-1)






const isLoading = ref(false)
const isLoadingSubjects = ref(false)
const isLoadingPapers = ref(false)
const isLoadingDistricts = ref(false)
const isLoadingStudents = ref(false)
const shouldLoadStudents = ref(false)
const subjectsError = ref<string | null>(null)
const districtsError = ref<string | null>(null)



const isPrimarySchoolExamType = computed(() => {
  // Check both route exam type and current filter exam type
  const routeExamType = props.routeExamType?.toUpperCase() || ''
  const currentExamType = filters.value.examType?.toUpperCase() || ''

  // Find the actual exam type name from examTypes array
  const selectedExamType = examTypes.value.find(et =>
    et.id === filters.value.examType || et.name === props.routeExamType
  )
  const examTypeName = selectedExamType?.name?.toUpperCase() || ''
  const examTypeId = selectedExamType?.id?.toUpperCase() || ''

  const isPrimary = routeExamType.includes('PSLCE') ||
         routeExamType.includes('PRIMARY') ||
         currentExamType.includes('PSLCE') ||
         currentExamType.includes('PRIMARY') ||
         examTypeName.includes('PSLCE') ||
         examTypeName.includes('PRIMARY') ||
         examTypeId.includes('PSLCE') ||
         examTypeId.includes('PRIMARY') ||
         examTypeId === 'PSLCE' ||
         routeExamType === 'PSLCE'
  return isPrimary
})

const sortedPapers = computed(() => {
  return [...availablePapers.value].sort((a, b) => a.name.localeCompare(b.name))
})

const allRequiredFiltersSelected = computed(() => {
  // If no route params, exam type is required
  const examTypeRequired = !props.hasRouteParams ? filters.value.examType : true
  const baseRequired = examTypeRequired && !!filters.value.subject && !!filters.value.paper && !!filters.value.centerNumber

  let result = false
  if (isPrimarySchoolExamType.value) {
    result = baseRequired && !!filters.value.district
  } else {
    result = baseRequired
  }



  return result
})


const currentFilters = computed(() => {
  if (!allRequiredFiltersSelected.value) return null

  return {
    ...filters.value,
    centerNumber: filters.value.centerNumber,
    centerNo: filters.value.centerNumber, // Also provide as centerNo for compatibility
    manebcentreNo: selectedCenter.value?.manebcentreNo || filters.value.centerNumber, // From selected center object
    examType: filters.value.examType,
    examTypeId: filters.value.examType,
    examYear: filters.value.examYear,
    schoolId: selectedCenter.value?.schoolId || null, // Extract schoolId from selected center
    center: selectedCenter.value?.id || null // Also provide center ID
  }
})


const loadInitialData = async () => {
  try {
    isLoading.value = true


    const examTypesData = await examTypeService.fetchExamTypes()
    examTypes.value = examTypesData


    if (props.hasRouteParams && props.routeExamType) {


      // Try multiple strategies to find the exam type
      let examType = examTypes.value.find(et => et.name === props.routeExamType)

      // If not found by exact name match, try by ID
      if (!examType) {
        examType = examTypes.value.find(et => et.id === props.routeExamType)
      }

      // If still not found, try partial matching
      if (!examType) {
        examType = examTypes.value.find(et =>
          et.name.toUpperCase().includes(props.routeExamType.toUpperCase()) ||
          et.id.toUpperCase().includes(props.routeExamType.toUpperCase())
        )
      }

      if (examType) {
        filters.value.examType = examType.id
        filters.value.examYear = props.routeExamYear

        await onExamTypeChange()
        await loadSubjects(examType.id)

        if (isPrimarySchoolExamType.value) {
          await loadDistricts()
        }
      } else {
        filters.value.examType = props.routeExamType
        await loadSubjects(props.routeExamType)

        const commonPatterns = [props.routeExamType, props.routeExamType.toUpperCase(), props.routeExamType.toLowerCase()]
        for (const pattern of commonPatterns) {
          await loadSubjects(pattern)
          if (availableSubjects.value.length > 0) {
            break
          }
        }
      }
    } else {

    }
  } catch (error) {
    console.error('Error loading initial data:', error)
  } finally {
    isLoading.value = false

  }
}

const loadDistricts = async () => {
  try {
    isLoadingDistricts.value = true
    districtsError.value = null

    const response = await districtService.fetchDistricts()
    districts.value = response
    filterDistricts()
  } catch (error) {
    districtsError.value = 'Failed to load districts. Please try again.'
    districts.value = []
    filteredDistricts.value = []
  } finally {
    isLoadingDistricts.value = false
  }
}


const loadSubjects = async (examTypeId?: string) => {
  const targetExamTypeId = examTypeId || filters.value.examType

  if (!targetExamTypeId) {
    availableSubjects.value = []
    return
  }

  isLoadingSubjects.value = true
  try {
    let papers = await paperStore.fetchPapers({ examTypeId: targetExamTypeId })

    if (!Array.isArray(papers)) {
      console.error('Papers is not an array:', papers)
      availableSubjects.value = []
      return
    }

    if (papers.length === 0) {
      papers = await paperStore.fetchPapers({})

      papers = papers.filter((paper: any) => {
        const examLevel = paper.examLevel || ''
        return examLevel.toUpperCase().includes(targetExamTypeId.toUpperCase()) ||
               targetExamTypeId.toUpperCase().includes(examLevel.toUpperCase()) ||
               examLevel === targetExamTypeId
      })
    }

    const filteredPapers = papers
    allLoadedPapers.value = filteredPapers

    console.log('📋 Sample paper from loadSubjects:', filteredPapers[0])
    console.log('🔍 Paper fields available:', filteredPapers[0] ? Object.keys(filteredPapers[0]) : 'No papers')

    const subjectMap = new Map()
    filteredPapers.forEach((paper: any) => {
      if (paper.subjectCode && paper.subjectName) {
        subjectMap.set(paper.subjectCode, {
          id: paper.subjectCode,
          name: paper.subjectName
        })
      }
    })

    availableSubjects.value = Array.from(subjectMap.values()).sort((a, b) => a.name.localeCompare(b.name))
    filterSubjects()

    if (filters.value.subject && !availableSubjects.value.find((s: any) => s.id === filters.value.subject)) {
      filters.value.subject = ''
      filters.value.paper = ''
      availablePapers.value = []
    }

  } catch (error) {
    console.error('❌ Error loading subjects:', error)
    subjectsError.value = 'Failed to load subjects. Please try again.'
    availableSubjects.value = []
  } finally {
    isLoadingSubjects.value = false
  }
}

const loadPapers = async () => {


  if (!filters.value.subject) {

    availablePapers.value = []
    return
  }

  try {
    isLoadingPapers.value = true
    console.log('� Starting to fetch papers for subject:', filters.value.subject)

    // Filter papers from the already loaded papers (from loadSubjects)
    // Since subjects and papers come from the same API endpoint
    console.log('� Filtering papers from already loaded papers for subject:', filters.value.subject)
    console.log('� Total papers available:', allLoadedPapers.value.length)

    // Filter by the selected subject code
    const filteredPapers = allLoadedPapers.value.filter((paper: any) =>
      paper.subjectCode === filters.value.subject
    )

    console.log('🔍 Sample paper structure before transformation:', filteredPapers[0])

    // Transform papers to have consistent structure
    const transformedPapers = filteredPapers.map((paper: any) => ({
      id: paper.paperId || paper.id || paper.code,
      name: paper.paperName || paper.name,
      code: paper.paperId || paper.code || paper.id,
      subjectCode: paper.subjectCode,
      subjectName: paper.subjectName
    }))

    console.log('✅ Sample transformed paper:', transformedPapers[0])

    availablePapers.value = transformedPapers
    console.log('📋 Available papers after transformation:', availablePapers.value.length, 'papers')

    if (filters.value.paper && !transformedPapers.find((p: any) => p.id === filters.value.paper)) {
      console.log('⚠️ Current paper selection not found in transformed papers, clearing selection')
      filters.value.paper = ''
    }

  } catch (error) {
    console.error('Error loading papers:', error)
    availablePapers.value = []
  } finally {
    isLoadingPapers.value = false

  }
}



const onExamTypeChange = async () => {
  console.log('🔄 Exam type changed to:', filters.value.examType)
  filters.value.district = ''
  filters.value.subject = ''
  filters.value.paper = ''
  filters.value.centerNumber = ''
  selectedCenter.value = null
  shouldLoadStudents.value = false

  if (filters.value.examType) {
    await loadSubjects()

    // Load districts if this is a Primary School exam type
    console.log('🏫 Checking if should load districts. isPrimarySchoolExamType:', isPrimarySchoolExamType.value)
    if (isPrimarySchoolExamType.value) {
      console.log('✅ Loading districts for Primary School exam type')
      await loadDistricts()
    } else {
      console.log('ℹ️ Not a Primary School exam type, skipping district loading')
    }
  }
}

const onDistrictChange = async () => {
  filters.value.centerNumber = ''
  selectedCenter.value = null
  shouldLoadStudents.value = false
}

// Function to find center by center number and extract schoolId
const findCenterByNumber = async (centerNumber: string) => {
  try {
    console.log('🔍 Looking up center by number:', centerNumber)

    // Use the center store to find the center
    const centers = await centerStore.fetchCenters({
      search: centerNumber,
      pageSize: 100
    })

    // Find exact match by manebcentreNo
    const center = centers.find((c: any) => c.manebcentreNo === centerNumber)

    if (center) {
      console.log('✅ Found center:', center)
      selectedCenter.value = center
      return center
    } else {
      console.warn('⚠️ Center not found for number:', centerNumber)
      selectedCenter.value = null
      return null
    }
  } catch (error) {
    console.error('❌ Error looking up center:', error)
    selectedCenter.value = null
    return null
  }
}

const onSubjectChange = async () => {
  filters.value.paper = ''
  shouldLoadStudents.value = false
  await loadPapers()
}

const onPaperChange = () => {
  shouldLoadStudents.value = false
}



const handleScoreUpdated = () => {
}

const applyFiltersAndLoadStudents = async () => {
  console.log('🚀 applyFiltersAndLoadStudents called')
  console.log('🔍 allRequiredFiltersSelected:', allRequiredFiltersSelected.value)

  if (!allRequiredFiltersSelected.value) {
    console.log('⚠️ Not all required filters selected, aborting')
    return
  }

  try {
    console.log('⏳ Setting loading state...')
    isLoadingStudents.value = true

    // Look up the center by center number to get schoolId if not already done
    if (filters.value.centerNumber && !selectedCenter.value) {
      console.log('🔍 Looking up center before loading students...')
      await findCenterByNumber(filters.value.centerNumber)
    }

    // Set shouldLoadStudents to trigger the ScoreEntryStage component
    shouldLoadStudents.value = true
    console.log('✅ Filters applied, loading students with:', currentFilters.value)
  } catch (error) {
    console.error('❌ Error applying filters:', error)
  } finally {
    isLoadingStudents.value = false
  }
}

// Subject search functions
const filterSubjects = () => {
  const query = subjectSearchQuery.value.toLowerCase()
  if (!query) {
    filteredSubjects.value = [...availableSubjects.value].sort((a, b) => a.name.localeCompare(b.name))
  } else {
    filteredSubjects.value = availableSubjects.value
      .filter(subject =>
        subject.name.toLowerCase().includes(query) ||
        subject.id.toLowerCase().includes(query)
      )
      .sort((a, b) => a.name.localeCompare(b.name))
  }
  highlightedSubjectIndex.value = filteredSubjects.value.length > 0 ? 0 : -1

  // Show dropdown when there are results and user is typing
  if (filteredSubjects.value.length > 0 && query) {
    showSubjectDropdown.value = true
  }
}

const selectSubject = (subject: any) => {
  filters.value.subject = subject.id
  subjectSearchQuery.value = subject.name
  showSubjectDropdown.value = false
  highlightedSubjectIndex.value = -1
  onSubjectChange()
}

const handleSubjectBlur = () => {
  // Delay hiding to allow click events to fire
  setTimeout(() => {
    showSubjectDropdown.value = false
  }, 150)
}

const handleSubjectKeydown = (event: KeyboardEvent) => {
  // Handle Enter key even when dropdown is not shown (for quick selection)
  if (event.key === 'Enter') {
    event.preventDefault()

    // If dropdown is not shown, show it and highlight first item
    if (!showSubjectDropdown.value && filteredSubjects.value.length > 0) {
      showSubjectDropdown.value = true
      highlightedSubjectIndex.value = 0
      return
    }

    // If dropdown is shown, select highlighted item or first item
    if (showSubjectDropdown.value && filteredSubjects.value.length > 0) {
      const indexToSelect = highlightedSubjectIndex.value >= 0 ? highlightedSubjectIndex.value : 0
      selectSubject(filteredSubjects.value[indexToSelect])
      return
    }
  }

  // Other keys only work when dropdown is visible
  if (!showSubjectDropdown.value || filteredSubjects.value.length === 0) return

  switch (event.key) {
    case 'ArrowDown':
      event.preventDefault()
      // Show dropdown if not visible
      if (!showSubjectDropdown.value) {
        showSubjectDropdown.value = true
        highlightedSubjectIndex.value = 0
      } else {
        highlightedSubjectIndex.value = Math.min(
          highlightedSubjectIndex.value + 1,
          filteredSubjects.value.length - 1
        )
      }
      break
    case 'ArrowUp':
      event.preventDefault()
      highlightedSubjectIndex.value = Math.max(highlightedSubjectIndex.value - 1, 0)
      break
    case 'Escape':
      showSubjectDropdown.value = false
      highlightedSubjectIndex.value = -1
      break
    case 'Tab':
      // Allow tab to close dropdown and move to next field
      showSubjectDropdown.value = false
      break
  }
}



// District search functions
const filterDistricts = () => {
  const query = districtSearchQuery.value.toLowerCase()
  if (!query) {
    filteredDistricts.value = [...districts.value].sort((a, b) => a.name.localeCompare(b.name))
  } else {
    filteredDistricts.value = districts.value
      .filter(district =>
        district.name.toLowerCase().includes(query) ||
        district.id.toLowerCase().includes(query)
      )
      .sort((a, b) => a.name.localeCompare(b.name))
  }
  highlightedDistrictIndex.value = filteredDistricts.value.length > 0 ? 0 : -1

  // Show dropdown when there are results and user is typing
  if (filteredDistricts.value.length > 0 && query) {
    showDistrictDropdown.value = true
  }
}

const selectDistrict = (district: any) => {
  filters.value.district = district.id
  districtSearchQuery.value = district.name
  showDistrictDropdown.value = false
  highlightedDistrictIndex.value = -1
  onDistrictChange()
}

const handleDistrictBlur = () => {
  // Delay hiding to allow click events to fire
  setTimeout(() => {
    showDistrictDropdown.value = false
  }, 150)
}

const handleDistrictKeydown = (event: KeyboardEvent) => {
  // Handle Enter key even when dropdown is not shown (for quick selection)
  if (event.key === 'Enter') {
    event.preventDefault()

    // If dropdown is not shown, show it and highlight first item
    if (!showDistrictDropdown.value && filteredDistricts.value.length > 0) {
      showDistrictDropdown.value = true
      highlightedDistrictIndex.value = 0
      return
    }

    // If dropdown is shown, select highlighted item or first item
    if (showDistrictDropdown.value && filteredDistricts.value.length > 0) {
      const indexToSelect = highlightedDistrictIndex.value >= 0 ? highlightedDistrictIndex.value : 0
      selectDistrict(filteredDistricts.value[indexToSelect])
      return
    }
  }

  // Other keys only work when dropdown is visible
  if (!showDistrictDropdown.value || filteredDistricts.value.length === 0) return

  switch (event.key) {
    case 'ArrowDown':
      event.preventDefault()
      // Show dropdown if not visible
      if (!showDistrictDropdown.value) {
        showDistrictDropdown.value = true
        highlightedDistrictIndex.value = 0
      } else {
        highlightedDistrictIndex.value = Math.min(
          highlightedDistrictIndex.value + 1,
          filteredDistricts.value.length - 1
        )
      }
      break
    case 'ArrowUp':
      event.preventDefault()
      highlightedDistrictIndex.value = Math.max(highlightedDistrictIndex.value - 1, 0)
      break
    case 'Escape':
      showDistrictDropdown.value = false
      highlightedDistrictIndex.value = -1
      break
    case 'Tab':
      // Allow tab to close dropdown and move to next field
      showDistrictDropdown.value = false
      break
  }
}

// Update center keydown handler to lookup center and extract schoolId
const handleCenterKeydown = async (event: KeyboardEvent) => {
  console.log('🎯 Center keydown event:', event.key, 'Center number:', filters.value.centerNumber)

  if (event.key === 'Enter') {
    event.preventDefault()
    console.log('⚡ Enter pressed on center number input')

    // Look up the center by center number to get schoolId
    if (filters.value.centerNumber) {
      console.log('🔍 Looking up center:', filters.value.centerNumber)
      await findCenterByNumber(filters.value.centerNumber)
    }

    // Only trigger loading if all required filters are selected
    console.log('🔍 Checking if all required filters selected:', allRequiredFiltersSelected.value)
    if (allRequiredFiltersSelected.value) {
      console.log('✅ All filters selected, applying filters and loading students')
      await applyFiltersAndLoadStudents()
    } else {
      console.log('⚠️ Not all required filters selected')
    }
  }
}

watch(() => props.routeExamType, async (newExamType, oldExamType) => {
  if (newExamType && newExamType !== oldExamType) {

    // Try multiple strategies to find the exam type
    let examType = examTypes.value.find(et => et.name === newExamType)

    // If not found by exact name match, try by ID
    if (!examType) {
      examType = examTypes.value.find(et => et.id === newExamType)
    }

    // If still not found, try partial matching
    if (!examType) {
      examType = examTypes.value.find(et =>
        et.name.toUpperCase().includes(newExamType.toUpperCase()) ||
        et.id.toUpperCase().includes(newExamType.toUpperCase())
      )
    }

    if (examType) {

      filters.value.examType = examType.id
      filters.value.examYear = props.routeExamYear

      // Trigger the exam type change to load dependent data (using working FilteringStage logic)
      await onExamTypeChange()

      // Load districts if Primary School
      console.log('🏫 Checking if should load districts in route watcher. isPrimarySchoolExamType:', isPrimarySchoolExamType.value)
      if (isPrimarySchoolExamType.value) {
        console.log('✅ Loading districts for Primary School exam type in route watcher')
        await loadDistricts()
      } else {
        console.log('ℹ️ Not a Primary School exam type in route watcher, skipping district loading')
      }
    } else {
      console.warn('⚠️ Exam type not found in examTypes array:', newExamType, 'Available:', examTypes.value.map(et => ({ id: et.id, name: et.name })))

      // Fallback: Try to load subjects using the route exam type directly

      filters.value.examType = newExamType
      await loadSubjects(newExamType)
    }
  }
}, { immediate: false })

watch(() => props.hasRouteParams, () => {
}, { immediate: true })

watch(() => props.routeExamYear, (newYear) => {
  if (newYear && newYear !== filters.value.examYear) {
    filters.value.examYear = newYear
  }
})

watch(() => filters.value.centerNumber, () => {
  shouldLoadStudents.value = false
})


watch(() => availableSubjects.value, () => {
  filterSubjects()
}, { deep: true })

watch(() => districts.value, () => {
  filterDistricts()
}, { deep: true })

onMounted(() => {
  filterSubjects()
  filterDistricts()
  loadInitialData()
})
</script>

<style scoped>
.bg-maneb-primary {
  background-color: #a12c2c;
}

.bg-maneb-primary-dark {
  background-color: #8b2424;
}

.hover\:bg-maneb-primary-dark:hover {
  background-color: #8b2424;
}

.focus\:ring-maneb-primary:focus {
  --tw-ring-color: rgba(161, 44, 44, 0.5);
}

.focus\:border-maneb-primary:focus {
  border-color: #a12c2c;
}
</style>

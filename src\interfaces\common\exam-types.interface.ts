/**
 * @fileoverview Standardized exam type definitions for MANEB Results Management System
 * @description This file contains all exam-related type definitions with consistent naming conventions
 */

/**
 * Standardized exam levels supported by MANEB
 * @description These are the official examination levels in the Malawi education system
 */
export type ExamLevel = 'PLCE' | 'MSCE' | 'TTC' | 'JCE' | 'PSLCE';

/**
 * Legacy alias for ExamLevel
 * @deprecated Use ExamLevel instead
 */
export type ExamType = ExamLevel;

/**
 * Grade levels used across all examinations
 * @description Standard grading scale from A (highest) to F (fail)
 */
export type GradeLevel = 'A' | 'B' | 'C' | 'D' | 'F';

/**
 * Score types used in the marking and verification process
 * @description Official MANEB score categories for the examination workflow
 */
export type ScoreType = 'first' | 'verify' | 'remark' | 'final' | 'regression' | 'absent';

/**
 * Approval status for grade boundaries and other administrative items
 * @description Workflow states for items requiring approval
 */
export type ApprovalStatus = 'pending' | 'approved' | 'rejected';

/**
 * Score status in the verification workflow
 * @description Tracks the current state of a score entry through the approval process
 */
export type ScoreStatus = 'pending' | 'verified' | 'approved' | 'final_approved' | 'rejected';

/**
 * Detailed exam level information with descriptions
 * @description Extended information about each examination level
 */
export interface ExamLevelInfo {
  /** The exam level code */
  value: ExamLevel;
  /** Display name for the exam */
  label: string;
  /** Full description of the examination */
  description: string;
  /** Typical duration in years */
  duration?: number;
  /** Target age group */
  targetAge?: string;
}

/**
 * Grade level information with scoring details
 * @description Extended information about each grade level
 */
export interface GradeLevelInfo {
  /** The grade letter */
  level: GradeLevel;
  /** Display name for the grade */
  name: string;
  /** Description of what this grade represents */
  description: string;
  /** Typical score range (informational) */
  typicalRange?: string;
  /** Whether this grade is considered passing */
  isPass: boolean;
}

/**
 * Score type information with workflow details
 * @description Extended information about each score type in the marking process
 */
export interface ScoreTypeInfo {
  /** The score type code */
  type: ScoreType;
  /** Display name for the score type */
  label: string;
  /** Description of when this score type is used */
  description: string;
  /** Order in the workflow (1 = first, 2 = second, etc.) */
  workflowOrder: number;
  /** Whether this score type requires special permissions */
  requiresPermission?: boolean;
}

/**
 * @deprecated Use EXAM_TYPES from '@/utils/validation/exam-types' instead
 * Predefined exam level configurations - maintained for backward compatibility
 * @description Standard exam level definitions used throughout the system
 */
export const EXAM_LEVELS: ExamLevelInfo[] = [
  {
    value: 'PLCE',
    label: 'PLCE',
    description: 'Primary Leaving Certificate Examination',
    duration: 8,
    targetAge: '13-15 years'
  },
  {
    value: 'MSCE',
    label: 'MSCE',
    description: 'Malawi School Certificate of Education',
    duration: 4,
    targetAge: '17-19 years'
  },
  {
    value: 'TTC',
    label: 'TTC',
    description: 'Teacher Training College',
    duration: 2,
    targetAge: '19+ years'
  },
  {
    value: 'JCE',
    label: 'JCE',
    description: 'Junior Certificate of Education',
    duration: 2,
    targetAge: '15-17 years'
  },
  {
    value: 'PSLCE',
    label: 'PSLCE',
    description: 'Primary School Leaving Certificate Examination',
    duration: 8,
    targetAge: '13-15 years'
  }
];

/**
 * Predefined grade level configurations
 * @description Standard grade definitions used throughout the system
 */
export const GRADE_LEVELS: GradeLevelInfo[] = [
  {
    level: 'A',
    name: 'Distinction',
    description: 'Excellent performance',
    typicalRange: '',
    isPass: true
  },
  {
    level: 'B',
    name: 'Credit',
    description: 'Very good performance',
    typicalRange: '',
    isPass: true
  },
  {
    level: 'C',
    name: 'Pass',
    description: 'Good performance',
    typicalRange: '',
    isPass: true
  },
  {
    level: 'D',
    name: 'Pass',
    description: 'Satisfactory performance',
    typicalRange: '',
    isPass: true
  },
  {
    level: 'F',
    name: 'Fail',
    description: 'Below required standard',
    typicalRange: '',
    isPass: false
  }
];

/**
 * Predefined score type configurations
 * @description Standard score type definitions used in the marking workflow
 */
export const SCORE_TYPES: ScoreTypeInfo[] = [
  {
    type: 'first',
    label: 'First Marking',
    description: 'Initial score entry by first examiner',
    workflowOrder: 1
  },
  {
    type: 'verify',
    label: 'Verification',
    description: 'Score verification by second examiner',
    workflowOrder: 2,
    requiresPermission: true
  },
  {
    type: 'remark',
    label: 'Remark',
    description: 'Score adjustment after review',
    workflowOrder: 3,
    requiresPermission: true
  },
  {
    type: 'final',
    label: 'Final Score',
    description: 'Final approved score',
    workflowOrder: 4,
    requiresPermission: true
  },
  {
    type: 'regression',
    label: 'Regression',
    description: 'Statistical adjustment score',
    workflowOrder: 5,
    requiresPermission: true
  },
  {
    type: 'absent',
    label: 'Absent',
    description: 'Candidate was absent',
    workflowOrder: 0
  }
];

/**
 * Helper function to get exam level information by value
 * @param examLevel - The exam level to look up
 * @returns The exam level information or undefined if not found
 */
export function getExamLevelInfo(examLevel: ExamLevel): ExamLevelInfo | undefined {
  return EXAM_LEVELS.find(level => level.value === examLevel);
}

/**
 * Helper function to get grade level information by level
 * @param gradeLevel - The grade level to look up
 * @returns The grade level information or undefined if not found
 */
export function getGradeLevelInfo(gradeLevel: GradeLevel): GradeLevelInfo | undefined {
  return GRADE_LEVELS.find(grade => grade.level === gradeLevel);
}

/**
 * Helper function to get score type information by type
 * @param scoreType - The score type to look up
 * @returns The score type information or undefined if not found
 */
export function getScoreTypeInfo(scoreType: ScoreType): ScoreTypeInfo | undefined {
  return SCORE_TYPES.find(type => type.type === scoreType);
}

// ============================================================================
// RE-EXPORTS FROM CENTRALIZED EXAM TYPES
// ============================================================================

/**
 * Re-export standardized exam types from centralized utility
 * @description Use these instead of the deprecated EXAM_LEVELS above
 */
export {
  EXAM_TYPES,
  EXAM_TYPE_VALUES,
  getActiveExamTypes,
  getExamTypeOptions,
  getExamTypeDescription,
  getExamTypeLabel,
  getExamTypeDisplay,
  getExamTypeInfo,
  isValidExamType,
  generateExamTypeFilterOptions
} from '@/utils/validation/exam-types';
export type { ExamTypeValue, ExamTypeOption, ExamTypeInfo } from '@/utils/validation/exam-types';

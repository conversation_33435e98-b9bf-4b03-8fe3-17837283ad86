import type { AxiosRequestConfig } from 'axios';
import type { ApiClientConfig, RequestConfig } from '@/interfaces';
import { BaseApiClient } from './base-api-client';

/**
 * Main API client for MANEB Results Management System
 * Extends BaseApiClient with specific configuration for the main API
 */
export class ApiClient extends BaseApiClient {
  constructor(config: ApiClientConfig) {
    super({
      baseURL: config.baseURL,
      timeout: config.timeout || 10000,
      enableDebugLogging: import.meta.env.DEV,
    });

    // Load existing token on initialization
    this.loadToken();
  }

  /**
   * Public HTTP methods for external use
   * These delegate to the protected methods in BaseApiClient
   */
  async get<T>(url: string, config?: RequestConfig): Promise<T> {
    return super.get<T>(url, config as AxiosRequestConfig);
  }

  async post<T>(url: string, data?: any, config?: RequestConfig): Promise<T> {
    return super.post<T>(url, data, config as AxiosRequestConfig);
  }

  async put<T>(url: string, data?: any, config?: RequestConfig): Promise<T> {
    return super.put<T>(url, data, config as AxiosRequestConfig);
  }

  async delete<T>(url: string, config?: RequestConfig): Promise<T> {
    return super.delete<T>(url, config as AxiosRequestConfig);
  }

  async patch<T>(url: string, data?: any, config?: RequestConfig): Promise<T> {
    return super.patch<T>(url, data, config as AxiosRequestConfig);
  }
}

// Create singleton instance
const apiClient = new ApiClient({
  baseURL: import.meta.env.VITE_API_BASE_URL || 'https://localhost:7266',
});

// Debug API configuration (console logging removed)

// Load token on initialization
apiClient.loadToken();

export default apiClient;

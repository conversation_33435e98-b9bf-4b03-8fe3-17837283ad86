/**
 * @fileoverview Endpoint Validator for MANEB Results Management System
 * @description Utility to validate and test API endpoint formation
 */

import apiClient from '@/services/core/api-client';

/**
 * Endpoint test result interface
 */
export interface EndpointTestResult {
  endpoint: string;
  fullUrl: string;
  isValid: boolean;
  error?: string;
  responseTime?: number;
  statusCode?: number;
}

/**
 * Endpoint Validator class
 */
export class EndpointValidator {
  private baseUrl: string;

  constructor() {
    this.baseUrl = import.meta.env.VITE_API_BASE_URL || 'https://localhost:7266';
  }

  /**
   * Test if an endpoint is properly formed
   */
  async testEndpoint(endpoint: string): Promise<EndpointTestResult> {
    const startTime = Date.now();
    const fullUrl = `${this.baseUrl}${endpoint}`;
    
    const result: EndpointTestResult = {
      endpoint,
      fullUrl,
      isValid: false
    };

    try {
      console.log(`🔍 Testing endpoint: ${fullUrl}`);
      
      // Make a GET request to test if endpoint exists and is accessible
      const response = await fetch(fullUrl, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('auth_token') || ''}`,
          'Content-Type': 'application/json'
        }
      });

      result.responseTime = Date.now() - startTime;
      result.statusCode = response.status;
      result.isValid = response.status < 400;

      if (!result.isValid) {
        result.error = `HTTP ${response.status}: ${response.statusText}`;
      }

      console.log(`✅ Endpoint test result:`, result);
      
    } catch (error: any) {
      result.responseTime = Date.now() - startTime;
      result.error = error.message;
      result.isValid = false;
      
      console.error(`❌ Endpoint test failed:`, result);
    }

    return result;
  }

  /**
   * Test multiple endpoints
   */
  async testEndpoints(endpoints: string[]): Promise<EndpointTestResult[]> {
    console.log(`🧪 Testing ${endpoints.length} endpoints...`);
    
    const results: EndpointTestResult[] = [];
    
    for (const endpoint of endpoints) {
      const result = await this.testEndpoint(endpoint);
      results.push(result);
      
      // Small delay between requests to avoid overwhelming the server
      await new Promise(resolve => setTimeout(resolve, 100));
    }
    
    return results;
  }

  /**
   * Test all MANEB API endpoints
   */
  async testManebEndpoints(): Promise<EndpointTestResult[]> {
    const endpoints = [
      '/api/ExamType',
      '/api/District',
      '/api/Paper',
      '/api/subject',
      '/api/centre',
      '/api/school',
      '/api/candidate',
      '/api/scoreentry',
      '/api/User',
      '/api/Role',
      '/api/health'
    ];

    return this.testEndpoints(endpoints);
  }

  /**
   * Validate URL formation
   */
  validateUrlFormation(endpoint: string): { isValid: boolean; issues: string[] } {
    const issues: string[] = [];
    const fullUrl = `${this.baseUrl}${endpoint}`;

    // Check for double slashes (except after protocol)
    if (fullUrl.includes('//') && !fullUrl.startsWith('http://') && !fullUrl.startsWith('https://')) {
      const doubleSlashIndex = fullUrl.indexOf('//', fullUrl.indexOf('://') + 3);
      if (doubleSlashIndex !== -1) {
        issues.push(`Double slash found at position ${doubleSlashIndex}: ${fullUrl}`);
      }
    }

    // Check if endpoint starts with /
    if (!endpoint.startsWith('/')) {
      issues.push(`Endpoint should start with '/': ${endpoint}`);
    }

    // Check if base URL ends with / and endpoint starts with /
    if (this.baseUrl.endsWith('/') && endpoint.startsWith('/')) {
      issues.push(`Base URL ends with '/' and endpoint starts with '/': ${this.baseUrl} + ${endpoint}`);
    }

    // Check if neither base URL ends with / nor endpoint starts with /
    if (!this.baseUrl.endsWith('/') && !endpoint.startsWith('/')) {
      issues.push(`Missing '/' between base URL and endpoint: ${this.baseUrl} + ${endpoint}`);
    }

    return {
      isValid: issues.length === 0,
      issues
    };
  }

  /**
   * Generate endpoint report
   */
  async generateEndpointReport(): Promise<{
    summary: { total: number; valid: number; invalid: number };
    results: EndpointTestResult[];
    urlValidation: Array<{ endpoint: string; validation: { isValid: boolean; issues: string[] } }>;
  }> {
    // Generating comprehensive endpoint report (console logging removed)
    
    const results = await this.testManebEndpoints();
    
    const urlValidation = [
      '/api/ExamType',
      '/api/District',
      '/api/Paper',
      '/api/subject',
      '/api/centre',
      '/api/school',
      '/api/candidate',
      '/api/scoreentry'
    ].map(endpoint => ({
      endpoint,
      validation: this.validateUrlFormation(endpoint)
    }));

    const summary = {
      total: results.length,
      valid: results.filter(r => r.isValid).length,
      invalid: results.filter(r => !r.isValid).length
    };

    // Endpoint report summary (console logging removed)

    return {
      summary,
      results,
      urlValidation
    };
  }

  /**
   * Log detailed endpoint information (console logging removed)
   */
  logEndpointInfo(): void {
    // Endpoint configuration details (console logging removed)
    // Base URL validation still occurs but output is suppressed
  }
}

// Export singleton instance
export const endpointValidator = new EndpointValidator();

if (import.meta.env.DEV) {
  endpointValidator.logEndpointInfo();

  (window as any).testEndpoints = async () => {
    const report = await endpointValidator.generateEndpointReport();
    return report;
  };

  (window as any).testSingleEndpoint = async (endpoint: string) => {
    const result = await endpointValidator.testEndpoint(endpoint);
    return result;
  };
}

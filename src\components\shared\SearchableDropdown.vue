<template>
  <div class="relative">
    <label v-if="label" :for="id" class="block text-sm font-medium text-gray-700 mb-2">
      {{ label }} <span v-if="required" class="text-red-500">*</span>
    </label>
    
    <div class="relative">
      <input
        :id="id"
        type="text"
        :value="searchQuery"
        @input="onSearchInput"
        @focus="showDropdown = true"
        @blur="handleBlur"
        @keydown="handleKeydown"
        :placeholder="placeholder"
        :disabled="disabled"
        class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-maneb-primary focus:border-maneb-primary block w-full p-2.5 pr-10 disabled:bg-gray-100 disabled:cursor-not-allowed"
        autocomplete="off"
      />
      
      <!-- Clear button -->
      <div class="absolute inset-y-0 right-0 flex items-center pr-3">
        <button
          v-if="searchQuery && !disabled"
          @click="clearSelection"
          type="button"
          class="mr-2 text-gray-400 hover:text-gray-600 focus:outline-none"
        >
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
        <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
        </svg>
      </div>
    </div>

    <!-- Dropdown -->
    <div
      v-if="showDropdown && filteredOptions.length > 0"
      class="absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg max-h-60 overflow-y-auto"
    >
      <ul class="py-1">
        <li
          v-for="(option, index) in filteredOptions"
          :key="option.id"
          @mousedown="selectOption(option)"
          @mouseenter="highlightedIndex = index"
          :class="[
            'px-3 py-2 cursor-pointer text-sm transition-colors duration-150',
            index === highlightedIndex ? 'bg-maneb-primary text-white' : 'text-gray-900 hover:bg-gray-100'
          ]"
        >
          <div class="font-medium">{{ option.name }}</div>
          <div v-if="option.description" :class="[
            'text-xs',
            index === highlightedIndex ? 'text-white text-opacity-80' : 'text-gray-500'
          ]">{{ option.description }}</div>
        </li>
      </ul>
    </div>

    <!-- No results -->
    <div
      v-if="showDropdown && searchQuery && filteredOptions.length === 0 && !isLoading"
      class="absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg"
    >
      <div class="px-3 py-2 text-sm text-gray-500">
        No results found for "{{ searchQuery }}"
      </div>
    </div>

    <!-- Loading -->
    <div
      v-if="showDropdown && isLoading"
      class="absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg"
    >
      <div class="px-3 py-2 text-sm text-gray-500 flex items-center">
        <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-gray-500" fill="none" viewBox="0 0 24 24">
          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
          <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
        Loading...
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'

// Types
interface DropdownOption {
  id: string
  name: string
  description?: string
}

// Props
interface Props {
  id: string
  label?: string
  placeholder?: string
  required?: boolean
  disabled?: boolean
  options: DropdownOption[]
  modelValue: string
  isLoading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: 'Search...',
  required: false,
  disabled: false,
  isLoading: false
})

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: string]
  'change': [option: DropdownOption | null]
  'search': [query: string]
}>()

// Reactive state
const searchQuery = ref('')
const showDropdown = ref(false)
const highlightedIndex = ref(-1)

// Computed
const filteredOptions = computed(() => {
  if (!searchQuery.value) {
    return props.options
  }
  
  const query = searchQuery.value.toLowerCase()
  return props.options.filter(option =>
    option.name.toLowerCase().includes(query) ||
    (option.description && option.description.toLowerCase().includes(query))
  )
})

const selectedOption = computed(() => {
  return props.options.find(option => option.id === props.modelValue)
})

// Methods
const onSearchInput = (event: Event) => {
  const target = event.target as HTMLInputElement
  searchQuery.value = target.value
  showDropdown.value = true
  highlightedIndex.value = -1

  // Emit search event for live search functionality
  emit('search', searchQuery.value)

  // Clear selection if search doesn't match current selection
  if (selectedOption.value && !selectedOption.value.name.toLowerCase().includes(searchQuery.value.toLowerCase())) {
    emit('update:modelValue', '')
    emit('change', null)
  }
}

const selectOption = (option: DropdownOption) => {
  searchQuery.value = option.name
  showDropdown.value = false
  highlightedIndex.value = -1
  emit('update:modelValue', option.id)
  emit('change', option)
}

const clearSelection = () => {
  searchQuery.value = ''
  showDropdown.value = false
  highlightedIndex.value = -1
  emit('update:modelValue', '')
  emit('change', null)
}

const handleBlur = () => {
  // Delay hiding dropdown to allow for click events
  setTimeout(() => {
    showDropdown.value = false
    highlightedIndex.value = -1
    
    // If no valid selection, clear the search
    if (!props.modelValue) {
      searchQuery.value = ''
    }
  }, 150)
}

const handleKeydown = (event: KeyboardEvent) => {
  if (!showDropdown.value) return

  switch (event.key) {
    case 'ArrowDown':
      event.preventDefault()
      highlightedIndex.value = Math.min(highlightedIndex.value + 1, filteredOptions.value.length - 1)
      break
    case 'ArrowUp':
      event.preventDefault()
      highlightedIndex.value = Math.max(highlightedIndex.value - 1, -1)
      break
    case 'Enter':
      event.preventDefault()
      if (highlightedIndex.value >= 0 && filteredOptions.value[highlightedIndex.value]) {
        selectOption(filteredOptions.value[highlightedIndex.value])
      }
      break
    case 'Escape':
      showDropdown.value = false
      highlightedIndex.value = -1
      break
  }
}

// Watch for external value changes
watch(() => props.modelValue, (newValue) => {
  const option = props.options.find(opt => opt.id === newValue)
  searchQuery.value = option ? option.name : ''
}, { immediate: true })
</script>

<style scoped>
.bg-maneb-primary {
  background-color: #a12c2c;
}

.text-maneb-primary {
  color: #a12c2c;
}

.focus\:ring-maneb-primary:focus {
  --tw-ring-color: #a12c2c;
}

.focus\:border-maneb-primary:focus {
  --tw-border-opacity: 1;
  border-color: #a12c2c;
}
</style>

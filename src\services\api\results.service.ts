import type {
  StudentResultDto,
  ResultsFilterDto,
  GradeLevel,
  GradeBoundaryDto,
  PaperDto,
  SubjectDto,
  StudentCertificateDto,
  CertificateSubjectResult,
  CertificatePaperResult
} from '@/interfaces';

// Mock score data that matches the score entry system
const mockScoreData = [
  // John <PERSON>a - STU001
  { studentId: 'STU001', subjectId: 'SUBJ001', paperId: 'PAP001', year: 2024, score: 85 }, // Math Paper 1
  { studentId: 'STU001', subjectId: 'SUBJ001', paperId: 'PAP002', year: 2024, score: 78 }, // Math Paper 2
  { studentId: 'STU001', subjectId: 'SUBJ002', paperId: 'PAP003', year: 2024, score: 82 }, // English Paper 1
  
  // Mary <PERSON>ri - STU002
  { studentId: 'STU002', subjectId: 'SUBJ001', paperId: 'PAP001', year: 2024, score: 45 }, // Math Paper 1
  { studentId: 'STU002', subjectId: 'SUBJ002', paperId: 'PAP003', year: 2024, score: 67 }, // English Paper 1
  
  // <PERSON> - <PERSON>U003
  { studentId: 'STU003', subjectId: 'SUBJ001', paperId: 'PAP001', year: 2024, score: 72 }, // Math Paper 1
  { studentId: 'STU003', subjectId: 'SUBJ003', paperId: 'PAP005', year: 2024, score: 68 }, // Physical Science Paper 1
  
  // Grace Tembo - STU004
  { studentId: 'STU004', subjectId: 'SUBJ001', paperId: 'PAP001', year: 2024, score: 91 }, // Math Paper 1
  { studentId: 'STU004', subjectId: 'SUBJ004', paperId: 'PAP008', year: 2024, score: 89 }, // Biology Paper 1
  
  // James Zulu - STU005
  { studentId: 'STU005', subjectId: 'SUBJ002', paperId: 'PAP003', year: 2024, score: 48 }, // English Paper 1
  { studentId: 'STU005', subjectId: 'SUBJ003', paperId: 'PAP005', year: 2024, score: 55 }, // Physical Science Paper 1
  
  // Sarah Banda - STU006
  { studentId: 'STU006', subjectId: 'SUBJ002', paperId: 'PAP003', year: 2024, score: 78 }, // English Paper 1
  { studentId: 'STU006', subjectId: 'SUBJ001', paperId: 'PAP001', year: 2024, score: 65 }, // Math Paper 1
  
  // Michael Phiri - STU007
  { studentId: 'STU007', subjectId: 'SUBJ005', paperId: 'PAP010', year: 2024, score: 73 }, // Chemistry Paper 1
  { studentId: 'STU007', subjectId: 'SUBJ001', paperId: 'PAP001', year: 2024, score: 58 }, // Math Paper 1
  
  // Linda Mwale - STU008
  { studentId: 'STU008', subjectId: 'SUBJ003', paperId: 'PAP005', year: 2024, score: 84 }, // Physical Science Paper 1
  { studentId: 'STU008', subjectId: 'SUBJ007', paperId: 'PAP014', year: 2024, score: 79 }, // Geography Paper 1
  
  // Grace Mbewe - STU009
  { studentId: 'STU009', subjectId: 'SUBJ002', paperId: 'PAP003', year: 2024, score: 82 }, // English Paper 1
  { studentId: 'STU009', subjectId: 'SUBJ004', paperId: 'PAP008', year: 2024, score: 76 }, // Biology Paper 1
  
  // David Chirwa - STU010
  { studentId: 'STU010', subjectId: 'SUBJ006', paperId: 'PAP012', year: 2024, score: 67 }, // Physics Paper 1
  { studentId: 'STU010', subjectId: 'SUBJ001', paperId: 'PAP001', year: 2024, score: 54 }, // Math Paper 1
  
  // Historical data for 2023
  { studentId: 'STU001', subjectId: 'SUBJ002', paperId: 'PAP003', year: 2023, score: 76 }, // English Paper 1
  { studentId: 'STU003', subjectId: 'SUBJ003', paperId: 'PAP005', year: 2023, score: 68 }, // Physical Science Paper 1
  { studentId: 'STU004', subjectId: 'SUBJ004', paperId: 'PAP008', year: 2023, score: 89 }, // Biology Paper 1
  { studentId: 'STU006', subjectId: 'SUBJ001', paperId: 'PAP001', year: 2023, score: 65 }, // Math Paper 1
];

export class ResultsService {
  /**
   * Calculate grade based on score and grade boundaries
   */
  private calculateGrade(score: number, boundaries: GradeBoundaryDto[]): { grade: GradeLevel, passStatus: boolean } {
    // Sort boundaries by minScore descending to check from highest to lowest
    const sortedBoundaries = boundaries.sort((a, b) => b.minScore - a.minScore);

    for (const boundary of sortedBoundaries) {
      if (score >= boundary.minScore && score <= boundary.maxScore) {
        return {
          grade: boundary.gradeLevel as GradeLevel,
          passStatus: boundary.gradeLevel !== 'F'
        };
      }
    }
    
    // Default to F if no boundary matches
    return { grade: 'F', passStatus: false };
  }

  /**
   * Generate student results from scores and grade boundaries
   */
  async generateResults(
    gradeBoundaries: GradeBoundaryDto[],
    subjects: SubjectDto[],
    papers: PaperDto[]
  ): Promise<StudentResultDto[]> {
    try {
      await new Promise(resolve => setTimeout(resolve, 500)); // Simulate API delay
      
      const results: StudentResultDto[] = [];
      
      for (const scoreData of mockScoreData) {
        // Find grade boundaries for this subject and year
        const subjectBoundaries = gradeBoundaries.filter(
          boundary =>
            boundary.subjectId === scoreData.subjectId &&
            boundary.year === scoreData.year &&
            boundary.isActive
        );

        if (subjectBoundaries.length > 0) {
          const { grade, passStatus } = this.calculateGrade(scoreData.score, subjectBoundaries);

          results.push({
            id: `RES_${scoreData.studentId}_${scoreData.subjectId}_${scoreData.year}`,
            studentId: scoreData.studentId,
            subjectId: scoreData.subjectId,
            year: scoreData.year,
            score: scoreData.score,
            calculatedGrade: grade as string,
            passStatus: passStatus,
            dateCalculated: new Date()
          });
        }
      }
      
      return results;
    } catch (error: any) {
      throw new Error(error.message || 'Failed to generate results');
    }
  }

  /**
   * Get filtered results
   */
  async getFilteredResults(
    filters: ResultsFilterDto,
    gradeBoundaries: GradeBoundaryDto[],
    subjects: SubjectDto[],
    papers: PaperDto[]
  ): Promise<StudentResultDto[]> {
    try {
      const allResults = await this.generateResults(gradeBoundaries, subjects, papers);
      
      let filtered = allResults;
      
      // Apply search filter
      if (filters.searchQuery) {
        const query = filters.searchQuery.toLowerCase();
        filtered = filtered.filter(result => 
          result.studentId.toLowerCase().includes(query) ||
          result.calculatedGrade.toLowerCase().includes(query)
        );
      }
      
      // Apply year filter
      if (filters.year && filters.year !== 'All') {
        filtered = filtered.filter(result => result.year === filters.year);
      }
      
      // Apply subject filter
      if (filters.subjectId && filters.subjectId !== 'All') {
        filtered = filtered.filter(result => result.subjectId === filters.subjectId);
      }
      
      // Note: paperId filter removed as system is now subject-based
      
      // Apply grade filter
      if (filters.gradeLevel && filters.gradeLevel !== 'All') {
        filtered = filtered.filter(result => result.calculatedGrade === filters.gradeLevel);
      }
      
      // Apply pass status filter
      if (filters.passStatus !== undefined && filters.passStatus !== 'All') {
        filtered = filtered.filter(result => result.passStatus === filters.passStatus);
      }
      
      return filtered.sort((a, b) => {
        // Sort by year (desc), then by student ID, then by subject
        if (a.year !== b.year) return b.year - a.year;
        if (a.studentId !== b.studentId) return a.studentId.localeCompare(b.studentId);
        return a.subjectId.localeCompare(b.subjectId);
      });
    } catch (error: any) {
      throw new Error(error.message || 'Failed to get filtered results');
    }
  }

  /**
   * Get result statistics
   */
  async getResultStatistics(
    gradeBoundaries: GradeBoundaryDto[],
    subjects: SubjectDto[],
    papers: PaperDto[]
  ): Promise<{
    totalResults: number;
    passCount: number;
    failCount: number;
    gradeDistribution: { [key in GradeLevel]: number };
    passRate: number;
  }> {
    try {
      const results = await this.generateResults(gradeBoundaries, subjects, papers);
      
      const stats = {
        totalResults: results.length,
        passCount: results.filter(r => r.passStatus).length,
        failCount: results.filter(r => !r.passStatus).length,
        gradeDistribution: {
          'A': results.filter(r => r.calculatedGrade === 'A').length,
          'B': results.filter(r => r.calculatedGrade === 'B').length,
          'C': results.filter(r => r.calculatedGrade === 'C').length,
          'D': results.filter(r => r.calculatedGrade === 'D').length,
          'F': results.filter(r => r.calculatedGrade === 'F').length,
        } as { [key in GradeLevel]: number },
        passRate: 0
      };
      
      stats.passRate = stats.totalResults > 0 ? (stats.passCount / stats.totalResults) * 100 : 0;
      
      return stats;
    } catch (error: any) {
      throw new Error(error.message || 'Failed to get result statistics');
    }
  }

  /**
   * Generate comprehensive certificate data for a student for a specific exam year
   */
  async generateCertificate(
    studentId: string,
    year: number,
    gradeBoundaries: GradeBoundaryDto[],
    subjects: SubjectDto[],
    papers: PaperDto[]
  ): Promise<StudentCertificateDto> {
    try {
      await new Promise(resolve => setTimeout(resolve, 300));

      // Get all results for this student and year
      const allResults = await this.generateResults(gradeBoundaries, subjects, papers);
      const studentResults = allResults.filter(result =>
        result.studentId === studentId && result.year === year
      );

      if (studentResults.length === 0) {
        throw new Error(`No results found for student ${studentId} in year ${year}`);
      }

      // Group results by subject
      const subjectGroups = new Map<string, StudentResultDto[]>();
      studentResults.forEach(result => {
        if (!subjectGroups.has(result.subjectId)) {
          subjectGroups.set(result.subjectId, []);
        }
        subjectGroups.get(result.subjectId)!.push(result);
      });

      // Build certificate subject results
      const certificateSubjects: CertificateSubjectResult[] = [];

      for (const [subjectId, subjectResults] of subjectGroups) {
        const subject = subjects.find(s => s.id === subjectId);
        if (!subject) continue;

        // Build paper results for this subject (simplified for subject-based system)
        const paperResults: CertificatePaperResult[] = subjectResults.map(result => {
          const subject = subjects.find(s => s.id === result.subjectId);
          return {
            paperId: subject?.code || result.subjectId, // Use subject code or fallback to subjectId
            paperName: subject?.name || 'Unknown Subject',
            score: result.score,
            grade: result.calculatedGrade,
            passStatus: result.passStatus
          };
        });

        // Calculate overall subject grade (best grade among papers)
        const subjectGrades = paperResults.map(p => p.grade);
        const gradeOrder = ['A', 'B', 'C', 'D', 'F'];
        const overallGrade = subjectGrades.reduce((best, current) => {
          return gradeOrder.indexOf(current as string) < gradeOrder.indexOf(best as string) ? current : best;
        });

        const overallPassStatus = paperResults.some(p => p.passStatus);

        certificateSubjects.push({
          subjectId,
          subjectName: subject.name,
          subjectCode: subject.code,
          papers: paperResults,
          overallGrade,
          overallPassStatus
        });
      }

      // Calculate overall performance
      const totalSubjects = certificateSubjects.length;
      const passedSubjects = certificateSubjects.filter(s => s.overallPassStatus).length;
      const failedSubjects = totalSubjects - passedSubjects;
      const passRate = totalSubjects > 0 ? (passedSubjects / totalSubjects) * 100 : 0;

      // Calculate overall grade based on subject performance
      const subjectGrades = certificateSubjects.map(s => s.overallGrade);
      const gradeOrder = ['A', 'B', 'C', 'D', 'F'];
      const overallGrade = subjectGrades.length > 0
        ? subjectGrades.reduce((best, current) => {
            return gradeOrder.indexOf(current as string) < gradeOrder.indexOf(best as string) ? current : best;
          })
        : 'F';

      // Generate certificate number
      const certificateNumber = `MANEB-${year}-${studentId}-${Date.now().toString().slice(-6)}`;

      return {
        studentId,
        year,
        examSession: `${year} Main Session`,
        subjects: certificateSubjects,
        overallPerformance: {
          totalSubjects,
          passedSubjects,
          failedSubjects,
          overallGrade,
          passRate
        },
        certificateNumber,
        issueDate: new Date(),
        issuedBy: 'Malawi National Examinations Board (MANEB)'
      };
    } catch (error: any) {
      throw new Error(error.message || 'Failed to generate certificate');
    }
  }
}

// Create singleton instance
export const resultsService = new ResultsService();

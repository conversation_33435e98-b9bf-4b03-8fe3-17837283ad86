import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import { authService } from '@/services';
import { rolePermissionService } from '@/services/management/role-permission.service';
import apiClient from '@/services/core/api-client';
import { authError<PERSON>andler } from '@/services/auth/auth-error-handler.service';
import type { LoginModel, UserDto, AuthResponse, RolePermissionDto } from '@/interfaces';

export const useAuthStore = defineStore('auth', () => {
  // State
  const user = ref<UserDto | null>(null);
  const token = ref<string | null>(null);
  const refreshToken = ref<string | null>(null);
  const isLoading = ref(false);
  const error = ref<string | null>(null)
  const userPermissions = ref<RolePermissionDto[]>([])
  const dynamicRolePermissions = ref<Map<string, RolePermissionDto[]>>(new Map());

  // Getters
  const isAuthenticated = computed(() => !!token.value && !!user.value);
  const userFullName = computed(() => user.value?.fullName || '');
  const userStatus = computed(() => user.value?.status || 'Unapproved');
  const isApproved = computed(() => user.value?.status === 'Approved' || user.value?.status === 'SecondApproved');

  // Actions
  const login = async (credentials: LoginModel): Promise<void> => {
    try {
      isLoading.value = true;
      error.value = null;

      const response: AuthResponse = await authService.login(credentials);

      console.log('Auth service response:', response);
      console.log('Response user:', response.user);
      console.log('Response token:', response.token);

      // Update store state
      user.value = response.user || null;
      token.value = response.token || null;
      refreshToken.value = response.refreshToken || null;

      // Ensure API client has the token
      if (response.token) {
        apiClient.setToken(response.token);
      }

      console.log('Auth store after update:');
      console.log('- user.value:', user.value);
      console.log('- token.value:', token.value);
      console.log('- isAuthenticated:', isAuthenticated.value);

      // Load user permissions after successful login
      await loadUserPermissions();

    } catch (err: any) {
      // Clear any partial authentication state on login failure
      user.value = null;
      token.value = null;
      refreshToken.value = null;

      // Clear tokens from API client and localStorage
      apiClient.clearToken();

      error.value = err.message || 'Login failed';
      console.error('Login failed in auth store:', err);
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const logout = async (): Promise<void> => {
    try {
      isLoading.value = true;
      await authService.logout();
    } catch (err: any) {
      console.warn('Logout error:', err);
    } finally {
      // Clear store state regardless of API call success
      user.value = null;
      token.value = null;
      refreshToken.value = null;
      error.value = null;
      userPermissions.value = [];
      dynamicRolePermissions.value.clear();
      isLoading.value = false;

      // Clear token from API client
      apiClient.clearToken();
    }
  };

  // Load user permissions from API
  const loadUserPermissions = async (): Promise<void> => {
    if (!user.value?.role?.id) {
      console.log('No role ID available for permission loading');
      return;
    }

    try {
      console.log('Loading permissions for role:', user.value.role.id);
      const permissions = await rolePermissionService.getRolePermissionsByRoleId(user.value.role.id);
      userPermissions.value = permissions;
      console.log('Loaded user permissions:', permissions);
    } catch (error) {
      console.error('Failed to load user permissions:', error);
      userPermissions.value = [];
    }
  };

  // Check if user has specific permission
  const hasPermission = (sectionId: string, action: string): boolean => {
    // System Administrator has all permissions
    if (isSystemAdministrator.value) {
      return true;
    }

    // Check dynamic permissions
    const permission = userPermissions.value.find(p =>
      p.sectionID === sectionId &&
      (p.action === action || p.action === 'All') &&
      p.canAccess
    );

    return !!permission;
  };

  // Check if user has access to a module/section
  const hasModuleAccess = (sectionId: string): boolean => {
    // System Administrator has all access
    if (isSystemAdministrator.value) {
      return true;
    }

    // Check if user has any permission for this section
    const hasAccess = userPermissions.value.some(p =>
      p.sectionID === sectionId && p.canAccess
    );

    return hasAccess;
  };

  const refreshAuthToken = async (): Promise<void> => {
    try {
      const response: AuthResponse = await authService.refreshToken();

      // Update store state
      user.value = response.user || null;
      token.value = response.token || null;
      refreshToken.value = response.refreshToken || null;

      // Ensure API client has the new token
      if (response.token) {
        apiClient.setToken(response.token);
      }

    } catch (err: any) {
      // If refresh fails, handle as authentication failure using centralized handler
      console.log('Token refresh failed - clearing auth state');
      authErrorHandler.handleRefreshFailure('auth.store.refreshAuthToken', err);
      throw err;
    }
  };

  const initializeAuth = (): void => {
    // Load auth data from localStorage on app initialization
    const storedUser = authService.getCurrentUser();
    const storedToken = authService.getToken();
    const storedRefreshToken = localStorage.getItem('refresh_token');

    if (storedUser && storedToken && !authService.isTokenExpired()) {
      user.value = storedUser;
      token.value = storedToken;
      refreshToken.value = storedRefreshToken;

      // Ensure API client has the token
      apiClient.setToken(storedToken);
    } else if (storedRefreshToken) {
      // Try to refresh token if main token is expired
      refreshAuthToken().catch(() => {
        // If refresh fails, clear everything
        logout();
      });
    }
  };

  const clearError = (): void => {
    error.value = null;
  };

  // Handle authentication failures using centralized handler
  const handleAuthFailure = (errorMessage?: string) => {
    // Clear local store state
    user.value = null;
    token.value = null;
    refreshToken.value = null;
    error.value = errorMessage || 'Authentication failed';

    // Use centralized error handler for consistent behavior
    authErrorHandler.handleTokenExpired('auth.store.handleAuthFailure');
  };

  const updateUser = (updatedUser: UserDto): void => {
    user.value = updatedUser;
    localStorage.setItem('user', JSON.stringify(updatedUser));
  };

  // Check if user has specific status
  const hasStatus = (status: string): boolean => {
    return user.value?.status === status;
  };

  // Check if user can perform admin actions
  const canPerformAdminActions = computed(() => {
    return user.value?.status === 'SecondApproved';
  });

  // Check if user is an admin (Status = 2 or has admin role)
  const isAdmin = computed(() => {
    // Use hasAdminPrivileges which includes proper SYS_ADMIN detection
    return hasAdminPrivileges.value;
  });

  // Check if user is a regular student/user
  const isStudent = computed(() => {
    // Student detection logic (console logging removed)

    // OVERRIDE: Force John Doe to be treated as an admin user (not student)
    if (user.value?.fullName === 'John Doe') {
      // Override for John Doe (console logging removed)
      return false;
    }

    // System Administrator is never a student
    if (isSystemAdministrator.value) {
      console.log('❌ Not student: System Administrator');
      return false;
    }

    // Data Clerk is not a student (they use admin interface)
    if (isDataClerk.value) {
      console.log('❌ Not student: Data Clerk');
      return false;
    }

    // If user has SecondApproved status, they are admin
    if (user.value?.status === 'SecondApproved') {
      console.log('❌ Not student: SecondApproved status (admin)');
      return false;
    }

    // For MANEB system: Students are users with 'Approved' status who don't have admin roles
    // This is the primary way to identify students in the system

    const currentRole = userRole.value.toLowerCase();
    console.log('Current role (lowercase):', currentRole);

    // Check for explicit student role indicators first
    const studentRoleIndicators = [
      'student', 'learner', 'candidate', 'pupil'
    ];

    const hasStudentRole = studentRoleIndicators.some(indicator =>
      currentRole.includes(indicator)
    );

    if (hasStudentRole) {
      console.log('✅ Is student: Has explicit student role indicator');
      return true;
    }

    // Check for admin role indicators that would disqualify from being a student
    // BUT be more specific - only certain admin roles should disqualify
    const adminRoleIndicators = [
      'system_administrator', 'sysadmin', 'sys_admin',
      'administrator', 'manager', 'clerk', 'staff',
      'super', 'moderator', 'operator', 'teacher', 'instructor'
    ];

    // Note: We're being more specific here - just having "admin" in the name
    // doesn't automatically disqualify someone from being a student
    const hasAdminRole = adminRoleIndicators.some(indicator =>
      currentRole.includes(indicator)
    );

    if (hasAdminRole) {
      console.log('❌ Not student: Has specific admin role indicator:', currentRole);
      return false;
    }

    console.log('✓ Role check passed - no disqualifying admin indicators found');

    // MANEB Logic: Only users with 'Approved' status AND explicit student indicators
    // should be treated as students. All others with admin-like roles should go to admin interface.

    // Check if user has any admin privileges or roles that indicate they should use admin interface
    if (hasAdminPrivileges.value) {
      console.log('❌ Not student: Has admin privileges');
      return false;
    }

    // If user has 'Approved' status and no admin role indicators, they might be a student
    // But we need to be more careful about this assumption
    if (user.value?.status === 'Approved') {
      // Additional check: if user has any role that suggests admin access, treat as admin
      const roleData = user.value?.role?.name || user.value?.roles?.[0]?.name || '';
      const roleDataLower = roleData.toString().toLowerCase();

      // If role contains any admin-like terms, treat as admin
      if (roleDataLower.includes('admin') || roleDataLower.includes('staff') ||
          roleDataLower.includes('manager') || roleDataLower.includes('officer')) {
        console.log('❌ Not student: Role data suggests admin user:', roleData);
        return false;
      }

      console.log('✅ Is student: Has Approved status, no admin indicators, and no admin role data');
      return true;
    }

    console.log('❌ Not student: Does not meet student criteria');
    return false;
  });

  // Helper function to safely extract string value from array or string
  const extractStringValue = (value: any, propertyName: string): string | null => {
    console.log(`🔍 Extracting ${propertyName}:`, typeof value, value);

    if (Array.isArray(value)) {
      if (value.length > 0 && typeof value[0] === 'string') {
        console.log(`✅ ${propertyName} is array, using first element:`, value[0]);
        return value[0];
      } else {
        console.log(`❌ ${propertyName} is empty array or first element is not string`);
        return null;
      }
    } else if (typeof value === 'string') {
      console.log(`✅ ${propertyName} is string:`, value);
      return value;
    } else {
      console.log(`❌ ${propertyName} is neither string nor array:`, typeof value, value);
      return null;
    }
  };

  // Get user role as string - SINGLE ROLE POLICY with array/string handling
  const userRole = computed(() => {
    console.log('=== User Role Detection (Single Role Policy with Array/String Support) ===');
    console.log('User object:', user.value);
    console.log('User role object:', user.value?.role);
    console.log('User roles array:', user.value?.roles);
    console.log('User status:', user.value?.status);

    // SINGLE ROLE POLICY: Check for specific role assignment (from JWT)
    // Handle both array and string formats for role.name
    if (user.value?.role?.name) {
      const roleName = extractStringValue(user.value.role.name, 'role.name');
      if (roleName) {
        const roleNameLower = roleName.toLowerCase();
        console.log('✅ Found JWT role.name (array/string safe):', roleNameLower);
        return roleNameLower;
      }
    }

    // Check for role ID (from JWT role code)
    // Handle both array and string formats for role.id
    if (user.value?.role?.id) {
      const roleId = extractStringValue(user.value.role.id, 'role.id');
      if (roleId) {
        const roleIdLower = roleId.toLowerCase();
        console.log('✅ Found JWT role.id (array/string safe):', roleIdLower);
        return roleIdLower;
      }
    }

    // Legacy support: Check roles array but only use first role (SINGLE ROLE ENFORCEMENT)
    if (user.value?.roles && user.value.roles.length > 0 && user.value.roles[0]?.name) {
      const roleName = extractStringValue(user.value.roles[0].name, 'roles[0].name');
      if (roleName) {
        const roleNameLower = roleName.toLowerCase();
        console.log('✅ Found legacy roles[0].name (array/string safe):', roleNameLower);
        if (user.value.roles.length > 1) {
          console.warn('⚠️ SINGLE ROLE POLICY VIOLATION: User has multiple roles, using first one only');
        }
        return roleNameLower;
      }
    }

    // Fallback to status-based roles
    if (user.value?.status === 'SecondApproved') {
      console.log('✅ Using status-based role: admin');
      return 'admin';
    }
    if (user.value?.status === 'Approved') {
      console.log('✅ Using status-based role: user');
      return 'user';
    }

    console.log('❌ No role found, returning unknown');
    return 'unknown';
  });

  // Check if user has a specific role
  const hasRole = (roleName: string) => {
    const currentRole = userRole.value.toLowerCase();
    return currentRole === roleName.toLowerCase();
  };

  // Check if user is Data Clerk with array/string support
  const isDataClerk = computed(() => {
    const currentRole = userRole.value;
    console.log('=== Data Clerk Detection (Array/String Safe) ===');
    console.log('Current role:', currentRole);
    console.log('User role object:', user.value?.role);

    // Safety check: ensure currentRole is a string
    if (!currentRole || typeof currentRole !== 'string') {
      console.log('❌ No valid role found for Data Clerk check');
      return false;
    }

    const currentRoleLower = currentRole.toLowerCase();

    // Check for various Data Clerk role name formats (role names)
    const dataClerkNameVariations = [
      'data clerk',
      'dataclerk',
      'data_clerk',
      'data-clerk',
      'clerk',
      'data clerk user',
      'data entry clerk',
      'data entry'
    ];

    // Check for Data Clerk role codes (from JWT)
    const dataClerkCodeVariations = [
      'data_clerk',
      'dataclerk',
      'clerk',
      'data_entry',
      'entry_clerk'
    ];

    // Check role name variations
    const isClerkByName = dataClerkNameVariations.some(variation =>
      currentRoleLower.includes(variation.toLowerCase())
    );

    // Check role code variations (from JWT role.id) with array/string support
    let roleCode = '';
    if (user.value?.role?.id) {
      const extractedRoleId = extractStringValue(user.value.role.id, 'role.id for Data Clerk check');
      roleCode = extractedRoleId ? extractedRoleId.toLowerCase() : '';
    }

    const isClerkByCode = dataClerkCodeVariations.some(variation =>
      roleCode.includes(variation.toLowerCase())
    );

    const isClerk = isClerkByName || isClerkByCode;

    console.log('Role code (array/string safe):', roleCode);
    console.log('Is Data Clerk by name:', isClerkByName);
    console.log('Is Data Clerk by code:', isClerkByCode);
    console.log('✅ Final Data Clerk result:', isClerk);

    return isClerk;
  });

  // Check if user is System Administrator (highest level)
  const isSystemAdministrator = computed(() => {
    console.log('=== System Administrator Detection ===');

    const currentRole = userRole.value;
    console.log('Current role from userRole.value:', currentRole, 'Type:', typeof currentRole);

    if (!currentRole || typeof currentRole !== 'string') {
      console.log('❌ No valid role found for system admin check');
      return false;
    }

    const currentRoleLower = currentRole.toLowerCase();
    console.log('Current role (lowercase):', currentRoleLower);
    const systemAdminVariations = [
      'system_administrator',
      'systemadministrator',
      'sysadmin',
      'system admin',
      'sys_admin',
      'sys admin',
      'sys_admin'
    ];

    const isSystemAdmin = systemAdminVariations.some(variation =>
      currentRoleLower.includes(variation.toLowerCase())
    );

    // Also check role data for system admin indicators
    let hasSystemAdminRoleData = false;
    if (user.value?.role?.name) {
      const roleName = extractStringValue(user.value.role.name, 'role.name for system admin check');
      if (roleName) {
        const roleNameLower = roleName.toLowerCase();
        hasSystemAdminRoleData = systemAdminVariations.some(variation =>
          roleNameLower.includes(variation.toLowerCase())
        );
      }
    }

    const result = isSystemAdmin || hasSystemAdminRoleData;
    console.log(`System Administrator check: ${result ? 'YES' : 'NO'}`);
    return result;
  });

  // Check if user has full admin privileges (NOT Data Clerk) with array/string support
  const hasAdminPrivileges = computed(() => {
    console.log('=== Admin Privileges Detection (Array/String Safe) ===');

    // System Administrator has the highest privileges
    if (isSystemAdministrator.value) {
      console.log('User is System Administrator - full admin privileges granted');
      return true;
    }

    // Data Clerk should NOT have admin privileges for navigation purposes
    if (isDataClerk.value) {
      console.log('User is Data Clerk - no admin privileges for navigation');
      return false;
    }

    // Check if user has actual admin role (not just status)
    const currentRole = userRole.value;
    if (!currentRole || typeof currentRole !== 'string') {
      console.log('No valid role for admin privileges check');
      return false;
    }

    const currentRoleLower = currentRole.toLowerCase();
    const hasAdminRole = currentRoleLower.includes('admin') || currentRoleLower === 'administrator';
    const hasAdminStatus = user.value?.status === 'SecondApproved';

    // Additional check: Look at raw role data for admin indicators (array/string safe)
    let hasAdminRoleData = false;
    if (user.value?.role?.name) {
      const roleName = extractStringValue(user.value.role.name, 'role.name for admin check');
      if (roleName) {
        const roleNameLower = roleName.toLowerCase();
        hasAdminRoleData = roleNameLower.includes('admin') || roleNameLower === 'administrator';
      }
    }

    if (user.value?.role?.id && !hasAdminRoleData) {
      const roleId = extractStringValue(user.value.role.id, 'role.id for admin check');
      if (roleId) {
        const roleIdLower = roleId.toLowerCase();
        hasAdminRoleData = roleIdLower.includes('admin') || roleIdLower === 'administrator';
      }
    }

    const finalAdminResult = hasAdminRole || hasAdminStatus || hasAdminRoleData;

    console.log('hasAdminPrivileges - Role:', currentRoleLower);
    console.log('hasAdminPrivileges - HasAdminRole:', hasAdminRole);
    console.log('hasAdminPrivileges - HasAdminStatus:', hasAdminStatus);
    console.log('hasAdminPrivileges - HasAdminRoleData:', hasAdminRoleData);
    console.log('✅ Final Admin Privileges result:', finalAdminResult);

    return finalAdminResult;
  });

  // Get appropriate dashboard route based on user role
  const getDashboardRoute = computed(() => {
    console.log('=== Dashboard Route Selection ===');
    console.log('User status:', user.value?.status);
    console.log('isStudent:', isStudent.value);
    console.log('isSystemAdministrator:', isSystemAdministrator.value);
    console.log('hasAdminPrivileges:', hasAdminPrivileges.value);

    // Students go to student dashboard
    if (isStudent.value) {
      console.log('✅ Routing to student dashboard');
      return '/student/dashboard';
    }

    // Everyone else (including SYS_ADMIN, Data Clerk, Admin, etc.) goes to admin dashboard
    console.log('✅ Routing to admin dashboard (non-student user)');
    return '/admin/dashboard';
  });

  // Get allowed navigation items based on role
  const getAllowedNavigation = computed(() => {
    console.log('=== Navigation Access Check ===');
    console.log('User role:', userRole.value);
    console.log('isSystemAdministrator:', isSystemAdministrator.value);
    console.log('isDataClerk:', isDataClerk.value);
    console.log('hasAdminPrivileges:', hasAdminPrivileges.value);
    console.log('isAdmin:', isAdmin.value);

    // HIGHEST PRIORITY: System Administrator gets everything
    if (isSystemAdministrator.value) {
      console.log('✅ User is System Administrator - Returning ALL navigation modules with full access');
      return ['dashboard', 'score-entry', 'grading-system', 'results', 'user-management', 'reports', 'audit-logs', 'system-settings'];
    }

    // IMPORTANT: Check Data Clerk FIRST before admin privileges
    if (isDataClerk.value) {
      // Data Clerk only sees Dashboard and Score Entry
      console.log('✅ User is Data Clerk - Returning limited navigation: dashboard, score-entry');
      return ['dashboard', 'score-entry'];
    }

    // For dynamic roles, check permissions
    if (userPermissions.value.length > 0) {
      const allowedModules = new Set<string>();

      // Map section IDs to navigation modules
      const sectionToModuleMap: Record<string, string> = {
        'dashboard': 'dashboard',
        'score_entry': 'score-entry',
        'grading': 'grading-system',
        'results_management': 'results',
        'user_management': 'user-management',
        'reports': 'reports',
        'audit_logs': 'audit-logs',
        'system_settings': 'system-settings'
      };

      userPermissions.value.forEach(permission => {
        if (permission.canAccess) {
          const moduleKey = sectionToModuleMap[permission.sectionID] || permission.sectionID;
          allowedModules.add(moduleKey);
        }
      });

      if (allowedModules.size > 0) {
        const modules = Array.from(allowedModules);
        console.log('✅ User has dynamic role permissions - Returning modules:', modules);
        return modules;
      }
    }

    if (hasAdminPrivileges.value) {
      // Standard admin access
      console.log('✅ User has admin privileges - Returning standard admin navigation');
      return ['dashboard', 'score-entry', 'grading-system', 'results', 'user-management'];
    }

    // Students get their own navigation (handled separately)
    console.log('❌ User has no special privileges - Returning empty navigation (student/unknown)');
    return [];
  });

  return {
    // State
    user,
    token,
    refreshToken,
    isLoading,
    error,

    // Getters
    isAuthenticated,
    userFullName,
    userStatus,
    isApproved,
    canPerformAdminActions,
    isAdmin,
    isStudent,
    userRole,
    getDashboardRoute,
    getAllowedNavigation,
    isDataClerk,
    hasAdminPrivileges,
    isSystemAdministrator,

    // Actions
    login,
    logout,
    refreshAuthToken,
    initializeAuth,
    clearError,
    handleAuthFailure,
    updateUser,
    hasStatus,
    hasRole,
    loadUserPermissions,
    hasPermission,
    hasModuleAccess,

    // Dynamic permissions state
    userPermissions,
    dynamicRolePermissions,
  };
});

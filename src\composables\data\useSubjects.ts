/**
 * @fileoverview Composable for managing subjects from API
 * @description Provides reactive subjects data and searching functionality
 */

import { ref, computed, onMounted } from 'vue'
import { subjectService, type SubjectDto, type SubjectOption, type SubjectFilters } from '@/services/data/subject.service'

/**
 * Composable for subjects management
 */
export function useSubjects() {
  // Reactive state
  const subjects = ref<SubjectDto[]>([])
  const isLoading = ref(false)
  const error = ref<string | null>(null)
  const searchQuery = ref('')
  const filters = ref<SubjectFilters>({})

  /**
   * Load subjects from API
   */
  const loadSubjects = async (forceRefresh: boolean = false) => {
    isLoading.value = true
    error.value = null

    try {
      const response = await subjectService.fetchSubjects({ forceRefresh })
      subjects.value = response.items
      console.log('✅ useSubjects: Subjects loaded from API:', subjects.value.length, 'subjects')
    } catch (err) {
      error.value = 'Failed to load subjects'
      console.error('❌ useSubjects: Error loading subjects from API:', err)
      subjects.value = []
    } finally {
      isLoading.value = false
    }
  }

  /**
   * Get active subjects only
   */
  const activeSubjects = computed(() => 
    subjects.value.filter(subject => subject.isActive)
  )

  /**
   * Get filtered subjects based on current filters and search
   */
  const filteredSubjects = computed(() => {
    let result = filters.value.activeOnly !== false ? activeSubjects.value : subjects.value

    // Apply exam type filter
    if (filters.value.examTypeId) {
      result = result.filter(subject => subject.examTypeId === filters.value.examTypeId)
    }

    // Apply search query
    if (searchQuery.value) {
      const query = searchQuery.value.toLowerCase()
      result = result.filter(subject =>
        subject.name.toLowerCase().includes(query) ||
        subject.shortName.toLowerCase().includes(query) ||
        subject.id.toLowerCase().includes(query)
      )
    }

    return result
  })

  /**
   * Get subjects as dropdown options
   */
  const subjectOptions = computed((): SubjectOption[] => 
    filteredSubjects.value.map(subject => ({
      id: subject.id,
      name: subject.name,
      shortName: subject.shortName,
      examTypeId: subject.examTypeId,
      examTypeName: subject.examType?.name || subject.examTypeId,
      isActive: subject.isActive
    }))
  )

  /**
   * Get unique exam types from subjects
   */
  const examTypes = computed(() => {
    const examTypesMap = new Map<string, string>()
    
    activeSubjects.value.forEach(subject => {
      if (subject.examType) {
        examTypesMap.set(subject.examType.id, subject.examType.name)
      }
    })
    
    return Array.from(examTypesMap.entries()).map(([id, name]) => ({ id, name }))
  })

  /**
   * Search subjects
   */
  const searchSubjects = (query: string) => {
    searchQuery.value = query
  }

  /**
   * Set filters
   */
  const setFilters = (newFilters: Partial<SubjectFilters>) => {
    filters.value = { ...filters.value, ...newFilters }
  }

  /**
   * Clear filters
   */
  const clearFilters = () => {
    filters.value = {}
    searchQuery.value = ''
  }

  /**
   * Get subject by ID
   */
  const getSubjectById = (id: string): SubjectDto | undefined => {
    return subjects.value.find(subject => subject.id === id)
  }

  /**
   * Get subjects by exam type
   */
  const getSubjectsByExamType = (examTypeId: string): SubjectDto[] => {
    return activeSubjects.value.filter(subject => subject.examTypeId === examTypeId)
  }

  /**
   * Get subject name by ID
   */
  const getSubjectName = (id: string): string => {
    const subject = getSubjectById(id)
    return subject?.name || id
  }

  /**
   * Refresh subjects data
   */
  const refresh = () => loadSubjects(true)

  /**
   * Clear error state
   */
  const clearError = () => {
    error.value = null
  }

  return {
    // State
    subjects,
    activeSubjects,
    filteredSubjects,
    subjectOptions,
    examTypes,
    isLoading,
    error,
    searchQuery,
    filters,
    
    // Methods
    loadSubjects,
    searchSubjects,
    setFilters,
    clearFilters,
    getSubjectById,
    getSubjectsByExamType,
    getSubjectName,
    refresh,
    clearError
  }
}

/**
 * Composable that auto-loads subjects on mount
 */
export function useSubjectsAutoLoad() {
  console.log('🚀 useSubjectsAutoLoad called');
  const subjectsComposable = useSubjects()

  onMounted(async () => {
    console.log('🔄 useSubjectsAutoLoad: onMounted triggered, calling loadSubjects');
    await subjectsComposable.loadSubjects()
  })

  return subjectsComposable
}

/**
 * Composable for searchable subject dropdown
 */
export function useSearchableSubjects(initialFilters?: SubjectFilters) {
  const subjectsComposable = useSubjectsAutoLoad()
  
  // Apply initial filters if provided
  if (initialFilters) {
    subjectsComposable.setFilters(initialFilters)
  }
  
  return {
    ...subjectsComposable,
    // Additional methods for searchable dropdown
    searchOptions: subjectsComposable.subjectOptions,
    search: subjectsComposable.searchSubjects,
    isSearching: subjectsComposable.isLoading
  }
}

/**
 * Composable for exam-level filtered subjects
 */
export function useSubjectsByExamLevel(examTypeId: string) {
  const subjectsComposable = useSubjectsAutoLoad()
  
  // Set exam type filter
  subjectsComposable.setFilters({ examTypeId, activeOnly: true })
  
  return {
    ...subjectsComposable,
    examLevelSubjects: subjectsComposable.filteredSubjects
  }
}

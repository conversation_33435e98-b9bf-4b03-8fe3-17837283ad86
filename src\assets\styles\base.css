@import 'tailwindcss';

/* MANEB Official Color Scheme */
:root {
  --maneb-red: #a12c2c;
  --maneb-red-dark: #8b2424;
  --maneb-red-light: #b83434;
  --maneb-gold: #D97706;
  --maneb-gold-light: #F59E0B;
  --maneb-black: #1F2937;
  --maneb-white: #FFFFFF;
}

/* Global MANEB color utilities */
.bg-maneb-primary {
  background-color: var(--maneb-red);
}

.bg-maneb-primary-dark {
  background-color: var(--maneb-red-dark);
}

.bg-maneb-secondary {
  background-color: var(--maneb-gold);
}

.text-maneb-primary {
  color: var(--maneb-red);
}

.text-maneb-secondary {
  color: var(--maneb-gold);
}

.border-maneb-primary {
  border-color: var(--maneb-red);
}

.border-maneb-secondary {
  border-color: var(--maneb-gold);
}

/* Additional MANEB-specific overrides for Flowbite components */
.bg-primary-600 {
  background-color: var(--maneb-red) !important;
}

.bg-primary-700 {
  background-color: var(--maneb-red-dark) !important;
}

.text-primary-600 {
  color: var(--maneb-red) !important;
}

.border-primary-600 {
  border-color: var(--maneb-red) !important;
}

.focus\:ring-primary-300:focus {
  --tw-ring-color: rgba(161, 44, 44, 0.3) !important;
}

.hover\:bg-primary-700:hover {
  background-color: var(--maneb-red-dark) !important;
}

/* DISABLE ALL DARK MODE STYLING - FORCE LIGHT MODE ONLY */
/* This ensures all dark: classes are overridden to maintain light mode appearance */

/* Force light mode for all elements */
html {
  color-scheme: light !important;
}

/* Override all dark mode background colors */
[class*="dark:bg-gray-700"],
[class*="dark:bg-gray-800"],
[class*="dark:bg-gray-900"] {
  background-color: white !important;
}

[class*="dark:bg-red-900"] {
  background-color: #fef2f2 !important;
}

[class*="dark:bg-blue-900"] {
  background-color: #dbeafe !important;
}

[class*="dark:bg-green-900"] {
  background-color: #dcfce7 !important;
}

[class*="dark:bg-yellow-900"] {
  background-color: #fefce8 !important;
}

/* Override all dark mode text colors */
[class*="dark:text-white"],
[class*="dark:text-gray-300"],
[class*="dark:text-gray-400"] {
  color: #111827 !important;
}

[class*="dark:text-red-300"],
[class*="dark:text-red-400"] {
  color: #dc2626 !important;
}

[class*="dark:text-blue-300"],
[class*="dark:text-blue-400"] {
  color: #2563eb !important;
}

[class*="dark:text-green-300"],
[class*="dark:text-green-400"] {
  color: #16a34a !important;
}

[class*="dark:text-yellow-300"],
[class*="dark:text-yellow-400"] {
  color: #ca8a04 !important;
}

/* Override all dark mode border colors */
[class*="dark:border-gray-600"],
[class*="dark:border-gray-700"],
[class*="dark:border-gray-800"] {
  border-color: #d1d5db !important;
}

/* Override all dark mode hover states */
[class*="dark:hover:bg-gray-600"],
[class*="dark:hover:bg-gray-700"],
[class*="dark:hover:bg-gray-800"] {
  background-color: #f3f4f6 !important;
}

[class*="dark:hover:text-white"],
[class*="dark:hover:text-gray-300"] {
  color: #111827 !important;
}

/* Override all dark mode focus states */
[class*="dark:focus:ring-gray-600"],
[class*="dark:focus:ring-gray-700"],
[class*="dark:focus:ring-gray-800"] {
  --tw-ring-color: rgba(156, 163, 175, 0.5) !important;
}

/* Override all dark mode placeholder colors */
[class*="dark:placeholder-gray-400"] {
  color: #9ca3af !important;
}

/* Ensure all components use light mode styling */
.dark\:bg-gray-700,
.dark\:bg-gray-800,
.dark\:bg-gray-900,
.dark\:text-white,
.dark\:text-gray-300,
.dark\:text-gray-400,
.dark\:border-gray-600,
.dark\:border-gray-700,
.dark\:hover\:bg-gray-600,
.dark\:hover\:bg-gray-700,
.dark\:hover\:text-white {
  /* Reset to light mode equivalents */
  background-color: white !important;
  color: #111827 !important;
  border-color: #d1d5db !important;
}
